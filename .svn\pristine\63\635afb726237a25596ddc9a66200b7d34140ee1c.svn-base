<template>
  <section class="dybzx-page">
    <v-title name="待已办中心"></v-title>
    <div class="zhxy-form zhxy-form-search-part" ref="element">
      <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
        <el-form-item>
          <span class="zhxy-form-label" slot="label">系统名称</span>
          <el-input class="zhxy-form-inline" v-model="listQuery.systemName" size="small"></el-input>
          <el-button type="primary" @click="search()" size="small">查询
          </el-button>
          <el-button type="" @click="reset()" size="small">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-loading="contentLoading" class="dybzx-page-content">
      <div class="button-list">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="showDialog">新增</el-button>
      </div>
      <template v-for="item in result">
        <div class="dybzx-page-content-box" :key="item.id">
          <div class="dybzx-page-content-card">
            <div class="card-top">
              <i class="card-top-icon"></i>
              <p class="card-top-title">{{ item.title }}</p>
            </div>
            <div class="card-middle">
              <div class="card-middle-item">
                <div class="card-middle-item-label">系统标识:</div>
                <div :title="item.XTBS" class="card-middle-item-value">{{ item.XTBS }}
                </div>
              </div>
              <div class="card-middle-item">
                <div class="card-middle-item-label">负责部门:</div>
                <div :title="item.BMMC" class="card-middle-item-value">{{ item.BMMC }}</div>
              </div>
              <div class="card-middle-item">
                <div class="card-middle-item-label">负责人:</div>
                <div :title="item.XM" class="card-middle-item-value">{{ item.XM }}</div>
              </div>
            </div>
            <div class="card-bottom">
              <div class="card-bottom-left card-bottom-btn" @click="edit()">
                <i class="el-icon-edit"></i>
                <p>编辑</p>
              </div>
              <div class="card-bottom-right card-bottom-btn" @click="delForm(item)">
                <i class="el-icon-delete"></i>
                <p>删除</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!--新增修改弹窗-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="系统标识" :label-width="formLabelWidth" prop="XTBS" >
          <el-input size="small" v-model="form.XTBS"  placeholder="必填"></el-input>
        </el-form-item>
        <el-form-item label="系统名称" :label-width="formLabelWidth" prop="XTMC" >
          <el-input size="small" v-model="form.XTMC"  placeholder="必填"></el-input>
        </el-form-item>
        <el-form-item label="负责部门" :label-width="formLabelWidth" prop="BMMC">
          <el-select v-model="form.BMMC" placeholder="请选择" >
            <el-option v-for="item in options" :key="item.BMM" :label="item.BMMC" :value="item.BMM"></el-option>
          </el-select>
<!--          <el-input size="small" v-model="form.BMMC"  autocomplete="off"></el-input>-->
        </el-form-item>
        <el-form-item label="联系人" :label-width="formLabelWidth" >
          <el-input size="small" v-model="form.XM"   autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" :label-width="formLabelWidth">
          <el-input size="small" v-model="form.DH"  autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="排序号" :label-width="formLabelWidth" >
          <el-input size="small" v-model="form.PXH"  autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth">
          <el-input size="small" v-model="form.BZ"   autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
        <el-button :loading="formLoading" size="small" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import VTitle from '@/components/title/VTitle.vue';
import { deletePrompt } from '@/utils/action-utils.js';

export default {
  components: {
    VTitle
  },
  data() {
    const validatetitle = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入系统标识'));
      }
      return callback();
    };
    return {
      // 新增dialog loading
      formLoading: false,
      // dialog title
      dialogTitle: '新增接入系统',
      // 列表内容loading
      contentLoading: false,
      dialogTableVisible: false,
      dialogFormVisible: false,
      form: {
        XTBS: '',
        BMM: '',
        XM: '',
        DH: '',
        XTMC: '',
        PXH: '',
        BZ: '',
        BMMC: ''
      },
      options: [
        {
          BMM: '000000',
          BMMC: '全部'
        },
        {
          BMM: '000001',
          BMMC: '航天学院'
        },
        {
          BMM: '000002',
          BMMC: '机电工程学院'
        },
        {
          BMM: '000003',
          BMMC: '材料科学与工程学院'
        },
        {
          BMM: '000004',
          BMMC: '能源科学与工程学院'
        },
        {
          BMM: '000005',
          BMMC: '电气工程及自动化学院'
        },
        {
          BMM: '000006',
          BMMC: '理学院'
        },
        {
          BMM: '000007',
          BMMC: '经济与管理学院'
        },
        {
          BMM: '000008',
          BMMC: '人文社科与法学学院'
        },
        {
          BMM: '000009',
          BMMC: '土木工程学院'
        },
        {
          BMM: '000010',
          BMMC: '市政环境工程学院'
        },
        {
          BMM: '000011',
          BMMC: '建筑学院'
        },
        {
          BMM: '000012',
          BMMC: '交通科学与工程学院'
        },
        {
          BMM: '000013',
          BMMC: '计算机科学与技术学院/国家示范性软件学院'
        },
        {
          BMM: '000015',
          BMMC: '外国语学院'
        },
        {
          BMM: '000016',
          BMMC: '仪器科学与工程学院'
        },
        {
          BMM: '000017',
          BMMC: '体育部'
        },
        {
          BMM: '000018',
          BMMC: '医学与健康学院'
        },
        {
          BMM: '000020',
          BMMC: '数学学院'
        },
        {
          BMM: '000021',
          BMMC: '物理学院'
        },
        {
          BMM: '000028',
          BMMC: '生命科学与技术学院'
        },
        {
          BMM: '000031',
          BMMC: '化工与化学学院'
        },
        {
          BMM: '000032',
          BMMC: '电子与信息工程学院'
        },
        {
          BMM: '000034',
          BMMC: '马克思主义学院'
        },
        {
          BMM: '000035',
          BMMC: '交叉科学研究中心'
        },
        {
          BMM: '000036',
          BMMC: '空间环境与物质科学研究院'
        },
        {
          BMM: '000040',
          BMMC: '学校办公室'
        },
        {
          BMM: '000041',
          BMMC: '本科生院'
        },
        {
          BMM: '000042',
          BMMC: '研究生院'
        },
        {
          BMM: '000044',
          BMMC: '科学与工业技术研究院'
        },
        {
          BMM: '000045',
          BMMC: '学生工作部（处）/团委'
        },
        {
          BMM: '000048',
          BMMC: '人事处'
        },
        {
          BMM: '000050',
          BMMC: '计划财务处'
        },
        {
          BMM: '000051',
          BMMC: '纪委办公室/监察处'
        },
        {
          BMM: '000052',
          BMMC: '审计处'
        },
        {
          BMM: '000053',
          BMMC: '国有资产管理处'
        },
        {
          BMM: '000055',
          BMMC: '基建处'
        },
        {
          BMM: '000056',
          BMMC: '保卫部（处）'
        },
        {
          BMM: '000057',
          BMMC: '保密处'
        },
        {
          BMM: '000058',
          BMMC: '离退休工作处'
        },
        {
          BMM: '000059',
          BMMC: '工会办公室'
        },
        {
          BMM: '000061',
          BMMC: '党委组织部（党校、机关党委）'
        },
        {
          BMM: '000063',
          BMMC: '党委宣传部'
        },
        {
          BMM: '000064',
          BMMC: '党委统战部'
        },
        {
          BMM: '000065',
          BMMC: '产业工作委员会'
        },
        {
          BMM: '000066',
          BMMC: '校部机关党委'
        },
        {
          BMM: '000067',
          BMMC: '国际合作部'
        },
        {
          BMM: '000069',
          BMMC: '网络安全和信息化办公室'
        },
        {
          BMM: '000071',
          BMMC: '继续教育学院'
        },
        {
          BMM: '000072',
          BMMC: '《哈尔滨工业大学学报》编辑部'
        },
        {
          BMM: '000073',
          BMMC: '图书馆'
        },
        {
          BMM: '000074',
          BMMC: '档案馆'
        },
        {
          BMM: '000075',
          BMMC: '校友工作办公室/教育发展基金会秘书处'
        },
        {
          BMM: '000078',
          BMMC: '网络与信息中心'
        },
        {
          BMM: '000079',
          BMMC: '博物馆'
        },
        {
          BMM: '000080',
          BMMC: '高等教育研究所/发展战略研究中心'
        },
        {
          BMM: '000081',
          BMMC: '校医院'
        },
        {
          BMM: '000082',
          BMMC: '总务处/后勤集团'
        },
        {
          BMM: '000090',
          BMMC: '资产投资经营有限责任公司'
        },
        {
          BMM: '000127',
          BMMC: '环境学院'
        },
        {
          BMM: '000128',
          BMMC: '生命科学中心'
        },
        {
          BMM: '000129',
          BMMC: '生物信息技术研究院'
        },
        {
          BMM: '000131',
          BMMC: '军民融合创新研究院'
        },
        {
          BMM: '000133',
          BMMC: '实验室与设备管理处'
        },
        {
          BMM: '000209',
          BMMC: '数学研究院'
        },
        {
          BMM: '000332',
          BMMC: '分析测试中心'
        },
        {
          BMM: '000858',
          BMMC: '校庆筹备工作办公室'
        }],
      rules: {
        XTBS: [
          {
            required: true,
            trigger: 'blur',
            validator: validatetitle
          },
          {
            max: 200,
            message: '最多输入200个字符',
            trigger: 'blur'
          }
        ],
        XTMC: [
          {
            required: true,
            message: '请填写系统名称',
            trigger: 'blur'
          }
        ],
        BMMC: [
          {
            required: true,
            message: '请填写负责部门',
            trigger: 'change'
          }
        ],
        XM: [
          {
            required: false,
            message: '请填写联系人',
            trigger: 'blur'
          }
        ],
        DH: [
          {
            required: false,
            message: '请填写联系电话',
            trigger: 'blur'
          }
        ],
        PXH: [
          {
            required: false,
            message: '请填写排序号',
            trigger: 'blur'
          }
        ],
        BZ: [
          {
            required: false,
            message: '请填写备注',
            trigger: 'blur'
          }
        ]
      },
      formLabelWidth: '120px',
      // label 宽度
      lableWidth: '120px',
      // search params
      listQuery: {
        systemName: ''
      },
      // mock 请求数据
      result: []
    };
  },
  created() {
    this.contentLoading = true;
    setTimeout(() => {
      this.result = [
        {
          title: '网上服务中心',
          ID: '1',
          BMMC: '网络与信息中心',
          XM: '系统管理员',
          XTBS: 'XAKLFGDKKS',
          XTMC: '企业微信',
          MSG: ''
        },
        {
          title: '网上服务中心',
          ID: '2',
          BMMC: '网络与信息中心',
          XM: '系统管理员',
          XTBS: 'XAKLFGDKKS',
          XTMC: '企业微信',
          MSG: ''
        },
        {
          title: '网上服务中心',
          id: '3',
          BMMC: '网络与信息中心',
          XM: '系统管理员',
          XTBS: 'XAKLFGDKKS',
          XTMC: '企业微信',
          MSG: ''
        },
        {
          title: '网上服务中心',
          id: '4',
          BMMC: '网络与信息中心',
          XM: '系统管理员',
          XTBS: 'XAKLFGDKKS',
          XTMC: '企业微信',
          MSG: ''
        },
        {
          title: '网上服务中心',
          id: '5',
          BMMC: '网络与信息中心',
          XM: '系统管理员',
          XTBS: 'XAKLFGDKKS',
          XTMC: '企业微信',
          MSG: ''
        },
        {
          title: '网上服务中心',
          id: '6',
          BMMC: '网络与信息中心',
          XM: '系统管理员',
          XTBS: 'XAKLFGDKKS',
          XTMC: '企业微信',
          MSG: ''
        }
      ];
      this.contentLoading = false;
    }, 1000);
  },
  mounted() {

  },
  methods: {
    /**
     * 搜索查询
     */
    search() {
      const params = {};
      console.log(params);
    },
    /**
     * 重置搜索条件
     */
    reset() {
      this.listQuery.systemName = '';
    },
    delForm(item) {
      console.log('0000');
      // 设置参数
      const config = {
        text: '是否删除?',
        btnText: '执行中...',
        title: '',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        const index = this.result.findIndex((x) => x.id === item.id);
        if (index > -1) {
          this.result.splice(index, 1);
          this.$message.success('删除');
        }

        successAction();
        // 删除接口
        // axios.post('/api',val).then(()=>{
        //   // 成功操作...
        // }).catch(()=>{
        //
        // }).finally(()=>{
        //   // 取消加载状态
        //   successAction();
        // })
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    /**
     * 显示新增弹窗
     */
    showDialog() {
      this.status = 0;
      this.dialogTitle = '新增接入系统';
      // 展示弹窗
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = this.form;
          console.log(params);
          this.formLoading = true;
          if (!this.status) {
            // 添加事件接口
            setTimeout(() => {
              // 模拟接口成功事件
              this.$message.success('添加成功');
              this.formLoading = false;
              this.dialogFormVisible = false;
            }, 2000);
          }
          // 调用接口 传递数据
          return true;
        }
        return false;
      });
    },
    edit() {
      this.$router.push('/jksqgl');
    }
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page {
  padding: $page-content-padding;
  background-color: #FFFFFF;

  .dybzx-page-content {
    min-height: 300px;
    display: flex;
    flex-wrap: wrap;

    .button-list {
      width: 100%;
      display: flex;
      flex-direction: row-reverse;
      padding-right: 2%;
      height: 32px;
    }

    .dybzx-page-content-box {
      width: 25%;
      padding: 0 2%;
    }

    .dybzx-page-content-card {
      background-color: #FFFFFF;
      width: 100%;
      height: 228px;
      border: 1px solid #EAEAEA;
      box-shadow: #ededed 0px 0px 5px 1px;
      position: relative;
      overflow: hidden;
      margin-top: 20px;

      .card-tip {
        position: absolute;
        top: 10px;
        right: -30px;
        width: 120px;
        background-color: #286fb7;
        color: #FFFFFF;
        font-size: 16px;
        padding: 7px 0;
        text-align: center;
        transform: rotate(45deg);
      }

      .card-top {
        display: flex;
        align-items: center;
        background-color: #F2F2F2;
        height: 52px;
        padding: 0 10px;

        .card-top-icon {
          width: 26px;
          height: 26px;
          background: url("../../assets/icon-xitong.png") no-repeat;
          background-size: cover;
        }

        .card-top-title {
          font-size: 16px;
          color: #2d8cf0;
          padding-left: 10px;
        }

      }

      .card-middle {
        padding: 20px 30px;

        .card-middle-item {
          display: flex;
          line-height: 30px;

          .card-middle-item-label {
            width: 80px;
            flex-shrink: 0;
            color: #90a4af;
          }

          .card-middle-item-value {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

      }

      .card-bottom {
        display: flex;
        height: 45px;

        .card-bottom-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 50%;
          border-top: 1px solid #EAEAEA;
          cursor: pointer;
        }

        .card-bottom-btn:hover {
          opacity: 0.8;
        }

        .card-bottom-left {
          border-right: 1px solid #EAEAEA;
          color: #286fb7;
        }

        .card-bottom-right {
          color: #90a4af;
        }
      }
    }
  }
}
</style>
