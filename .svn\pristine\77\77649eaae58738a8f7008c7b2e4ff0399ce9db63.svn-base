<template>
  <div class="bmgl-content">
    <!--  新增根部门  -->
    <el-dialog
      title="新增部门"
      :visible.sync="bmForm.visible"
      width="400px">
      <div class="department-user">
        <el-form label-width="150px" :model="bmForm">
          <el-form-item>
            <span class="zhxy-form-label" slot="label">部门码</span>
            <el-input class="zhxy-form-inline" v-model="bmForm.bmm" placeholder="部门代码"
                      size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <span slot="label" class="zhxy-form-label">部门名称</span>
            <el-input class="zhxy-form-inline" v-model="bmForm.bmmc" placeholder="部门名称"
                      size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <span class="zhxy-form-label" slot="label">是否二级部门</span>
            <el-select class="zhxy-form-inline" v-model="bmForm.sfejbm" placeholder="是否二级部门"
                       size="small">
              <el-option
                v-for="item in partOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <span class="zhxy-form-label" slot="label">是否为保密行政管理单位</span>
            <el-select class="zhxy-form-inline" v-model="bmForm.sfwbmxzgldw" placeholder="是否为保密行政管理单位"
                       size="small">
              <el-option
                v-for="item in isBmOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <span class="zhxy-form-label" slot="label">排序号</span>
            <el-input class="zhxy-form-inline" v-model="bmForm.pxh" placeholder="排序号"
                      size="small"></el-input>
          </el-form-item>

<!--          <el-form-item>-->
<!--            <span class="zhxy-form-label" slot="label">人事机构码</span>-->
<!--            <el-input class="zhxy-form-inline" v-model="bmForm.rsjgm" placeholder="人事机构码"-->
<!--                      size="small"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <span class="zhxy-form-label" slot="label">本科生学院码</span>-->
<!--            <el-input class="zhxy-form-inline" v-model="bmForm.bksxym" placeholder="本科生学院码"-->
<!--                      size="small"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <span class="zhxy-form-label" slot="label">研究生学院码</span>-->
<!--            <el-input class="zhxy-form-inline" v-model="bmForm.yjsxym" placeholder="研究生学院码"-->
<!--                      size="small"></el-input>-->
<!--          </el-form-item>-->
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="addRootFile">确 定</el-button>
        <el-button size="small" @click="concleaddRootFile">取 消</el-button>
      </span>
    </el-dialog>

    <!--  新增下属部门  -->
    <el-dialog
      title="新增下属部门"
      :visible.sync="xsbmForm.visible"
      width="400px">
      <div class="department-user">
        <el-form label-width="150px" :model="xsbmForm">
          <el-form-item>
            <span class="zhxy-form-label" slot="label">部门码</span>
            <el-input class="zhxy-form-inline" v-model="xsbmForm.bmm" placeholder="部门代码"
                      size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <span slot="label" class="zhxy-form-label">部门名称</span>
            <el-input class="zhxy-form-inline" v-model="xsbmForm.bmmc" placeholder="部门名称"
                      size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <span class="zhxy-form-label" slot="label">是否二级部门</span>
            <el-select class="zhxy-form-inline" v-model="xsbmForm.sfejbm" placeholder="是否二级部门"
                       size="small">
              <el-option
                v-for="item in partOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <span class="zhxy-form-label" slot="label">是否为保密行政管理单位</span>
            <el-select class="zhxy-form-inline" v-model="xsbmForm.sfwbmxzgldw" placeholder="是否为保密行政管理单位"
                       size="small">
              <el-option
                v-for="item in isBmOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <span class="zhxy-form-label" slot="label">排序号</span>
            <el-input class="zhxy-form-inline" v-model="xsbmForm.pxh" placeholder="排序号"
                      size="small"></el-input>
          </el-form-item>

<!--          <el-form-item>-->
<!--            <span class="zhxy-form-label" slot="label">人事机构码</span>-->
<!--            <el-input class="zhxy-form-inline" v-model="xsbmForm.rsjgm" placeholder="人事机构码"-->
<!--                      size="small"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <span class="zhxy-form-label" slot="label">本科生学院码</span>-->
<!--            <el-input class="zhxy-form-inline" v-model="xsbmForm.bksxym" placeholder="本科生学院码"-->
<!--                      size="small"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <span class="zhxy-form-label" slot="label">研究生学院码</span>-->
<!--            <el-input class="zhxy-form-inline" v-model="xsbmForm.yjsxym" placeholder="研究生学院码"-->
<!--                      size="small"></el-input>-->
<!--          </el-form-item>-->
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="addXsbmFile">确 定</el-button>
        <el-button size="small" @click="concleaddXsbmFile">取 消</el-button>
      </span>
    </el-dialog>

    <!--  正文  -->
    <div class="title">
      <v-title name="部门管理"></v-title>
    </div>
    <div class="content">
      <div class="bmgl-content-left">
        <div class="bmgl-content-left-content">
          <div class="tree-top-part">
            <el-input
              placeholder="请输入关键字"
              type="text"
              class="input-search"
              size="small"
              prefix-icon="el-icon-search"
              v-model="treeinput" clearable>
            </el-input>
            <div style="padding-left: 25px">
              <el-button @click="addGbm" style="font-weight: bold" size="default" icon="el-icon-plus" type="text">
                新建根部门
              </el-button>
            </div>
          </div>
          <div class="bmgl-content-left-tree">
            <el-tree
              ref="tree"
              :props="defaultProps"
              highlight-current
              :data="bmtree"
              node-key="bmm"
              default-expand-all
              :filter-node-method="filterNode"
              @node-contextmenu="openMenu"
              @node-click="handleNodeClick">
              <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf" :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{ data.bmmc }}
              </span>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="bmgl-content-right" ref="right">
        <el-tabs @tab-click="departmentTabChange" type="card" v-model="activeName" style="height: 100%">
          <el-tab-pane label="部门详情" name="tab_bmxq" class="content-tab-pane">
            <department-detail ref="departmentDetail" @save="updateDepartment" :formData="formInlineBm" :SjBmOptions="SjBmOptions"
                               :isHideParent="isHideParent"></department-detail>
          </el-tab-pane>
          <el-tab-pane label="部门用户" name="tab_bmyh" class="content-tab-pane">
            <div class="zhxy-form zhxy-form-search-part">
              <el-form label-width="120px" inline :model="departmentSearch" ref="searchForm">
                <el-form-item>
                  <span class="zhxy-form-label" slot="label">用户ID/姓名</span>
                  <el-input class="zhxy-form-inline" v-model="departmentSearch.xmid"
                            placeholder="用户ID/姓名"
                            size="small"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="findYhList" size="small">查询
                  </el-button>
                  <el-button type="" @click="resertYhList" size="small">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="button-tab">
              <el-button type="primary" icon="el-icon-plus" size="small"
                         @click="addDepartmentUserFn">新增部门用户
              </el-button>
              <el-button type="danger" icon="el-icon-delete-solid" size="small" @click="deleteDepartmentUser">批量删除
              </el-button>
            </div>
            <department-user
              :page="pageUser"
              :page-size="pageSizeUser"
              :total="totalUser"
              ref="departmentUser"
              :table-data="tableDataBmyh"
              :scrollerHeight="scrollerHeight"
              @departmentUserPageSizeChange="departmentUserPageSizeChange"
              @departmentUserCurrentPage="departmentUserCurrentPage"
              @delDepartmentUserCurrent="delDepartmentUserCurrent">
            </department-user>
          </el-tab-pane>
          <el-tab-pane label="下级部门" name="tab_xjbm" class="content-tab-pane">
            <department-sub-search @subSearch="subSearch" ref="subSearch"></department-sub-search>
            <div class="button-tab">
              <el-button type="" size="small" @click="addBm()">新增下属部门</el-button>
              <el-button type="danger" size="small" @click="delBmBatch()">删除</el-button>
            </div>
            <department-sub
              :page="pageSub"
              :page-size="pageSizeSub"
              :total="totalSub"
              ref="departmentSub"
              :table-data="tableDataBm"
              @departmentSubPageSizeChange="departmentSubPageSizeChange"
              @departmentSubCurrentPage="departmentSubCurrentPage"
              @delSonDepartment="delSonDepartment"
            >
            </department-sub>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!--
        <div v-show="rightMenu" ref="rightMenu" class="rightMenu"
             :style="`position: fixed;top:${this.top}px;left:${this.left}px`">
          <ul>
            <li>编辑</li>
            <li>新增下级</li>
            <li>删除</li>
          </ul>
        </div>
    -->

    <right-menu ref="rightMenuPart" :options="rightMenuOptions"></right-menu>
    <choose-user-dialog
      ref="chooseUser"
      :single="true"
      :title="chooseTitle"
      :treeLoading="treeLoading"
      :treeDetailLoading="treeDetailLoading"
      :tree-data="chooseTree"
      :tree-detail="treeDetail"
      :defaultProps="userProps"
      :page="page"
      :pageSize="pageSize"
      :total="total"
      @searchInfo="searchInfo"
      @nodeClick="nodeClick"
      @pageChange="pageChange"
      @cancel="closeFixedPost"
      @certain="getFixedPost">>
    </choose-user-dialog>
    <!--  新增部门下用户弹窗  -->
    <el-dialog title="新增部门用户" :visible.sync="addDepartmentUser" width="500px" lock-scroll
               close-on-press-escape>
      <div class="">
        <el-form :model="addUserForm" :rules="rules" ref="addUserForm" class="" label-width="120px">
          <el-form-item label="部门名称">
            <p>
              {{ addUserForm.departmentName }}
            </p>
          </el-form-item>
          <el-form-item label="用户名称" required prop="userName">
            <el-input readonly style="width: 80%;vertical-align: middle" size="small" v-model="addUserForm.userName" placeholder="">
              <el-button slot="append" @click="getDepartmentUserDialog">选择</el-button>
            </el-input>
          </el-form-item>
<!--          <el-form-item label="是否主职" required prop="isMainJob">-->
<!--            <el-radio-group v-model="addUserForm.isMainJob">-->
<!--              <el-radio :label="1">主职</el-radio>-->
<!--              <el-radio :label="0">兼职</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
        </el-form>
      </div>
      <div slot="footer">
        <el-button size="small" type="primary" @click="saveAddDepartmentUser()">确 定</el-button>
        <el-button size="small" @click="addDepartmentUser = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fwlistDelete } from '@/app/gzl_01_01/api/lcgl/lcgl';
import { cloneDeep } from 'lodash';
import {
  findBmList,
  findBmByBmm,
  findSjbmList,
  findBmListByPageFbmm,
  findYhListByPageBmm,
  addBm,
  updateBm,
  deleteBmBatch,
  findYhListByBmm,
  drBmBatch,
  deleteYhBm,
  deleteYhBmBatch
} from '../../api/bmgl/bmgl';
import VTitle from '../../../../components/title/VTitle';
import DepartmentDetail from './bmglComponents/DepartmentDetail';
import DepartmentUser from './bmglComponents/DepartmentUser';
import ChooseUserDialog from './bmglComponents/ChooseUserDialog';
import DepartmentSubSearch from './bmglComponents/DepartmentSubSearch';
import DepartmentSub from './bmglComponents/DepartmentSub';
import RightMenu from '../../components/RightMenu/index';

export default {
  components: { // 注册组件
    VTitle,
    DepartmentDetail,
    DepartmentUser,
    ChooseUserDialog,
    DepartmentSubSearch,
    DepartmentSub,
    RightMenu
  },
  data() {
    return {
      // page
      page: 1,
      // pageSize
      pageSize: 50,
      // total
      total: 0,
      // 部门下用户获取弹窗 title
      chooseTitle: '部门下用户',
      // 左侧tree loading
      treeLoading: false,
      // 详情loading
      treeDetailLoading: false,
      // 左侧数据tree data
      chooseTree: [],
      // 中间数据 treeData
      treeDetail: [],

      // 左侧部门树 current
      currentNode: {},
      // 右键菜单menu list
      rightMenuOptions: {
        list: [],
        top: 0,
        left: 0,
        event: {},
        ref: {}
      },
      // 部门信息
      bmForm: {
        visible: false,
        bmm: '',
        bmmc: '',
        sfejbm: '',
        rsjgm: '',
        bksxym: '',
        yjsxym: '',
        pxh: '',
        sfwbmxzgldw: 0,
        ssbmxzgldw: ''
      },
      // 部门信息
      xsbmForm: {
        visible: false,
        bmm: '',
        bmmc: '',
        sfejbm: '',
        rsjgm: '',
        bksxym: '',
        yjsxym: '',
        pxh: '',
        sfwbmxzgldw: 0,
        ssbmxzgldw: ''
      },
      // 部门详情
      departmentSearch: {
        xmid: ''
      },
      // 下级部门详情
      departmentSubSearch: {
        bmmmc: '',
        sfejbm: '',
        rsjgm: '',
        bksxym: '',
        yjsxym: '',
        sfwbmxzgldw: '',
        ssbmxzgldw: ''
      },
      listQuery: {
        fwzt: '' // 服务状态
      },
      // 状态 下拉options
      ztOptions: [
        {
          label: '不可用',
          value: 0
        },
        {
          label: '草稿',
          value: 1
        },
        {
          label: '发布',
          value: 2
        }
      ],
      // 新增部门用户 show
      addDepartmentUser: false,
      // 新增部门用户弹窗form
      addUserForm: {
        departmentName: '部门名称',
        userName: '',
        yhList: [],
        isMainJob: '1'
      },
      // 界面定义属性
      scrollerHeight: 'calc(100% - 110px)',
      treeinput: '',
      // 是否二级部门 options
      partOptions: [
        {
          value: 0,
          label: '否'
        },
        {
          value: 2,
          label: '是'
        }
      ],

      isBmOptions: [
        {
          value: 0,
          label: '否'
        },
        {
          value: 1,
          label: '是'
        }
      ],

      // tab标签
      activeName: 'tab_bmyh',

      lableWidth: '500px',

      // 新增部门用户table
      tableDataBmyh: [],
      // 用户分页
      pageUser: 1,
      pageSizeUser: 30,
      totalUser: 0,

      // 下级部门分页
      pageSub: 1,
      pageSizeSub: 30,
      totalSub: 0,

      // 下级部门table
      tableDataBm: [],
      // table 选中Array
      multipleSelection: [],
      // // 部门分页
      // BmcurrentPage: 1,
      // BmpageSize: 100,
      // Bmtotal: null,

      // 所属保密行政管理单位列表数据
      SjBmOptions: [],
      // 部门详情表单数据
      formInlineBm: {},
      // 部门管理树结构
      bmtree: [],
      // form rules
      rules: {
        userName: [
          {
            required: true,
            message: '用户名称不可为空',
            trigger: 'blur'
          }
        ],
        isMainJob: [
          {
            required: true,
            message: '是否主职不可为空',
            trigger: 'change'
          }
        ]
      },
      userProps: {
        label: 'xm',
        id: 'yhid',
        children: 'children'
      },
      // 部门管理树结构数据格式
      defaultProps: {
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      },
      // 部门管理树结构默认展开数组
      defaultTree: [],
      isHideParent: true
    };
  },
  watch: {
    treeinput(val) {
      // tree 结构搜索过滤
      this.$refs.tree.filter(val);
    }
  },
  mounted() { // 页面初始化加载(只在页面初始时加载一次)
    // this.findListByPage();
    // 获取第一个部门所有信息
    this.loadfirstnode();
    // 默认展开几级
    // setTimeout(() => {
    //   // this.expendDefaultLevel(this.bmtree, 1, 2);
    // }, 2000);
  },
  methods: {
    // pageChange
    pageChange(val) {
      this.page = val;
    },
    // searchInfo
    searchInfo() {

    },
    // 新增部门下用户的确认按钮
    saveAddDepartmentUser() {
      // eslint-disable-next-line consistent-return
      this.$refs.addUserForm.validate((valid) => {
        if (valid) {
          const param = {
            bmm: this.currentNode.bmm,
            // sfzzdr: this.addUserForm.isMainJob,
            yhlist: this.addUserForm.yhList
          };
          // 点击部门获取用户数据
          drBmBatch(param)
            .then((res) => {
              // 获取第一个部门所有信息
              this.loadfirstnode();
              // 关闭弹窗
              this.addDepartmentUser = false;
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 人员弹窗取消
    closeFixedPost() {
      this.$refs.chooseUser.hideDialog();
    },
    // 人员弹窗确定
    getFixedPost(val) {
      if (val.length > 0) {
        console.log(val);
        this.addUserForm.userName = val[0].xm;
        this.addUserForm.yhList = val;
      }
      this.closeFixedPost();
    },
    // 获取部门下用户
    getDepartmentUserDialog() {
      this.treeDetail = [];
      this.page = 1;
      this.$refs.chooseUser.clearNode();
      this.$refs.chooseUser.showDialog();
      this.chooseTree = cloneDeep(this.bmtree);
    },
    nodeClick(val) {
      console.log(val.bmm);

      // 判断
      if (val.bmm === this.currentNode.bmm) {
        this.treeDetail = [];
      } else {
        // 根据部门码获取用户数据源
        const param = {
          bmm: val.bmm,
          xmid: '',
          page: this.page,
          pageSize: this.pageSize
        };
        // 点击部门获取用户数据
        findYhListByPageBmm(param)
          .then((res) => {
            this.treeDetail = res.data.content;
            this.total = res.data.pageInfo.total || 0;
            // this.totalUser = res.data.pageInfo.total;
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    // 查询子部门
    subSearch(params) {
      const param = {
        fbmm: this.currentNode.bmm,
        bmmmc: params.bmmmc,
        sfejbm: params.sfejbm,
        sfwbmxzgldw: params.sfwbmxzgldw,
        rsjgm: params.rsjgm,
        bksxym: params.bksxym,
        yjsxym: params.yjsxym,
        page: this.pageSub,
        pageSize: this.pageSizeSub
      };

      // 点击部门获取用户数据
      findBmListByPageFbmm(param)
        .then((res) => {
          this.tableDataBm = res.data.content;
          this.totalSub = res.data.pageInfo.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 部门保存修改
    updateDepartment(val) {
      const param = {
        bmm: val.bmm,
        bmmc: val.bmmc,
        rsjgm: val.rsjgm,
        bksxym: val.bksxym,
        yjsxym: val.yjsxym,
        sfejbm: val.sfejbm,
        pxh: val.pxh,
        sfwbmxzgldw: val.sfwbmxzgldw,
        ssbmxzgldw: val.ssbmxzgldw
      };
      // 点击部门获取部门信息
      updateBm(param)
        .then((res) => {
          // 获取 全部角色树 接口
          findBmList()
            .then((res1) => {
              this.bmtree = res1.data.content;
              this.$refs.tree.setCurrentKey(val.bmm);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 点击部门右侧tab页
    departmentTabChange(val) {
      // 判断点击的是哪个tab页
      switch (val.name) {
        case 'tab_bmxq':
          this.getDepartmentDetail();
          break;
        case 'tab_bmyh':
          this.getDpartmentUser();
          break;
        default:
          this.getDepartmentSub();
      }
    },
    // 点击部门详情tab页
    getDepartmentDetail() {
      this.$refs.departmentDetail.isEdit = true;
      const param = {
        bmm: this.currentNode.bmm
      };
      // 点击部门获取部门信息
      findBmByBmm(param)
        .then((res) => {
          this.formInlineBm = res.data.content;
        })
        .finally(() => {
          this.loading = false;
        });

      findSjbmList()
        .then((res) => {
          this.SjBmOptions = res.data.content;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 点击部门用户tab页
    getDpartmentUser() {
      const param = {
        bmm: this.currentNode.bmm,
        xmid: this.departmentSearch.xmid,
        page: this.pageUser,
        pageSize: this.pageSizeUser
      };
      // 点击部门获取用户数据
      findYhListByPageBmm(param)
        .then((res) => {
          this.tableDataBmyh = res.data.content;
          this.totalUser = res.data.pageInfo.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 点击下级部门tab页
    getDepartmentSub() {
      const xjbmsearch = this.$refs.subSearch.formData;

      const param = {
        fbmm: this.currentNode.bmm,
        bmmmc: xjbmsearch.bmmmc,
        sfejbm: xjbmsearch.sfejbm,
        sfwbmxzgldw: xjbmsearch.sfwbmxzgldw,
        rsjgm: xjbmsearch.rsjgm,
        bksxym: xjbmsearch.bksxym,
        yjsxym: xjbmsearch.yjsxym,
        page: this.pageSub,
        pageSize: this.pageSizeSub
      };

      // 点击部门获取用户数据
      findBmListByPageFbmm(param)
        .then((res) => {
          this.tableDataBm = res.data.content;
          this.totalSub = res.data.pageInfo.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 添加根目录文件弹窗
    addGbm() {
      this.bmForm.bmm = '';
      this.bmForm.bmmc = '';
      this.bmForm.pxh = '';
      this.bmForm.bksxym = '';
      this.bmForm.rsjgm = '';
      this.bmForm.sfejbm = '';
      this.bmForm.sfwbmxzgldw = '';
      this.bmForm.yjsxym = '';
      this.bmForm.visible = true;
    },
    // 新增根目录确认按钮
    addRootFile() {
      const param = {
        fbmm: '',
        bmm: this.bmForm.bmm,
        bmmc: this.bmForm.bmmc,
        sfejbm: this.bmForm.sfejbm,
        rsjgm: this.bmForm.rsjgm,
        bksxym: this.bmForm.bksxym,
        yjsxym: this.bmForm.yjsxym,
        pxh: this.bmForm.pxh,
        sfwbmxzgldw: this.bmForm.sfwbmxzgldw
      };

      // 点击部门获取用户数据
      addBm(param)
        .then((res) => {
          this.$message.success('新增成功');
          // 获取第一个部门所有信息
          this.loadfirstnode();
        })
        .finally(() => {
          this.loading = false;
        });

      this.bmForm.visible = false;
    },
    // 新增根目录取消按钮
    concleaddRootFile() {
      this.bmForm.visible = false;
    },
    // 新增下属部门
    // eslint-disable-next-line consistent-return
    addBm() {
      if (this.currentNode == null || this.currentNode === '') {
        this.$message.warning('请选择上级部门后新增 ！');
        return false;
      }

      this.xsbmForm.bmm = '';
      this.xsbmForm.bmmc = '';
      this.xsbmForm.pxh = '';
      this.xsbmForm.bksxym = '';
      this.xsbmForm.rsjgm = '';
      this.xsbmForm.sfejbm = '';
      this.xsbmForm.sfwbmxzgldw = '';
      this.xsbmForm.yjsxym = '';

      this.xsbmForm.visible = true;
    },
    // 新增下属部门确认按钮
    addXsbmFile() {
      const param = {
        fbmm: this.currentNode.bmm,
        bmm: this.xsbmForm.bmm,
        bmmc: this.xsbmForm.bmmc,
        sfejbm: this.xsbmForm.sfejbm,
        rsjgm: this.xsbmForm.rsjgm,
        bksxym: this.xsbmForm.bksxym,
        yjsxym: this.xsbmForm.yjsxym,
        pxh: this.xsbmForm.pxh,
        sfwbmxzgldw: this.xsbmForm.sfwbmxzgldw
      };

      // 点击部门获取用户数据
      addBm(param)
        .then((res) => {
          this.$message.success('新增成功');
          // 获取第一个部门所有信息
          this.loadfirstnode();
        })
        .finally(() => {
          this.loading = false;
        });

      this.xsbmForm.visible = false;
    },
    // 新增下属部门取消按钮
    concleaddXsbmFile() {
      this.xsbmForm.visible = false;
    },
    // 删除部门
    delBmBatch() {
      this.$confirm('确认删除已选择部门吗？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';

            const param = {
              jcqxBmList: this.$refs.departmentSub.multipleSelection
            };

            // 点击部门获取用户数据
            deleteBmBatch(param)
              .then((res) => {
                if (res.code === 200) {
                  this.$notify({
                    title: '成功',
                    type: 'success',
                    message: '调出成功',
                    dangerouslyUseHTMLString: true
                  });
                  // 获取第一个部门所有信息
                  this.loadfirstnode();
                } else {
                  this.$notify({
                    title: '失败',
                    type: 'danger',
                    message: '调出失败',
                    dangerouslyUseHTMLString: true
                  });
                }
              })
              .finally(() => {
                this.loading = false;
                instance.confirmButtonLoading = false;
                done();
              });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    // 右键事件
    openMenu(event, item) {
      if (item.isSource) {
        this.rightMenuOptions.list = [
          {
            label: '查看详情',
            onClick: () => {
              this.ckxq(item);
            }
          },
          {
            label: '编辑',
            onClick: () => {
              this.updBm(item);
            }
          },
          {
            label: '删除',
            onClick: () => {
              this.delBm(item);
            },
            style: 'color:red'
          }
        ];
      } else {
        this.rightMenuOptions.list = [
          {
            label: '查看详情',
            onClick: () => {
              this.ckxq(item);
            }
          },
          {
            label: '新建子部门',
            onClick: () => {
              this.addZbm(item);
            }
          },
          // {
          //   label: '新建系统资源',
          //   onClick: this.rightMenuEdit
          // },
          {
            label: '编辑',
            onClick: () => {
              this.updBm(item);
            }
          },
          {
            label: '删除',
            onClick: () => {
              this.delBm(item);
            },
            style: 'color:red'
          }
        ];
      }
      this.$refs.rightMenuPart.showRightMenu();
      this.rightMenuOptions.event = event;
      this.rightMenuOptions.ref = this.$refs.rightMenuPart.$el;
    },
    // 查看详情
    ckxq(params) {
      this.$refs.tree.setCurrentKey(params.bmm);
      this.activeName = 'tab_bmxq';
      this.currentNode = params;

      const param = {
        bmm: params.bmm
      };
      // 点击部门获取部门信息
      findBmByBmm(param)
        .then((res) => {
          this.formInlineBm = res.data.content;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 编辑部门
    updBm(params) {
      this.$refs.tree.setCurrentKey(params.bmm);
      this.activeName = 'tab_bmxq';

      this.currentNode = params;

      const param = {
        bmm: params.bmm
      };
      // 点击部门获取部门信息
      findBmByBmm(param)
        .then((res) => {
          this.formInlineBm = res.data.content;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 新建子部门
    addZbm(params) {
      this.$refs.tree.setCurrentKey(params.bmm);
      this.activeName = 'tab_xjbm';

      this.currentNode = params;
      this.$refs.subSearch.reset();

      const param = {
        bmm: params.bmm
      };
      // 点击部门获取部门信息
      findBmByBmm(param)
        .then((res) => {
          this.formInlineBm = res.data.content;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 删除部门
    delBm(params) {
      this.$refs.tree.setCurrentKey(params.bmm);
      this.activeName = 'tab_bmyh';

      this.currentNode = params;

      const param = {
        jcqxBmList: [
          {
            bmm: params.bmm
          }
        ]
      };
      // 点击部门获取部门信息
      deleteBmBatch(param)
        .then((res) => {
          if (res.code === 200) {
            this.$notify({
              title: '成功',
              type: 'success',
              message: '删除成功',
              dangerouslyUseHTMLString: true

            });
            // 下级部门搜索条件
            const xjbmSearch = this.$refs.subSearch.formData;
            // 获取第一个部门所有信息
            this.loadfirstnode();
          } else {
            this.$notify({
              title: '失败',
              type: 'danger',
              message: '删除失败',
              dangerouslyUseHTMLString: true
            });
          }
        });
    },
    // 子页面page change
    departmentSubCurrentPage(val) {
      // 下级部门搜索条件
      const xjbmSearch = this.$refs.subSearch.formData;

      this.pageSub = val;

      // 查询子部门
      this.subSearch(xjbmSearch);
    },
    // 获取下级部门每页页数
    departmentSubPageSizeChange(val) {
      // 下级部门搜索条件
      const xjbmSearch = this.$refs.subSearch.formData;

      this.pageSizeSub = val;

      // 查询子部门
      this.subSearch(xjbmSearch);
    },
    // 删除下级部门
    delSonDepartment(val) {
      console.log(val);
      this.$confirm('确定删除该部门及部门内用户吗?', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(async () => {
          const param = {
            jcqxBmList: [
              {
                bmm: val.bmm
              }
            ]
          };
          // 点击部门获取部门信息
          deleteBmBatch(param)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({
                  title: '成功',
                  type: 'success',
                  message: '删除成功',
                  dangerouslyUseHTMLString: true

                });
                // 下级部门搜索条件
                const xjbmSearch = this.$refs.subSearch.formData;
                // 获取第一个部门所有信息
                this.loadfirstnode();
              } else {
                this.$notify({
                  title: '失败',
                  type: 'danger',
                  message: '删除失败',
                  dangerouslyUseHTMLString: true
                });
              }
            })
            .finally(() => {
              this.loading = false;
            });
        });
    },
    /**
     * tree 默认代开层级
     * @param data
     * @param startLevel
     * @param stopLevel
     */
    expendDefaultLevel(data, startLevel, stopLevel) {
      this.defaultTree = [];
      const handleTree = (dataTree, level, needLevel) => {
        dataTree.forEach((item) => {
          // this.$set(item, 'privateLevel', level);
          item.privateLevel = level;
          if (item.privateLevel <= needLevel) {
            this.defaultTree.push(item.bmm);
          }
          if (item.privateLevel <= needLevel && item.children && item.children.length > 0) {
            const index = item.privateLevel + 1;
            handleTree(item.children, index, needLevel);
          }
        });
      };
      handleTree(data, startLevel, stopLevel);
    },
    /**
     * tree node 过滤
     * @param value
     * @param data
     * @returns {boolean}
     */
    filterNode(value, data) {
      if (!value) return true;
      return data.bmmc.indexOf(value) !== -1;
    },

    // 获取用户页码数
    departmentUserCurrentPage(val) {
      this.pageUser = val;
      this.findYhList();
    },
    // 获取用户每页页数
    departmentUserPageSizeChange(val) {
      this.pageSizeUser = val;
      this.findYhList();
    },
    // 获取用户信息的查询按钮
    findYhList() {
      const param = {
        bmm: this.currentNode.bmm,
        xmid: this.departmentSearch.xmid,
        page: this.pageUser,
        pageSize: this.pageSizeUser
      };
      // 点击部门获取用户数据
      findYhListByPageBmm(param)
        .then((res) => {
          this.tableDataBmyh = res.data.content;
          this.totalUser = res.data.pageInfo.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取用户信息的重置按钮
    resertYhList() {
      // 清空搜索条件
      this.departmentSearch.xmid = '';

      const param = {
        bmm: this.currentNode.bmm,
        xmid: this.departmentSearch.xmid,
        page: this.pageUser,
        pageSize: this.pageSizeUser
      };

      // 点击部门获取用户数据
      findYhListByPageBmm(param)
        .then((res) => {
          this.tableDataBmyh = res.data.content;
          this.totalUser = res.data.pageInfo.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 部门树点击后执行
    handleNodeClick(data) {
      this.$refs.rightMenuPart.hideRightMenu();
      this.activeName = 'tab_bmyh';
      this.currentNode = data;
      this.departmentSearch.xmid = '';

      const param = {
        bmm: data.bmm,
        xmid: this.departmentSearch.xmid,
        page: this.pageUser,
        pageSize: this.pageSizeUser
      };

      // 点击部门获取用户数据
      findYhListByPageBmm(param)
        .then((res) => {
          this.tableDataBmyh = res.data.content;
          this.totalUser = res.data.pageInfo.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 加载部门树
    async loadfirstnode(resolve) {
      // 获取 全部角色树 接口
      findBmList()
        .then((res) => {
          this.bmtree = res.data.content;
          this.handleNodeClick(res.data.content[0]);
          this.currentNode = res.data.content[0];
          this.$refs.tree.setCurrentKey(res.data.content[0].bmm);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    addDepartmentUserFn() {
      this.addUserForm.userName = '';
      this.addDepartmentUser = true;
      this.addUserForm.departmentName = this.currentNode.bmmc;
      this.$nextTick(() => {
        this.$refs.addUserForm.resetFields();
      });
    },
    // 用户调入
    // eslint-disable-next-line consistent-return
    openZzjg(title) {
      // if (this.currentBm == null || this.currentBm === '') {
      //   this.$message.warning('请选择部门后调入 ！');
      //   return false;
      // }
      // this.$refs.zzjgDialog.show();
      // this.$refs.zzjgDialog.dialogTitle = title;
      // this.$refs.zzjgDialog.bmtree = this.bmtree;
    },
    // 获取部门下所有用户（子组件调用）
    async findYhListByBmm(bmm) {
      // const parms = { // axios(ajax) 请求参数
      //   bmm
      // };
      // const data = await findYhListByBmm(parms); // 请求参数路径方法为‘findListByPage’ data返回数据
      // this.$refs.zzjgDialog.geneDxry(data.data.content);
    },
    // 提交调入
    submitZzjg(data) {
      // let sfzzdr = 0;
      // if (this.$refs.zzjgDialog.dialogTitle === '主职调入') {
      //   sfzzdr = 1;
      // }
      // this.drBmBatch(data, sfzzdr);
    },
    // 用户调入
    // eslint-disable-next-line consistent-return
    async drBmBatch(yhlist, sfzzdr) {
      // if (this.$refs.zzjgDialog.yxrytableData.length === 0) {
      //   this.$message.warning('请添加已选人员 ！');
      //   return false;
      // }
      // this.$confirm(`确定将已选人员${sfzzdr === 1 ? '主职' : '兼职'}调入此部门?`, '提示', {
      //   cancelButtonText: '取消',
      //   confirmButtonText: '确定',
      //   type: 'warning'
      // })
      //   .then(async () => {
      //     const parms = { // axios(ajax) 请求参数
      //       sfzzdr,
      //       yhlist,
      //       bmm: this.currentBm
      //     };
      //     const data = await drBmBatch(parms);
      //     if (data.code === 200) {
      //       this.$notify({
      //         title: '成功',
      //         type: 'success',
      //         message: '调入成功',
      //         dangerouslyUseHTMLString: true
      //       });
      //       await this.findYhListByPageBmm(this.currentBm);
      //       this.$refs.zzjgDialog.dialogVisible = false;
      //     } else {
      //       this.$notify({
      //         title: '失败',
      //         type: 'danger',
      //         message: '调入失败',
      //         dangerouslyUseHTMLString: true
      //       });
      //     }
      //   });
    },
    // 更新部门信息
    async updateBm() {
      // this.$confirm('确定修改当前部门信息吗?', '提示', {
      //   cancelButtonText: '取消',
      //   confirmButtonText: '确定',
      //   type: 'warning'
      // })
      //   .then(async () => {
      //     const parms = {
      //       bmm: this.formInlineBm.bmm,
      //       bmmc: this.formInlineBm.bmmc,
      //       pxh: this.formInlineBm.pxh
      //     };
      //     const data = await updateBm(parms);
      //     if (data.code === 200) {
      //       this.$notify({
      //         title: '成功',
      //         type: 'success',
      //         message: '修改成功',
      //         dangerouslyUseHTMLString: true
      //       });
      //       await this.loadfirstnode();
      //     } else {
      //       this.$notify({
      //         title: '失败',
      //         type: 'danger',
      //         message: '修改失败',
      //         dangerouslyUseHTMLString: true
      //       });
      //     }
      //   });
    },
    /**
     * 删除当前流程
     * @param val
     */
    deleteDepartmentUser() {
      this.$confirm('确认删除已选择用户？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';

            const param = {
              bmm: this.currentNode.bmm,
              yhlist: this.$refs.departmentUser.multipleSelection
            };

            // 批量删除用户部门
            deleteYhBmBatch(param)
              .then((res) => {
                if (res.code === 200) {
                  this.$notify({
                    title: '成功',
                    type: 'success',
                    message: '调出成功',
                    dangerouslyUseHTMLString: true
                  });
                  // 获取部门下用户
                  this.getDpartmentUser();
                } else {
                  this.$notify({
                    title: '失败',
                    type: 'danger',
                    message: '调出失败',
                    dangerouslyUseHTMLString: true
                  });
                }
              })
              .finally(() => {
                instance.confirmButtonLoading = false;
                done();
              });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    // 单个删除 部门用户
    delDepartmentUserCurrent(val) {
      this.$confirm('确定要调出该用户吗?', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(async () => {
          const param = {
            bmm: this.currentNode.bmm,
            yhid: val.yhid
          };

          // 点击部门获取用户数据
          deleteYhBm(param)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({
                  title: '成功',
                  type: 'success',
                  message: '调出成功',
                  dangerouslyUseHTMLString: true
                });
                // 获取部门下用户
                this.getDpartmentUser();
              } else {
                this.$notify({
                  title: '失败',
                  type: 'danger',
                  message: '调出失败',
                  dangerouslyUseHTMLString: true
                });
              }
            })
            .finally(() => {
              this.loading = false;
            });
        });
    }

  }
};

</script>

<style lang="scss" scoped>
.bmgl-content {
  display: flex;
  flex-direction: column;

  .bm-content {
    padding: $page-content-padding;
  }

  .content {
    display: flex;
    flex: 1;
    overflow: auto
  }

  .title {
    background-color: #FFFFFF;
    padding: 10px 10px 0;
  }

  .rightMenu {
    background-color: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);

    ul {
      padding: $page-content-padding;

      li {
        padding: 5px;
        border-bottom: 1px solid $page-bg-color;
        cursor: pointer;

        &:hover {
          color: $page-font-hover-color;
        }
      }
    }
  }

  &-left {
    width: 300px;
    height: 100%;
    padding: $page-content-padding;
    flex-shrink: 0;
    margin-right: $page-content-padding;
    background-color: #ffffff;

    &-content {
      position: relative;
      padding-top: 72px;
      height: 100%;

      .tree-top-part {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }
    }

    &-tree {
      height: 100%;
      overflow: auto;
    }
  }

  &-right {
    padding: $page-content-padding;
    flex: 1;
    background-color: #ffffff;
    overflow: auto;

    .button-tab {
      margin-bottom: $page-content-padding;
    }

    .content-tab-pane {
      height: 100%;
      overflow: auto;
    }
  }
}
</style>

<style lang="scss">
.bmgl-content {
  .el-tabs__content {
    height: calc(100% - 42px)
  }

  .custom-tree-node {
    width: 100%;
  }
}

</style>
