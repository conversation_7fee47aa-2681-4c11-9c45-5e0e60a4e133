<template>
  <el-dialog title="新增下属部门" :visible.sync="dialogVisible" width="450px" lock-scroll close-on-press-escape @close="hiddenInner('formInline')">
    <div class="middle" ref="element">
      <!-- 'formInline' 为此el-form 自定义的对象  -->
      <el-form :model="formInline" :rules="rules" ref="formInline" class="demo-ruleForm" :label-position="labelPosition" label-width="140px">
        <el-form-item label="部门代码" required prop="bmm" >
          <el-input v-model="formInline.bmm" placeholder="部门代码（必填）"></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="部门名称" class="sbj">
          <el-input v-model="formInline.bmmc" placeholder=""></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="部门序号"  class="sbj">
          <el-input v-model="formInline.pxh" placeholder="排序号"></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="是否二级部门" required prop="sfejbm" class="sbj">
          <el-select v-model="formInline.sfejbm" placeholder="是否二级部门 （必填）">
            <el-option label="是" value="2"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>

      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="addBm()">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addBm } from '../../../api/bmgl/bmgl';

export default {
  name: 'add_bm_dialog',
  data() {
    return {
      dialogVisible: false,
      fbmm: '',
      labelPosition: 'right',
      formInline: {
        bmm: '',
        bmmc: '',
        pxh: '',
        sfejbm: ''
      },
      rules: {
        bmm: [
          { required: true, message: '部门码不可为空', trigger: 'blur' }
        ],
        sfejbm: [
          { required: true, message: '请选择是否微页面', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    show() { // 显示方法
      this.dialogVisible = true;
    },
    hidden() { // 隐藏方法
      this.dialogVisible = false;
    },
    hiddenInner(formName) {
      this.innerVisible = false;
      this.$refs[formName].clearValidate();
      this.$refs[formName].resetFields();
    },
    addBm() {
      // eslint-disable-next-line consistent-return
      this.$refs.formInline.validate(async (valid) => {
        if (valid) {
          const params = {
            bmm: this.formInline.bmm,
            bmmc: this.formInline.bmmc,
            pxh: this.formInline.pxh,
            sfejbm: this.formInline.sfejbm,
            fbmm: this.fbmm
          };

          const result = await addBm(params);

          if (result.code !== '200') {
            this.$message.warning(`新增部门失败，失败原因：${ result.data.message}`);
          } else {
            this.$message.success('新增部门成功！');
            this.dialogVisible = false;
            this.$emit('findBmListByPageFbmm', this.fbmm);
          }
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style scoped>
/*@import "../../../../styles/app0301.scss";*/
.sbj {
  margin-top: 15px;
}
</style>
