<template>
  <div style="padding: 0 15px;" class="hamburger_qy" @click="toggleClick" :class="{'dw_left':!isActive}">
    <span class="icon iconfont iconzuocedaohangzhedie hamburger" :class="{'isactive':isActive}"></span>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>',
  props: {
    isActive: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleClick() {
      this.$emit('toggleClick');
    }
  }
};
</script>

<style scoped>
.hamburger {
  display: inline-block;
  vertical-align: middle;
  font-size: 20px;
  color: #fff;
}

.dw_left {
  left: 75px !important;
}

.hamburger.isactive {
  transform: rotate(180deg);
}
</style>
