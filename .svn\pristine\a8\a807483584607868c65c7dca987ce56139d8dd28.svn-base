<template>
  <div style="height: 100%">
    <component :is="isExtra"></component>
  </div>
</template>
<script>
// import { mapMutations, mapState } from 'vuex';
import InreachPage from './indexComponents/InreachPage';
import OutreachPage from './indexComponents/OutreachPage';
import { CONSTANTS } from '../../../../../constant';

export default {
  name: 'AppContentIndex',
  components: {
    InreachPage, // 内部资源
    OutreachPage // 新标签页打卡的资源
  },
  data() {
    return {
      isExtra: this.$route.meta.type === '1' ? InreachPage : OutreachPage // 打开方式
    };
  },
  watch: {
    $route: {
      handler(to) {
        if (to) {
          document.title = this.$route.meta.name || CONSTANTS.APP_TITLE;
          this.isExtra = this.$route.meta.type === '1' ? InreachPage : OutreachPage;
        }
      },
      immediate: true
    }
  },
  created() {
    // if (!this.hasLogin) {
    //   this.setHasLogin(false);
    //   if (window.addEventListener) {
    //     window.addEventListener('storage', this.handleStorage, false);
    //   } else {
    //     window.attachEvent('onstorage', this.handleStorage);
    //   }
    // }
  },
  methods: {
    // ...mapMutations('layout_03_01/user', ['setHasLogin']) // vuex setLogin
    /**
     * localstorage change
     * @param e
     */
    // handleStorage(e) {
    //   if (e.key === 'userInfo_obj' || e.key === 'roleId_str') {
    //     this.$message.error('用户信息失效');
    //     this.$router.push('/login');
    //   }
    // }
  }
};
</script>
