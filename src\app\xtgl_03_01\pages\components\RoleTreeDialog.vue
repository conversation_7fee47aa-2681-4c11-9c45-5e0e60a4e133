<template>
  <div class="zhxy-drawer-tree resource">
    <el-drawer
      :title="title"
      :visible.sync="visible"
      :direction="'rtl'"
      :before-close="handleClose">
      <div class="content">
        <div class="content-button">
          <el-button @click="drawerCertain" type="primary" size="small">确定</el-button>
          <el-button type="" size="small" @click="()=>{visible=false}">取消</el-button>
        </div>
        <div v-if="visible" class="content-main">
          <el-tree
            ref="tree"
            @check-change="handleClick"
            :check-strictly="true"
            :node-key="defaultProps.id"
            show-checkbox
            :data="treeData"
            :props="defaultProps"
          >
            <span :title="data[defaultProps.label]" class="tree-label" slot-scope="{ node, data }">
              {{ data[defaultProps.label] }}
            </span>
          </el-tree>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'roleTreeDialog',
  props: {
    title: {
      type: String,
      default: '选择用户'
    },
    checkList: {
      type: Array,
      default: () => ([])
    },
    isRadio: {
      type: Boolean,
      default: false
    },
    defaultProps: {
      type: Object,
      default: () => ({
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      })
    },
    nodeKey: {
      type: String,
      default: 'bmm'
    },
    treeData: {
      type: Array,
      default: () => []
    }

  },
  data() {
    return {
      // dialog 显隐
      visible: false,
      checkLists: []
    };
  },
  methods: {
    handleClick(data, checked, node) {
      if (this.isRadio) {
        if (checked) {
          // 关键
          this.$refs.tree.setCheckedNodes([data]);
        }
      }
    },
    showDialog() {
      this.visible = true;
    },
    hideDialog() {
      this.visible = false;
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done();
        })
        .catch((_) => {
        });
    },
    drawerCertain() {
      this.$emit('drawerCertain', this.$refs.tree.getCheckedNodes());
    }
  }
};
</script>

<style lang="scss" scoped>
.zhxy-drawer-tree {
  .tree-label {
    font-size: $page-font-size;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
  }

  .content {
    padding: $page-content-padding;
    height: 100%;

    &-button {
      padding-bottom: 2*$page-content-padding;
    }

    &-main {
      height: calc(100% - 52px);
      overflow: auto
    }
  }
}
</style>
<style lang="scss">
.zhxy-drawer-tree.resource {

  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }

    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }

  .el-drawer__body {
    overflow: hidden;
  }

  .tree-content {
    .el-checkbox {
      display: flex;
      align-items: center;
    }

    .el-checkbox__label {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 100%;
    }
  }
}
</style>
