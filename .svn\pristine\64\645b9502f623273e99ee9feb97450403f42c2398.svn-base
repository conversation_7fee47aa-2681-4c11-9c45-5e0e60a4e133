<template>
  <div class="dialog-content">
    <el-form ref="yjjbForm" :model="formData" :rules="rules" label-width="120px" label-position="right">
      <el-form-item label="预警级别码" prop="yjjbm">
      <el-input :disabled="!yjjbmVisible" v-model="formData.yjjbm" placeholder="必填" size="small" style="width: 80%"/>
      </el-form-item>
      <el-form-item label="预警级别名称" prop="yjjbmc">
        <el-input v-model="formData.yjjbmc" placeholder="必填" size="small" style="width: 80%"/>
      </el-form-item>
      <el-form-item label="提醒方式" prop="txfsArray">
        <el-checkbox-group v-model="formData.txfsArray" @change="handleCheckedTxfsChange">
          <el-checkbox v-for="txfs in txfsCsh" :label="txfs" :key="txfs">{{txfs}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
const txfsOptions = ['邮件'];
export default {
  name: 'YjjbxxEdit',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    yjjbmVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 预警级别码校验
    // eslint-disable-next-line consistent-return
    const validateYjjbm = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入预警级别码'));
      }
      callback();
    };
    // 预警级别名称校验
    // eslint-disable-next-line consistent-return
    const validateYjjbmc = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择预警级别名称'));
      }
      callback();
    };
    // 提醒方式校验
    // eslint-disable-next-line consistent-return
    const validateTxfs = (rule, value, callback) => {
      if (this.formData.txfsArray.length <= 0) {
        return callback(new Error('请选择提醒方式'));
      }
      callback();
    };
    return {
      // label 宽度
      lableWidth: '75px',
      checkAll: false,
      txfsCsh: txfsOptions,
      isIndeterminate: true,
      rules: {
        yjjbm: [
          {
            required: true,
            trigger: 'blur',
            validator: validateYjjbm
          },
          {
            max: 20,
            message: '预警级别码长度不能多于20位'
          }
        ],
        yjjbmc: [
          {
            required: true,
            trigger: 'blur',
            validator: validateYjjbmc
          },
          {
            max: 100,
            message: '预警级别名称长度不能多于100位'
          }
        ],
        txfsArray: [
          {
            required: true,
            trigger: 'blur',
            validator: validateTxfs
          }
        ]
      }
    };
  },
  methods: {
    handleCheckedTxfsChange(value) {
      this.formData.txfsArray = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-content {
  overflow: auto;
  padding: 0 10px;
}
</style>
