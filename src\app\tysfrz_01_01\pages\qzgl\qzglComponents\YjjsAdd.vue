<template>
  <div class="dialog-content">
    <el-form ref="form" :model="formData" :rules="rules" label-width="140px" label-position="right">
      <el-form-item v-if="isShowParent" label="父角色ID" prop="sjqzid">
        <el-input disabled  v-model="formData.sjqzid" placeholder="父角色ID" size="small"/>
      </el-form-item>
      <el-form-item label="群组ID" prop="qzid">
        <el-input v-model="formData.qzid" placeholder="群组ID" size="small"/>
      </el-form-item>
      <el-form-item label="群组名称" prop="qzmc">
        <el-input v-model="formData.qzmc" placeholder="群组名称" size="small"/>
      </el-form-item>
      <el-form-item label="排序号" prop="pxh">
        <el-input v-model.trim="formData.pxh" placeholder="选填且只能为数字" size="small" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => ({
        sjqzid: '',
        qzid: '',
        qzmc: '',
        pxh: ''
      })
    },
    editDialogType: {
      type: String,
      default: () => 'new'
    }
  },
  data() {
    return {
      rules: {
        qzid: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入角色id'
          },
          {
            max: 20,
            message: '群组ID长度不能多于20位'
          }
        ],
        qzmc: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入群组名称'
          },
          {
            max: 32,
            message: '群组名称长度不能多于32位'
          }
        ],
        pxh: [
          {
            max: 5,
            message: '排序号长度不能多于5位'
          }
        ]
      }
    };
  },
  computed: {
    isShowParent() {
      return this.editDialogType === 'edit';
    }
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 10px;
  }
</style>
