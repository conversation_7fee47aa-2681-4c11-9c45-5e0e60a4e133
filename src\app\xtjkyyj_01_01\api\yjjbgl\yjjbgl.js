import { createAPI } from '@/utils/request';

const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/xtjkyyj_01_01/yjjbgl`;
// 查询预警级别列表
export const findYjjbList = (data) => createAPI(`${APP_PRE}/findPageList`, 'get', data);
// 删除预警级别
export const delYjjb = (data) => createAPI(`${APP_PRE}/delete`, 'post', data);
/* 查询单条预警级别 */
export const findOneAdd = (data) => createAPI(`${APP_PRE }/findOne`, 'get', data);
// 新增预警级别
export const addYjjb = (data) => createAPI(`${APP_PRE}/add`, 'post', data);
// 更新预警级别
export const updateYjjb = (data) => createAPI(`${APP_PRE}/update`, 'post', data);
