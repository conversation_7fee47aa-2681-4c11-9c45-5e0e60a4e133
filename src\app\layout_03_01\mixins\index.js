import { mapMutations, mapState } from 'vuex';
import { cloneDeep } from 'lodash';

const VERSION_NUM = 'layout_03_01';

export const tagMixins = {
  data() {
    return '';
  },
  computed: {
    ...mapState(`${VERSION_NUM}/tagView`, ['historyList'])
  },
  methods: {
    ...mapMutations(`${VERSION_NUM}/tagView`, ['changeHistoryList']),
    /**
     * 删除指定的tabTag 标签
     * @param path
     */
    closeTargetTag(path) {
      const historyTagList = cloneDeep(this.historyList);
      const index = historyTagList.findIndex((x) => x.path === path);
      if (index > -1) {
        historyTagList.splice(index, 1);
      }
      this.changeHistoryList(historyTagList);
    }
  }
};
