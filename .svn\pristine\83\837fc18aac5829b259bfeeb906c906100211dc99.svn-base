import { createAPI } from '@/utils/request.js';

const BASE_URL = '';
// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/tysfrz_01_01/tysfrzYybmd`;
/**
 * 应用管理获取数据 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getYyIPList = (data) => createAPI(`${APP_PRE}/findList`, 'get', data);
/**
 * 应用管理ip新增 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const addYyIP = (data) => createAPI(`${APP_PRE}/add`, 'post', data);
/**
 * 应用管理IP 删除 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const deleteYyIP = (data) => createAPI(`${APP_PRE}/delete`, 'post', data);
/**
 * 应用管理Ip 状态更改 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const updateYyIP = (data) => createAPI(`${APP_PRE}/update`, 'post', data);
