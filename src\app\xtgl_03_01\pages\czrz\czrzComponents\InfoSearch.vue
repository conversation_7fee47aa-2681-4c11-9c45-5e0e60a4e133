<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">操作人ID/姓名</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.gxhxm" placeholder="操作人ID/姓名"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">小应用标识</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.xyybs" placeholder="小应用标识"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">微页面标识</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.wymbs" placeholder="微页面标识"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">创建时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.cjsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">变更时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.bgsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
        <p
          class="search-fold"
          @click="()=>{this.isShowLabel = !this.isShowLabel}">
          {{this.isShowLabel ? '收缩' : '展开'}}
          <i :class="!this.isShowLabel ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></p>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'yhglSearch',
  data() {
    return {
      isShowLabel: false,
      // label 宽度
      lableWidth: '120px',
      // labelWidth
      lableWidthSingle: '450px',
      // search params
      listQuery: {
        wymbs: null,
        xyybs: null,
        gxhxm: null,
        cjsjkssj: null,
        cjsjjssj: null,
        bgsjkssj: null,
        bgsjjssj: null
      },
      // time date
      valueDate: ''
    };
  },
  methods: {
    /**
       * 搜索查询
       */
    search() {
      const param = {
        wymbs: this.listQuery.wymbs,
        xyybs: this.listQuery.xyybs,
        gxhxm: this.listQuery.gxhxm,
        cjsjkssj: '',
        cjsjjssj: '',
        bgsjkssj: '',
        bgsjjssj: ''
      };

      if (this.listQuery.cjsj) {
        param.cjsjkssj = this.listQuery.cjsj[0];
        param.cjsjjssj = this.listQuery.cjsj[1];
      }
      if (this.listQuery.bgsj) {
        param.bgsjkssj = this.listQuery.bgsj[0];
        param.bgsjjssj = this.listQuery.bgsj[1];
      }

      this.$emit('search', param);
    },
    /**
       * 重置搜索条件
       */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });
      this.$emit('search', this.listQuery);
    }
  }
};
</script>

<style lang="scss" scoped>
  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
</style>
