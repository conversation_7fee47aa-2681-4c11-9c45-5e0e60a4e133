<template>
  <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
    <el-table-column prop="xh" label="序号" width="60"></el-table-column>
    <el-table-column prop="cpuhs" label="CPU核数（个）" width="80"></el-table-column>
    <el-table-column prop="cpuzy" label="CPU负载" width="100">
      <template slot-scope="scope">
        <div v-if="scope.row.cpuzy === 0">
          {{scope.row.cpuzy}}
        </div>
        <div v-if="scope.row.cpuzy !== 0">
          {{scope.row.cpuzy}}%
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="fz01" label="1分钟均值" width="90">
      <template slot-scope="scope">
        <div v-if="scope.row.fz01 === 0">
          {{scope.row.fz01}}
        </div>
        <div v-if="scope.row.fz01 !== 0">
          {{scope.row.fz01}}%
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="fz05" label="5分钟均值" width="90">
      <template slot-scope="scope">
        <div v-if="scope.row.fz05 === 0">
          {{scope.row.fz05}}
        </div>
        <div v-if="scope.row.fz05 !== 0">
          {{scope.row.fz05}}%
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="fz15" label="15分钟均值" width="100">
      <template slot-scope="scope">
        <div v-if="scope.row.fz15 === 0">
          {{scope.row.fz15}}
        </div>
        <div v-if="scope.row.fz15 !== 0">
          {{scope.row.fz15}}%
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="nczj" label="内存总计（Mb）" width="80"></el-table-column>
    <el-table-column prop="nckx" label="内存空闲（Mb）" width="80"></el-table-column>
    <el-table-column prop="cpzj" label="磁盘总计（Gb）" width="80"></el-table-column>
    <el-table-column prop="cpsy" label="磁盘剩余（Gb）" width="80"></el-table-column>
    <el-table-column prop="ycsm" label="异常说明" ></el-table-column>
    <el-table-column prop="cjsj" label="创建时间" width="180">
      <template slot-scope="scope">
        <div v-if="scope.row.cjsj != '' && scope.row.cjsj != null">
          {{dayjs(scope.row.cjsj).format('YYYY-MM-DD HH:mm:ss')}}
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>

import dayjs from 'dayjs';

export default {
  name: 'CjrwgzTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dayjs
    };
  },
  mounted() {
    // console.log(this.tableData);
    // this.Tbjl();
  },
  methods: {
    // Tbjl() {
    //   alert(0);
    //   this.$emit('Tbjl');
    // }
  }
};
</script>
<style lang="scss" scoped>
.dialog-content {
  max-height: 500px;
  overflow: auto;
  padding: 0 10px;
}
</style>
