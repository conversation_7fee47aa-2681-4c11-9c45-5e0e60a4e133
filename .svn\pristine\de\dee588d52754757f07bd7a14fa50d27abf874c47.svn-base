const TITLE = '通用工作台';
let proxy = {
  '/': {
    target: 'http://192.168.1.103:8081',
    changeOrigin: true,
    // ws: true,
    pathRewrite: {
      '^/': ''
    }
  }
};
if (process.env.VUE_APP_PROXY_TARGET_LIST) {
  const list = JSON.parse(process.env.VUE_APP_PROXY_TARGET_LIST);
  proxy = {};
  list.forEach((item) => {
    proxy[item.path] = {
      target: item.target,
      changeOrigin: true,
      ws: true,
      pathRewrite: {
        '^/': ''
      }
    };
  });
}
module.exports = {
  // 基本路径
  publicPath: './',
  // 输出文件目录
  outputDir: 'dist',
  configureWebpack: (config) => {
    config.optimization.minimizer[0].options.terserOptions.compress.drop_console = process.env.NODE_ENV === 'production'; // 生产环境去除console
    // config.devtool = process.env.NODE_ENV === 'production' ? false : 'eval-source-map'  //生产环境去除sourcemap文件
  },
  chainWebpack: (config) => {
    config.plugin('html')
      .tap((args) => {
        args[0].title = TITLE;
        return args;
      });
  },
  css: {
    loaderOptions: {
      sass: {
        prependData: '@import "@/styles/_variable.scss";' // 引入全局变量
      }
    }
  },
  // webpack-dev-server 相关配置
  devServer: {
    port: 8081,
    proxy
  }
};
