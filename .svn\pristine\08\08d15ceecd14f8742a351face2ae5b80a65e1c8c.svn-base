import axios from 'axios';
import { Message } from 'element-ui';
import { clearToken } from './auth-utils';
import router from '../router/index';

let API = '';
// if (process.env.NODE_ENV === 'production') {
//   API = '/bzw_api';
// }

const axiosPromiseArr = [];
window.axiosPromiseArr = axiosPromiseArr;
/**
 * 取消请求
 * @param {*} ever
 */
const removePending = (ever) => {
  // 遍历所有处于pending状态的请求
  // eslint-disable-next-line no-restricted-syntax
  for (const p in axiosPromiseArr) {
    // 判断当前请求是否有相同请求处于pending状态
    if (axiosPromiseArr[p].u === `${ever.url}&${ever.method}`) {
      // 当前请求有相同请求处于pending状态，取消pending状态的该请求
      // axiosPromiseArr[p].f(ever);
      axiosPromiseArr.splice(p, 1);
    }
  }
};

const instance = axios.create({
  // baseURL: '', // api的base_url process.env.BASE_API
  timeout: 50000
});
instance.defaults.withCredentials = true;

// request interceptor
instance.interceptors.request.use(
  (config) => {
    // 发送请求前，把相同的请求从pending列表中移除
    removePending(config);
    config.cancelToken = new axios.CancelToken((c) => {
      // 将请求加入pending列表
      axiosPromiseArr.push({ u: `${config.url}&${config.method}`, f: c });
    });
    // const token = getToken();
    // if (token) {
    //   config.headers.Authorization = token; // 让每个请求携带token
    // }
    return config;
  },

  (error) => {
    console.log(error); // for debug
    Promise.reject(error);
  }
);

// respone interceptor
instance.interceptors.response.use(
  (response) => {
    // 对于已经完成的请求，从pending列表中移除
    removePending(response.config);
    const resp = response.data;



    console.log(resp, 'resp----------------------');

    const errCode = resp.code;

    if (errCode !== undefined) {
      // code:200 登录成功
      if (errCode === 200 || errCode === 0) {
        return resp;
      }
      switch (errCode) {
        case 'A0230':
          clearToken();
          router.push('/login');
          break;
        case 'A0301':
          router.push('/403');
          break;
        default:
      }
      Message({
        message: resp.message,
        type: 'error'
      });
      return Promise.reject(resp);

      // return resp.data;
    }
    return response;
  },
  (error) => {
    console.log(`err${ error}`); // for debug
    if (error.message === undefined) {
      return Promise.reject(error);
    }
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export const createAPI = (url, method, data, action) => {
  url = API + url;
  const config = {};
  if (action) {
    Object.assign(config, action);
  }
  if (method === 'get') {
    config.params = data;
  } else {
    config.data = data; // 包一层{xx：token，xxx：data}
  }
  return instance({
    url,
    method,
    ...config
  });
};

export const createFormAPI = (url, method, data) => {
  const config = {};
  config.data = data;
  config.headers = {
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/json'
  };
  config.responseType = 'json';
  config.transformRequest = [
    (response) => {
      let ret = '';
      // eslint-disable-next-line guard-for-in,no-restricted-syntax
      for (const it in response) {
        ret += `${encodeURIComponent(it) }=${ encodeURIComponent(response[it]) }&`;
      }
      return ret;
    }
  ];
  return instance({
    url,
    method,
    ...config
  });
};
