<template>
  <div v-show="visible" class="right-menu"
       :style="`position: fixed;top:${position.y}px;left:${position.x}px;z-index:999;`">
    <ul>
      <li v-for="(item,index) in options.list" :key="index" @click="item.onClick">
        <i v-if="item.icon" :class="item.icon"></i>
        <span :style="item.style ? item.style : ''">
            {{item.label}}
          </span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'index',
  props: {
    options: {
      type: Object,
      default: () => ({
        list: [],
        top: 0,
        left: 0,
        event: {},
        ref: undefined
      })
    }
  },
  data() {
    return {
      visible: false,
      position: {
        x: 0,
        y: 0
      }
    };
  },
  watch: {
    'options.event': {
      handler() {
        const menuObj = this.options.ref;
        if (menuObj) {
          this.$nextTick(() => {
            const { offsetHeight, offsetWidth } = menuObj;
            const x = this.options.event.clientX + offsetWidth > document.body.clientWidth ? this.options.event.clientX - offsetWidth : this.options.event.clientX;
            const y = this.options.event.clientY + offsetHeight > document.body.clientHeight ? this.options.event.clientY - offsetHeight : this.options.event.clientY;
            this.position.x = x;
            this.position.y = y;
          });
        }
        this.position.x = 0;
        this.position.y = 0;
      },
      deep: true
    }

  },
  mounted() {
    document.body.addEventListener('click', this.hideRightMenu);
    this.$once('hook:beforeDestroy', () => {
      document.body.removeEventListener('touchstart', this.hideRightMenu);
    });
  },
  methods: {
    hideRightMenu() {
      this.visible = false;
    },
    showRightMenu() {
      this.visible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
  .right-menu {
    background-color: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);

    ul {
      padding: $page-content-padding;

      li {
        padding: 5px;
        border-bottom: 1px solid $page-bg-color;
        cursor: pointer;

        &:hover {
          color: $page-font-hover-color;
        }
      }
    }
  }
</style>
