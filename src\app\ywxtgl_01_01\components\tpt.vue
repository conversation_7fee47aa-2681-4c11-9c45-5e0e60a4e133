<template>
  <div ref="myChart" :style="{width: '100%', height: '100%'}"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/graph');
require('echarts/lib/chart/lines');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  props: {
    color: {
      type: Array,
      default() {
        return ['#00F5FF', '#DCDCDC'];
      }
    },
    value: {
      type: Number
    },
    data: {
      type: Array
    }
  },
  watch: {
    data(oldVal, newVal) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },

  methods: {
    drawLine() {
      const getY = function (x) {
        // eslint-disable-next-line no-restricted-properties
        const y = Math.sqrt((1 - Math.pow(x / 38, 2)) * Math.pow(30, 2));
        return y;
      };
      // var items = [
      //     {
      //         symbol: "",
      //         name: "南开区",
      //         value: [6, getY(6)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "河北区",
      //         value: [12, getY(12)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "西青区",
      //         value: [18, getY(18)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "空港IDC",
      //         value: [24, getY(24)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "武清区",
      //         value: [30, getY(30)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "市电子政务中心",
      //         value: [36, getY(36)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "东丽区",
      //         value: [36, -getY(36)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "宝坻区",
      //         value: [30, -getY(30)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "和平区",
      //         value: [24, -getY(24)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "蓟州区",
      //         value: [18, -getY(18)],
      //         pointType: "cloud"
      //     },
      //     {
      //         level: 4,
      //         symbol: "",
      //         name: "河东区",
      //         value: [11, -getY(11)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "河西区",
      //         value: [4, -getY(4)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "静海区",
      //         value: [-4, -getY(-4)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "北辰区",
      //         value: [-11, -getY(-11)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "红桥区",
      //         value: [-18, -getY(-18)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "滨海新区",
      //         value: [-24, -getY(-24)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "宁河区",
      //         value: [-30, -getY(-30)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "烟台道联通机房",
      //         value: [-36, -getY(-36)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "市政府",
      //         value: [-36, getY(-36)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "市人大",
      //         value: [-30, getY(-30)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "市政协",
      //         value: [-24, getY(-24)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "津南区",
      //         value: [-18, getY(-18)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "华苑IDC",
      //         value: [-12, getY(-12)],
      //         pointType: "cloud"
      //     },
      //     {

      //         symbol: "",
      //         name: "科技信息所",
      //         value: [-6, getY(-6)],
      //         pointType: "cloud"
      //     }
      // ];
      this.data.forEach((el, index) => {
        if (el.bs === 'rwcx') {
          // eslint-disable-next-line no-unused-expressions,no-sequences
          el.symbol = 'image://data:image/png;base64,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',
          el.symbolSize = [56, 35];

          el.label = {
            normal: {
              show: true,
              position: 'bottom',
              borderWidth: 1,
              borderRadius: 12,
              padding: [4, 8, 4, 8],
              distance: 10,
              color: 'rgb(64,158,255)',
              borderColor: 'rgb(64,158,255)'
            }
          };
        } else if (el.bs === 'zjxx') {
          // eslint-disable-next-line no-unused-expressions,no-sequences
          el.symbol = 'image://data:image/png;base64,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',
          el.symbolSize = [56, 35];

          el.label = {
            normal: {
              show: true,
              position: 'bottom',
              borderWidth: 1,
              borderRadius: 12,
              padding: [4, 8, 4, 8],
              distance: 10,
              color: 'rgb(64,158,255)',
              borderColor: 'rgb(64,158,255)'
            }
          };
        }
      });
      const dataArr = [];
      const targetCoord = [0, 0];
      this.data.forEach((el) => {
        // if (el.belong) {
        //     items.forEach(element => {
        //         if (el.belong == element.name) {
        //             dataArr.push([{
        //                     coord: element.value
        //                 },
        //                 {
        //                     coord: el.value
        //                 }
        //             ]);
        //         }
        //     });
        // } else if (el.pointType != 'none') {
        dataArr.push([{
          coord: targetCoord
        },
        {
          coord: el.value
        }
        ]);
        // }
      });

      const {
        color,
        value
      } = this;

      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);

      const option = {
        backgroundColor: '#ffffff',

        legend: [],
        xAxis: {
          show: false,
          type: 'value',
          max: 50,
          min: -51
        },
        yAxis: {
          show: false,
          type: 'value',
          max: 50,
          min: -50
        },
        series: [{
          type: 'graph',
          layout: 'none',
          coordinateSystem: 'cartesian2d',
          symbolSize: [15, 15],
          z: 3,
          circular: {
            rotateLabel: true
          },

          itemStyle: {
            normal: {
              shadowColor: 'none'
            }
          },
          data: this.data
        },
        {
          name: '',
          type: 'lines',
          coordinateSystem: 'cartesian2d',
          z: 1,
          effect: {
            show: true,
            smooth: false,
            trailLength: 0,
            symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAhCAYAAADtR0oPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAB2ElEQVQ4T32USU7DQBBFbcKMAAFigcSSg3ADtmzZc0juwQ6JMA9hCBAG81/Rv9W2HEp6cae7fk1tuW6appLVCWwmPTEOw0H2ww8CHHEaiLmERTh9ic/0bBBwiNOiWE7MCwynsXgVH2KCYFYLnNfFhtgSiMj8Lu4TIzFGQLQ1sS12xZFA8CZuxYk4FVdiRDmwIBDtiH1xIA7FsdgTZF4SA5xJTcNE3RSeio1gq4I+a08DAaWtiBhfMpqmP0T0mgXlaL/ZSMY4icx+3JMFRKUUnozPNhHs+TzuwBtEJiJztzEpAlBaXBwCR0bAIZdkQ2xBBC4FbJLhWdhehAUEzBn4A9SMkw0xe87QKolNDh+FjTUZyEzAXJKzcMA7Y0PgDFNLehC2O9HbQ5mBHnDAeEvLKYUA448z8EozTvZ4W8sessAZEHBZ3AWi8h6ikq6ASGSgrCfRvbj8LiFgk0MEzJ9psfaU8Gm9fGVJRKdhMljQyoCx6ZIYLSN1hmhYtEpik5LIgPNNWpdNt0pik2hEZZyXad1bkpt2D0S/EGUPvSW5Bz4pQ0E5UzN4Sjhdi3Pxb9NEcQbqPxNuGEGYv94Iux9ll1ESH2M9wywyWDj9LWOdM9hKZxsOyamqfgG1ZQ8JFbfSTwAAAABJRU5ErkJggg==',
            symbolSize: [10, 30],
            period: 4,
            delay: 2
          },

          lineStyle: {
            width: 2,
            color: 'rgb(64,158,255)',
            curveness: 0
          },
          data: dataArr
        }
        ]
      };

      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
