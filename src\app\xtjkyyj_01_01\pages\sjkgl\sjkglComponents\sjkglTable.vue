<template>
  <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
    <el-table-column prop="xh" label="序号" width="80"></el-table-column>

    <el-table-column prop="sjyip" label="IP" width="150"></el-table-column>
    <el-table-column prop="sjymc" label="数据库名称" width="200"></el-table-column>
    <el-table-column prop="sjyslmc" label="数据源实例名称" width="200"></el-table-column>
    <el-table-column prop="" label="数据库类型" width="120">
      <template slot-scope="scope">
        <span v-if="scope.row.sjylx === 1 ">结构化</span>
        <span v-else-if="scope.row.sjylx === 2 ">非结构化</span>
      </template>
    </el-table-column>

    <el-table-column prop="" label="状态" width="120">
      <template slot-scope="scope">
        <span v-if="scope.row.sjyzt === 0 ">停用</span>
        <span v-else-if="scope.row.sjyzt === 1 ">启用</span>
      </template>
    </el-table-column>

    <el-table-column prop="sjymc" label="描述"></el-table-column>

    <el-table-column fixed="right" prop="" label="操作" width="170">
      <template slot-scope="scope">
        <el-button size="small" @click="updateCjrwgz(scope.row)" type="text">修改</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" @click="Tbjl(scope.row)" type="text">状态明细</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" type="text" @click="delCjrwgz(scope.row)">删除</el-button>
      </template>
    </el-table-column>

  </el-table>
</template>

<script>

export default {
  name: 'CjrwgzTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {

    };
  },
  methods: {
    updateCjrwgz(val) {
      this.$emit('updateCjrwgz', val);
    },
    delCjrwgz(val) {
      this.$emit('delCjrwgz', val);
    },
    Tbjl(val) {
      this.$emit('Tbjl', val);
    }
  }
};
</script>
