export function checkIdCard(value) {
  return new Promise((resolve, inject) => {
    if (!(/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value))) {
      alert(1);
      resolve(new Error('请输入正确的身份证号码'));
    } else {
      resolve();
    }
  });
}

export function checkMail(value) {
  return new Promise((resolve, inject) => {
    if (!(/^^([\s\S]*)+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value))) {
      resolve('请输入正确的邮箱');
    } else {
      resolve();
    }
  });
}

export function checkPhone(value) {
  return new Promise((resolve, inject) => {
    if (!(/^1(3|4|5|6|7|8|9)\d{9}$/.test(value))) {
      resolve('请输入正确的手机号码');
    } else {
      resolve();
    }
  });
}

export function illegalCharacter(value) {
  return new Promise((resolve, inject) => {
    if (/alert|insert|select|update|delete/.test(value)) {
      resolve('您提交的是非法字符');
    } else {
      resolve();
    }
  });
}
