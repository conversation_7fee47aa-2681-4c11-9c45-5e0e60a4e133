<template>
  <div style="height: calc(100% - 50px)">
    <el-table
      :data="tableData"
      stripe
      border
      class="zhxy-table"
      @selection-change="selectionChange"
      height="calc(100% - 40px)">
      <el-table-column align="center" type="selection" label="" width="52"></el-table-column>
      <el-table-column align="center" type="index" label="序号" width="52"></el-table-column>
      <el-table-column prop="id" width="130" v-if=false></el-table-column>
      <el-table-column prop="gnzymc" label="所属功能资源" width="130"></el-table-column>
      <el-table-column prop="gnzymxmc" label="功能资源明细名称" width="150"></el-table-column>
      <el-table-column prop="qqlj" label="请求路径" width="150"></el-table-column>
      <el-table-column prop="cjr" label="创建人" width="100"></el-table-column>
      <el-table-column prop="cjsj" label="创建时间" width="160">
        <template slot-scope="scope">
          {{scope.row.cjsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column prop="bgr" label="变更人" width="100"></el-table-column>
      <el-table-column prop="bgsj" label="变更时间" width="160">
        <template slot-scope="scope">
          {{scope.row.bgsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="100">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="updateYh(scope.row)">修改</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" type="text" @click="delYh(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pageIn">
      <el-pagination
        size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[30, 50, 100, 200,500]"
        layout="total, sizes, prev, pager, next, jumper"
        :total=total>
      </el-pagination>
    </div>
    <el-dialog
      :title="mxdialogType === 'edit'?'修改资源明细':'新增资源明细'"
      :visible.sync="addfileEdit.visible"
      width="400px">
      <el-form ref="addfileEdit" :model="addfileEdit" :rules="rules" label-width="140px" label-position="right">
        <el-form-item label="功能资源明细名称" prop="gnzymxmc">
          <el-input v-model="addfileEdit.gnzymxmc" placeholder="选填" size="small"/>
        </el-form-item>
        <el-form-item label="请求路径" prop="qqlj">
          <el-input v-model="addfileEdit.qqlj" placeholder="选填" size="small"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="addMxFile('addfileEdit')">确 定</el-button>
        <el-button size="small" @click="addfileEdit.visible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';
import {
  findListByPage, addGnzymx, updateGnzymx, delZymx, delPlZymx, add
} from '../../../api/zygl/zygl';
import { delPlJsyh } from '../../../api/jsgl/jsgl';

export default {
  name: 'DepartmentUser',
  filters: {
    dateFilter
  },
  data() {
    return {
      // table pageSize page
      pageSize: 30,
      page: 1,
      // 功能资源id
      xzgnzyid: '',
      // table数据
      tableData: [],
      // 总数据数
      total: 0,
      // 弹框类型
      mxdialogType: '',
      // 资源明细数据id
      id: '',
      // 新增/修改弹框数据
      addfileEdit: {
        visible: false,
        gnzymxmc: '',
        qqlj: ''
      },
      selections: '',
      // 规则
      rules: {
        gnzymxmc: [{
          required: true,
          trigger: 'blur',
          message: '请输入功能资源明细名称'
        },
        {
          max: 100,
          message: '功能资源明细名称长度不能多于100位'
        }
        ],
        qqlj: [{
          required: true,
          trigger: 'blur',
          message: '请输入请求路径'
        },
        {
          max: 100,
          message: '请求路径长度不能多于100位'
        }
        ]
      }
    };
  },
  methods: {
    // 勾选资源明细
    selectionChange(selection) {
      this.selections = selection;
    },
    action(params) {
    },
    // 修改每页数据量
    handleSizeChange(val) {
      this.pageSize = val;
      this.getGmzymx();
    },
    // 修改页数
    handleCurrentChange(val) {
      this.page = val;
      this.getGmzymx();
    },
    // 查询资源明细数据
    getGmzymx() {
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        gnzyid: this.xzgnzyid
      };
      /**
       * 获取 资源明细 接口
       */
      findListByPage(param).then((res) => {
        this.tableData = res.data.content;
        this.total = res.data.pageInfo.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 新增资源明细
    addMxFile(data) {
      if (this.mxdialogType === 'add') {
        const param = {
          gnzymxmc: this.addfileEdit.gnzymxmc, // 页码条数
          qqlj: this.addfileEdit.qqlj, // 页码
          gnzyid: this.xzgnzyid
        };
        // eslint-disable-next-line consistent-return
        this.$refs[data].validate((valid) => {
          if (valid) {
            addGnzymx(param).then((res) => {
              this.addfileEdit.visible = false;
              this.$message.success('新增成功');
              this.getGmzymx();
            }).finally(() => {
              this.loading = false;
            });
          } else {
            this.$message.error('请确认格式');
            return false;
          }
        });
      } else {
        const param = {
          gnzymxmc: this.addfileEdit.gnzymxmc, // 页码条数
          qqlj: this.addfileEdit.qqlj, // 页码
          id: this.id
        };
        // eslint-disable-next-line consistent-return
        this.$refs[data].validate((valid) => {
          if (valid) {
            updateGnzymx(param).then((res) => {
              this.addfileEdit.visible = false;
              this.getGmzymx();
              this.$message.success('修改成功');
            }).finally(() => {
              this.loading = false;
            });
          } else {
            this.$message.error('请确认格式');
            return false;
          }
        });
      }
    },
    // 修改资源明细
    updateYh(data) {
      this.id = data.id;
      this.addfileEdit.visible = true;
      this.addfileEdit.gnzymxmc = data.gnzymxmc;
      this.addfileEdit.qqlj = data.qqlj;
      this.mxdialogType = 'edit';
    },
    /**
     * 删除 资源明细 接口
     */
    delYh(data) {
      this.$confirm('请确认删除该资源明细？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteZymxCurrent(data, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 删除 资源明细 接口
     */
    deleteZymxCurrent(data, instance, done) {
      const param = {
        id: data.id
      };
      delZymx(param).then((res) => {
        this.getGmzymx();
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    // 批量删除资源明细
    deleteJsyh() {
      if (this.selections === '') {
        this.$message.success('请勾选角色用户');
      } else {
        const arr = [];
        this.selections.forEach((item) => {
          const itemparam = {
            id: item.id
          };
          arr.push(itemparam);
        });
        const param = {
          idarr: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },
    /**
     * 批量删除 资源明细 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      delPlZymx(param).then((res) => {
        this.getGmzymx();
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
  .button-tab {
    margin-bottom: $page-content-padding;
  }

  .department-user {
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }

    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
