<template>
  <div class="action">
    <v-title name="快捷操作"></v-title>
    <div class="action-card">
      <div class="action-card-content">
        <div class="action-card-content-item">
          <i class="el-icon-user"></i>
          <p>公告</p>
        </div>
        <div class="action-card-content-item">
          <i class="el-icon-user"></i>
          <p>公告</p>
        </div>
        <div class="action-card-content-item">
          <i class="el-icon-user"></i>
          <p>公告</p>
        </div>
        <div class="action-card-content-item">
          <i class="el-icon-user"></i>
          <p>公告</p>
        </div>
        <div class="action-card-content-item">
          <i class="el-icon-user"></i>
          <p>公告</p>
        </div>
        <div class="action-card-content-item">
          <i class="el-icon-user"></i>
          <p>公告</p>
        </div>

        <div class="action-card-content-bottom">
          <el-button size="small" type="primary">添加</el-button>
          <el-button size="small" type="danger">清除</el-button>
        </div>

      </div>
    </div>
  </div>

</template>

<script>
import VTitle from '@/components/title/VTitle';

export default {
  name: 'QuickAction',
  components: {
    VTitle
  }
};
</script>

<style lang="scss" scoped>
  .action-card{
    height: 100%;
    background-color: #FFFFFF;
    padding: 0 20px;
    position: relative;
    &-content{
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      padding-bottom: 50px;
      overflow: auto;
      height: calc(100% - 37px);
      &-bottom{
        position: absolute;
        bottom: 12px;
      }
      &-item{
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
        width: 60px;
        height: 80px;
        cursor: pointer;
        &:hover{
          color: #2d8cf0;
        }
        i{
          font-size: 20px;
          margin-bottom: 10px;
          color: #4fe3c1;
        }
      }
    }
  }
</style>
