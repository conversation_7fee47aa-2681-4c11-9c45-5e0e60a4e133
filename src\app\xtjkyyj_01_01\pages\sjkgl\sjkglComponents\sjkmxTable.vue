<template>
  <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
    <el-table-column prop="xh" label="序号" width="80"></el-table-column>
    <el-table-column prop="bkjdx" label="空间大小" width="200"></el-table-column>
    <el-table-column prop="kjsy" label="空间使用" width="200"></el-table-column>
    <el-table-column prop="bsl" label="表数量" width="200"></el-table-column>
    <el-table-column prop="hsl" label="行数量" width="200"></el-table-column>
    <el-table-column prop="sjsl" label="数据数量" width="200"></el-table-column>
    <el-table-column prop="wjsl" label="文件数量" width="200"></el-table-column>
    <el-table-column prop="ljsl" label="连接数量" width="200"></el-table-column>
    <el-table-column prop="sssl" label="死锁数量" width="200"></el-table-column>
    <el-table-column prop="zytgsl" label="提供资源数量" width="200"></el-table-column>
    <el-table-column prop="syzysl" label="使用资源数量" width="200"></el-table-column>

    <el-table-column prop="" label="检测结果状态" width="120">
      <template slot-scope="scope">
        <span v-if="scope.row.jcjgzt === 0 ">停用</span>
        <span v-else-if="scope.row.jcjgzt === 1 ">启用</span>
      </template>
    </el-table-column>

    <el-table-column prop="ztcjbs" label="状态采集标识" width="200"></el-table-column>

  </el-table>
</template>

<script>

export default {
  name: 'CjrwgzTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {

    };
  },
  mounted() {
    // console.log(this.tableData);
    // this.Tbjl();
  },
  methods: {
    // Tbjl() {
    //   alert(0);
    //   this.$emit('Tbjl');
    // }
  }
};
</script>
<style lang="scss" scoped>
.dialog-content {
  max-height: 500px;
  overflow: auto;
  padding: 0 10px;
}
</style>
