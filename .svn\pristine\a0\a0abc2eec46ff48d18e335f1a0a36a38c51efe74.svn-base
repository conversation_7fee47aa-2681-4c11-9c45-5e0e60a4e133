const router = [
  {
    name: 'zhgl',
    path: '/tysfrz_01_01/tysfrzZhxx',
    component: () => import('../pages/zhgl'),
    meta: { name: '账号管理' }
  },
  {
    name: 'yygl',
    path: '/tysfrz_01_01/tysfrzYygl',
    component: () => import('../pages/yygl/index'),
    meta: { name: '应用管理' }
  },
  {
    name: 'Yyxq',
    path: '/tysfrz_01_01/Yyxq',
    props: (route) => ({ id: route.query.id }),
    component: () => import('../pages/yygl/Yyxq'),
    meta: { name: '应用详情' }
  },
  {
    name: 'qzgl',
    path: '/tysfrz_01_01/tysfrzQzxx',
    component: () => import('../pages/qzgl'),
    meta: { name: '群组管理' }
  },
  {
    name: 'rzrz',
    path: '/tysfrz_01_01/tysfrzRzrz',
    component: () => import('../pages/rzrz'),
    meta: { name: '认证日志' }
  }
];

export default router;
