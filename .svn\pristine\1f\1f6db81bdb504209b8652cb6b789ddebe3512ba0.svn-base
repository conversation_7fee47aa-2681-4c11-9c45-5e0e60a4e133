// 引入mockjs
import Mock from 'mockjs';

const mockRequest = [
  {
    url: '/layout_03_01/home/<USER>', // 接口url
    methods: 'get', // 请求方式
    data: { // data 返回的数据结果
      code: 200,
      message: '',
      data: {
        content: {
          token: 'ASDjjkldsfjd1211343sdjfdsj',
          data: {
            userName: 'zhao',
            userId: '',
            loginMode: 1,
            phone: 18888888888,
            email: '<EMAIL>'
          },
          roleList: [
            { jsid: '1', jsmc: '名称1' },
            { jsid: '2', jsmc: '名称2' }
          ]
        }
      }
    }
  },
  {
    url: '/layout_03_01/login/getCode', // 接口url
    methods: 'post', // 请求方式
    data: { // data 返回的数据结果
      code: 200,
      message: '',
      data: {
        content: {
          token: 'ASDjjkldsfjd1211343sdjfdsj',
          data: {
            userName: 'zhao',
            userId: '',
            loginMode: 1,
            phone: 18888888888,
            email: '<EMAIL>'
          },
          roleList: [
            { jsid: '1', jsmc: '名称1' },
            { jsid: '2', jsmc: '名称2' }
          ]
        }
      }
    }
  },
  {
    url: '/layout_03_01/login/checkUser', // 接口url
    methods: 'post', // 请求方式
    data: { // data 返回的数据结果
      code: 200,
      message: '',
      data: {
        content: {
          token: 'ASDjjkldsfjd1211343sdjfdsj',
          data: {
            userName: 'zhao',
            userId: '',
            loginMode: 1,
            phone: 18888888888,
            email: '<EMAIL>'
          },
          roleList: [
            { jsid: '1', jsmc: '名称1' },
            { jsid: '2', jsmc: '名称2' }
          ]
        }
      }
    }
  },
  // 获取权限列表mock数据
  {
    url: '/layout_03_01/home/<USER>', // 接口url
    methods: 'get', // 请求方式
    data: { // data 返回的数据结果
      code: 200,
      message: '',
      data: {
        content: [
          {
            gnzyid: '02eda3fa25ef47d1987cbe5ce9690773', // 资源id
            name: '角色管理', // 标签名称
            openType: '1', // 1:系统内置打开  2:新标签页打开
            path: '/xtgl_02_01/jcqxJs', // 路由地址
            rootId: '344acb552659f29ec4cf42c2001a1481', // 所属根菜单id
            sfsqxxz: '1' // 是否受权限限制
          },
          {
            gnzyid: '02eda3fa25ef47d1987cbe5ce9690773',
            name: '角色资源',
            openType: '1',
            path: '/xtgl_02_01/jcqxJszy',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5e93769bfd2545578b3a4cf83ebf042a',
            name: '系统参数',
            openType: '1',
            path: '/xtgl_02_01/jcggXtcs',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5ef6a5393deb44c8a9ca74e0a17a8648',
            name: '账号管理',
            openType: '1',
            path: '/tysfrz_01_01/zhgl',
            rootId: 'bf023d6e450898a7254350b3bd03b819',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5ef6a5393deb44c8a9ca74e0a17a8649',
            name: '应用管理',
            openType: '1',
            path: '/tysfrz_01_01/yygl',
            rootId: 'bf023d6e450898a7254350b3bd03b819',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5ef6a5393deb44c8a9ca74e0a17a86410',
            name: '应用详情',
            openType: '1',
            path: '/tysfrz_01_01/Yyxq',
            rootId: 'bf023d6e450898a7254350b3bd03b819',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5ef6a5393deb44c8a9ca74e0a17a8748',
            name: '群组管理',
            openType: '1',
            path: '/tysfrz_01_01/qzgl',
            rootId: 'bf023d6e450898a7254350b3bd03b819',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5ef6a5393deb44c8a9ca74e0a17a87481',
            name: '认证日志',
            openType: '1',
            path: '/tysfrz_01_01/rzrz',
            rootId: 'bf023d6e450898a7254350b3bd03b819',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5ef6a5393deb44c8a9ca74e0a17a8647',
            name: '流程管理',
            openType: '1',
            path: '/gzl_01_01/gzlJbxx',
            rootId: 'bf023d6e450898a7254350b3bd03b818',
            sfsqxxz: '1'
          },
          {
            gnzyid: '5ef6a5393deb44c8a9ca74e0a17a8647',
            name: '流程配置详情页',
            openType: '2',
            path: '/gzl_01_01/gzlLchj',
            rootId: 'bf023d6e450898a7254350b3bd03b818',
            sfsqxxz: '1'
          },
          {
            gnzyid: '66f02e6e7fbf414a8c67f0284f3f4e9a',
            name: '资源明细',
            openType: '1',
            path: '/xtgl_02_01/jcqxGnzymx',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: '66f02e6e7fbf414a8c67f0284f3f4e9a',
            name: '资源管理',
            openType: '1',
            path: '/xtgl_02_01/jcqxGnzy',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: '77437cd07ee645f596c15cc8a7078377',
            name: '用户管理',
            openType: '1',
            path: '/xtgl_02_01/jcqxYh',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: '8ce08bb6206a4630a8afdb8bdb021dd8',
            name: '部门管理',
            openType: '1',
            path: '/xtgl_02_01/jcqxBm',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: '9cf533ad7cdd440590afc649dcfa393e',
            name: '登录日志',
            openType: '1',
            path: '/xtgl_02_01/jcggDlrz',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: 'aa2f655e9dbe460dabacc2952fffdea2',
            name: '操作日志',
            openType: '1',
            path: '/xtgl_02_01/jcggCzrz',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: 'd208e68af1c144e1bce09837627fe72d',
            name: '菜单管理',
            openType: '1',
            path: '/xtgl_02_01/tygzt3Tycdpz',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: 'dbf6a64b572744439030e3473b39cdec',
            name: '代码表',
            openType: '1',
            path: '/xtgl_02_01/jcggDmfl',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            gnzyid: 'dbf6a64b572744439030e3473b39cdec',
            name: '代码项',
            openType: '1',
            path: '/xtgl_02_01/jcggDmx',
            rootId: '344acb552659f29ec4cf42c2001a1481',
            sfsqxxz: '1'
          },
          {
            name: '资源对象维护组管理',
            path: '/zydxwhzgl/zydxwhzgl',
            rootId: '4',
            openType: 1
          },
          {
            name: '资源编码规则管理',
            path: '/zybmgzgl/zybmgzgl',
            rootId: '4',
            openType: 1
          },
          {
            name: '资源对象属性模板管理',
            path: '/zydxsxmbgl/zydxsxmbgl',
            rootId: '4',
            openType: 1
          },
          {
            name: '字典数据管理',
            path: '/zdsjgl/zdsjgl',
            rootId: '4',
            openType: 1
          },
          {
            name: '信息资源审核',
            path: '/xxzygl/xxzygl',
            rootId: '4',
            openType: 1
          },
          {
            name: '资源对象管理',
            path: '/zydxgl/zydxgl',
            rootId: '6',
            openType: 1
          },
          {
            name: '目录详情',
            path: '/xxzygl/mlxq',
            rootId: '8',
            openType: 1
          },
          {
            name: '资源库管理',
            path: '/zykgl/zykgl',
            rootId: '9',
            openType: 1
          },
          {
            gnzyid: 'bf02',
            name: '待已办中心',
            openType: '1',
            path: '/dybzx',
            rootId: 'bf',
            sfsqxxz: '1'
          },
          {
            gnzyid: 'bf03',
            name: '接口授权管理',
            openType: '1',
            path: '/jksqgl',
            rootId: '2',
            sfsqxxz: '1'
          }
        ]
      }
    }
  },
  // 获取菜单列表资源mock数据
  {
    url: '/layout_03_01/home/<USER>', // 接口url
    methods: 'get', // 请求方式
    data: { // data 返回的数据结果
      code: 200,
      message: '',
      data: {
        content: [
          {
            icon: 'el-icon-picture', // 菜单icon图标
            id: '344acb552659f29ec4cf42c2001a1481', // 菜单id
            name: '系统管理', // 菜单名称
            rootId: '344acb552659f29ec4cf42c2001a1481', // 所属更目录菜单id
            // 子菜单
            children: [
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481', // 父级菜单id
                icon: 'el-icon-s-goods', // 菜单icon图标
                id: '401ceee7fd6c5d882c28cd1514164491', // 菜单id
                isOut: '0', // 是否外联 0:否 1:是
                name: '系统参数', // 菜单名称
                openType: '1', // 打开方式- 1:内置打开 2:新标签页打开
                path: '/xtgl_02_01/jcggXtcs', // 菜单route
                rootId: '344acb552659f29ec4cf42c2001a1481' // 根菜单id
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                id: 'd12328c965db4a7fa2351ae5742df669',
                isOut: '0',
                name: '用户管理',
                openType: '1',
                path: '/xtgl_02_01/jcqxYh',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                id: 'a7a62ace93e445fa8b83cb4e186d3f47',
                isOut: '0',
                name: '资源管理',
                openType: '1',
                path: '/xtgl_02_01/jcqxGnzy',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                id: '2405729bd98a4647b848044062e62c7f',
                isOut: '0',
                name: '部门管理',
                openType: '1',
                path: '/xtgl_02_01/jcqxBm',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                id: 'df6af213ec884c77bda5274a38f05d1c',
                isOut: '0',
                name: '角色管理',
                openType: '1',
                path: '/xtgl_02_01/jcqxJs',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                id: '51da454027654262adfaf44ebe60b9d6',
                isOut: '0',
                name: '菜单管理',
                openType: '1',
                path: '/xtgl_02_01/tygzt3Tycdpz',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                id: 'd925ed41175d469b936a7fc80d1e3eed',
                isOut: '0',
                name: '代码表',
                openType: '1',
                path: '/xtgl_02_01/jcggDmfl',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                icon: 'el-icon-info',
                id: '13ddb02b94c004a8a5eea6f60ed2cb77',
                isOut: '0',
                name: '操作日志',
                openType: '1',
                path: '/xtgl_02_01/jcggCzrz',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              },
              {
                fcdid: '344acb552659f29ec4cf42c2001a1481',
                id: '958ac131b7624110bdc6d575af591870',
                isOut: '0',
                name: '登录日志',
                openType: '1',
                path: '/xtgl_02_01/jcggDlrz',
                rootId: '344acb552659f29ec4cf42c2001a1481'
              }
            ]
          },
          {
            children: [
              {
                fcdid: 'bf023d6e450898a7254350b3bd03b819',
                id: '73a7cd3a528b45ab96ee1317579f77a8',
                isOut: '0',
                name: '账号管理',
                openType: '1',
                path: '/tysfrz_01_01/zhgl',
                rootId: 'bf023d6e450898a7254350b3bd03b819'
              },
              {
                fcdid: 'bf023d6e450898a7254350b3bd03b819',
                id: '73a7cd3a528b45ab96ee1317579f77a9',
                isOut: '0',
                name: '应用管理',
                openType: '1',
                path: '/tysfrz_01_01/yygl',
                rootId: 'bf023d6e450898a7254350b3bd03b819'
              },
              {
                fcdid: 'bf023d6e450898a7254350b3bd03b919',
                id: '73a7cd3a528b45ab96ee1317579f78a8',
                isOut: '0',
                name: '群组管理',
                openType: '1',
                path: '/tysfrz_01_01/qzgl',
                rootId: 'bf023d6e450898a7254350b3bd03b919'
              },
              {
                fcdid: 'bf023d6e450898a7254350b3bd03b919',
                id: '73a7cd3a528b45ab96ee1317579f78a81',
                isOut: '0',
                name: '认证日志',
                openType: '1',
                path: '/tysfrz_01_01/rzrz',
                rootId: 'bf023d6e450898a7254350b3bd03b819'
              }
            ],
            icon: 'el-icon-s-goods',
            id: 'bf023d6e450898a7254350b3bd03b819',
            name: '统一身份认证',
            rootId: 'bf023d6e450898a7254350b3bd03b819'
          },
          {
            children: [
              {
                fcdid: 'bf023d6e450898a7254350b3bd03b818',
                id: '73a7cd3a528b45ab96ee1317579f77a7',
                isOut: '0',
                name: '流程管理',
                openType: '2',
                path: '/gzl_01_01/gzlJbxx',
                rootId: 'bf023d6e450898a7254350b3bd03b818'
              }
            ],
            icon: 'el-icon-s-goods',
            id: 'bf023d6e450898a7254350b3bd03b818',
            name: '流程中心',
            rootId: 'bf023d6e450898a7254350b3bd03b818'
          },
          {
            name: '基础信息配置',
            path: '',
            icon: 'el-icon-s-data',
            id: '4',
            children: [
              {
                name: '资源对象维护组管理',
                path: '/zydxwhzgl/zydxwhzgl',
                icon: 'el-icon-s-data',
                id: '4-1',
                rootId: '4'
              },
              {
                name: '资源编码规则管理',
                path: '/zybmgzgl/zybmgzgl',
                icon: 'el-icon-s-data',
                id: '4-2',
                rootId: '4'
              },
              {
                name: '资源对象属性模板管理',
                path: '/zydxsxmbgl/zydxsxmbgl',
                icon: 'el-icon-s-data',
                id: '4-3',
                rootId: '4'
              },
              {
                name: '字典数据管理',
                path: '/zdsjgl/zdsjgl',
                icon: 'el-icon-s-data',
                id: '4-4',
                rootId: '4'
              },
              {
                name: '信息资源审核',
                path: '/xxzygl/xxzygl',
                icon: 'el-icon-s-data',
                id: '4-5',
                rootId: '4'
              },
              {
                name: '资源对象管理',
                path: '/zydxgl/zydxgl',
                icon: 'el-icon-s-data',
                id: '4-6',
                rootId: '4'
              },
              {
                name: '资源库管理',
                path: '/zykgl/zykgl',
                icon: 'el-icon-s-data',
                id: '4-7',
                rootId: '4'
              }
            ]
          },
          {
            name: '元数据管理',
            path: '',
            icon: 'el-icon-s-data',
            id: '5',
            children: [
              {
                name: '元数据版本管理',
                path: '/ysjgl/ysjbbgl',
                icon: 'el-icon-s-data',
                id: '5-1',
                rootId: '5'
              }

            ]
          },
          {
            children: [
              {
                fcdid: 'bf',
                id: '73a',
                isOut: '0',
                name: '待已办管理',
                openType: '1',
                path: '/dybzx',
                rootId: 'bf'
              }
            ],
            icon: 'el-icon-s-goods',
            id: 'bf',
            name: '待已办中心',
            rootId: 'bf'
          }
        ]
      }
    }
  }
];

mockRequest.forEach((item) => {
  Mock.mock(RegExp(`${item.url}*`), item.methods, item.data);
});
export default Mock;
