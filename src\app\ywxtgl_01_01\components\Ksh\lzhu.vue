<template>
  <div ref="myChart" style="width:100%; height:100%"></div>
</template>

<script>
// 引入基本模板
// let echarts = require('echarts/dist/echarts')
// // 引入柱状图组件
// require('echarts/lib/chart/bar')
// // 引入提示框和title组件
// require('echarts/lib/component/tooltip')
// require('echarts/lib/component/title')
// require('echarts/lib/component/dataZoom')
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from 'echarts/core';
// 引入各种图表，图表后缀都为 Chart
import {
  BarChart
} from 'echarts/charts';
// 引入提示框，标题，直角坐标系等组件，组件后缀都为 Component
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
  // GeoCoComponent
} from 'echarts/components';
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import {
  CanvasRenderer
} from 'echarts/renderers';

// 注册必须的组件
echarts.use(
  [TitleComponent, TooltipComponent, GridComponent, LegendComponent, BarChart, CanvasRenderer]
);
export default {
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      myChart: null,
      myChart64: null
    };
  },
  watch: {
    data(val) {
      console.log('数据变了');
      console.log(val);
      this.drawLine();
    }

  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      console.log(this.data);
      const {
        list,
        title
      } = this;
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(this.$refs.myChart);

      const dataAxis = this.data.xData;
      const data = this.data.yData;
      // var yMax = 500;
      const dataShadow = [];

      // for (var i = 0; i < data.length; i++) {
      //     dataShadow.push(yMax);
      // }
      console.log(this.data);
      const option = {
        legend: this.data.legend,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          data: dataAxis,
          axisLabel: {
            // inside: true,// 显示在内部
            textStyle: {
              // color: '#fff'
            },
            rotate: 40
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          z: 10
        },

        yAxis: {
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#999'
            }
          }
        },

        series: [{ // For shadow
          type: 'bar',
          itemStyle: {
            color: 'rgba(0,0,0,0.05)'
          },
          barGap: '-100%',
          barCategoryGap: '40%',
          data: dataShadow,
          barWidth: '50%',
          animation: false
        }, {
          type: 'bar',
          barWidth: '50%',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [{
                offset: 0,
                color: '#83bff6'
              }, {
                offset: 0.5,
                color: '#188df0'
              }, {
                offset: 1,
                color: '#188df0'
              }
              ]
            )
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [{
                  offset: 0,
                  color: '#2378f7'
                }, {
                  offset: 0.7,
                  color: '#2378f7'
                }, {
                  offset: 1,
                  color: '#83bff6'
                }
                ]
              )
            }
          },
          data
        }
        ]
      };
      this.myChart.setOption(option);
    }

  }
};
</script>

<style>
</style>
