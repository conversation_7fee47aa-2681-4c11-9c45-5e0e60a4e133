import { createAPI } from '@/utils/request.js';

const BASE_URL = '';

/* 查询列表数据 */
export const findList = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmfl/findList`, 'get', data);

/* 查询列表数据 */
export const findDmxList = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmx/findList`, 'get', data);

/* 查询单条数据 */
export const findOne = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmfl/findOne`, 'get', data);

/* 修改 */
export const update = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmfl/update`, 'post', data);

/* 新增 */
export const add = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmfl/add`, 'post', data);

/* 新增 */
export const adddmx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmx/add`, 'post', data);

/* 修改 */
export const updatedmx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmx/update`, 'post', data);

/* 查询列表数据 */
export const findListByPageDmx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmx/findListByPage`, 'get', data);

/* 删除 */
export const del = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmfl/delete`, 'post', data);

/* 删除 */
export const deldmx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmx/delete`, 'post', data);

/*
批量删除
 */
export const deleteBatchDmx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmx/deleteBatchDmx`, 'post', data);

export const findOneByID = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcggDmfl/findOneByID`, 'get', data);
