<template>
  <el-table class="zhxy-table" :data="tableData" stripe border
            :height="'100%'">
    <el-table-column prop="czrid" label="操作人id" width="100"></el-table-column>
    <el-table-column prop="czrxm" label="操作人姓名" width="100" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column prop="xyybs" label="小应用标识" width="120"></el-table-column>
    <el-table-column prop="wymbs" label="微页面标识" width="120" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column prop="ymngnmc" label="页面内功能名称" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column prop="cznr" label="操作内容" width="360" :show-overflow-tooltip="true"></el-table-column>
<!--    <el-table-column prop="czyrl" label="操作url" width="150"></el-table-column>-->
    <el-table-column prop="cjr" label="创建人" width="100" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column prop="cjsj" label="创建时间" width="180">
      <template slot-scope="scope">
        {{scope.row.cjsj | dateFilter}}
      </template>
    </el-table-column>
<!--    <el-table-column prop="bgr" label="变更人" width="100"></el-table-column>-->
<!--    <el-table-column prop="bgsj" label="变更时间" width="180">-->
<!--      <template slot-scope="scope">-->
<!--        {{scope.row.bgsj | dateFilter}}-->
<!--      </template></el-table-column>-->
  </el-table>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';

export default {
  name: 'YhglTable',
  filters: {
    dateFilter
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  methods: {
  }
};
</script>

<style scoped>

</style>
