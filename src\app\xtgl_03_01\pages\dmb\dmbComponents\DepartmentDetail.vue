<template>
  <div class="department-user" ref="element">
    <el-button type="primary" size="small" @click="buttonAction"> {{isEdit ? '编辑' :
      '保存'}}
    </el-button>
    <el-form :disabled="isEdit" label-width="150px" :model="formData" :rules="rules"
             class="zhxy-form zhxy-form-search-part"
             :class="isEdit ? '' : 'form-status-edit'">
      <el-form-item prop="dmflbs">
        <span class="zhxy-form-label" slot="label">代码分类标识</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.dmflbs" placeholder="代码分类标识"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item prop="dmflmc">
        <span slot="label" class="zhxy-form-label">代码分类名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.dmflmc" placeholder="代码分类名称"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item prop="xscj">
        <span class="zhxy-form-label" slot="label">显示层级</span>
        <el-input class="zhxy-form-inline" v-model="formData.xscj"
                  placeholder="显示层级"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item prop="pxh">
        <span class="zhxy-form-label" slot="label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh"
                  placeholder="排序号"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">是否可用</span>
        <el-select class="zhxy-form-inline" v-model="formData.sfky" placeholder="是否可用"
                   size="small">
          <el-option
            v-for="item in partOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjr"
                  placeholder="创建人"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjsj"
                  placeholder="创建时间"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgr"
                  placeholder="变更人"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgsj"
                  placeholder="变更时间"
                  size="small"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  update
} from '../../../api/dmb/dmb';

export default {
  name: 'DepartmentDetail',

  data() {
    return {
      // 是否可编辑
      isEdit: true,
      formData: {
        dmflbs: '',
        dmflmc: '',
        xscj: '',
        pxh: '',
        sfky: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      },
      // 是否可用 options
      partOptions: [
        {
          value: '否',
          lable: 0
        },
        {
          value: '是',
          lable: 1
        }
      ],
      rules: {
        dmflbs: [{
          required: true,
          trigger: 'blur',
          message: '请输入代码分类标识'
        },
        {
          max: 50,
          message: '代码分类标识长度不能多于50位'
        }
        ],
        dmflmc: [{
          required: true,
          trigger: 'blur',
          message: '请输入代码分类名称'
        },
        {
          max: 100,
          message: '代码分类名称长度不能多于100位'
        }
        ],
        pxh: [{
          max: 10,
          message: '排序号长度不能多于10位'
        }
        ],
        xscj: [{
          max: 5,
          message: '显示层级长度不能多于5位'
        }
        ]
      }
    };
  },
  methods: {
    buttonAction() {
      // 切换编辑状态
      this.isEdit = !this.isEdit;
      if (this.isEdit === true) {
        const param = {
          dmflbs: this.formData.dmflbs,
          dmflmc: this.formData.dmflmc,
          xscj: this.formData.xscj,
          sfky: this.formData.sfky,
          pxh: this.formData.pxh
        };
        /**
         * 修改 角色 接口
         */
        update(param).then((res) => {
          this.$message.success('修改成功');
        }).finally(() => {
          this.loading = false;
        });
      }
    }
  }
};
</script>
<style lang="scss">
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
      cursor: default;
    }
  }
</style>
<style lang="scss" scoped>
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
    }
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }

    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
