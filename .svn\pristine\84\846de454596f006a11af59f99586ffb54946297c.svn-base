<template>
  <div ref="myChart" style="width:100%; height:100%"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/bar');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  props: {
    list: {
      type: Object
    },
    msg: {
      type: String
    },
    title: {
      type: String
    }
  },
  data() {
    return {
      myChart: null,
      myChart64: null
    };
  },
  watch: {
    list(val) {
      this.drawLine();
    },
    title(val) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      const { list, title } = this;
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(this.$refs.myChart);

      const option = {
        color: [
          new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(106,185,242,1)'
          },
          {
            offset: 1,
            color: 'rgba(106,185,242,.3)'
          }
          ]),

          new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(94,95,95,1)'
          },
          {
            offset: 1,
            color: 'rgba(94,95,95,.3)'
          }
          ]),
          new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(202,27,27,1)'
          },
          {
            offset: 1,
            color: 'rgba(202,27,27,.3)'
          }
          ])
        ],
        title: {
          text: '定版标准数量统计',
          left: 'center'
        },
        grid: {
          top: '15%',
          bottom: '10%'
        },
        tooltip: {
          trigger: 'axis',
          // confine: false,
          position: 'top',
          textStyle: {
            fontSize: 12
          },
          backgroundColor: 'transparent',
          extraCssText: 'box-shadow: 0 0 20px #00C7FF inset;'
        },
        legend: {
          data: ['1', '2', '3', '4'],
          top: '50%'
        },
        // toolbox: {
        //     show: true,
        //     orient: 'vertical',
        //     left: 'right',
        //     top: 'center',
        //     feature: {
        //         mark: {show: true},
        //         dataView: {show: true, readOnly: false},
        //         magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
        //         restore: {show: true},
        //         saveAsImage: {show: true}
        //     }
        // },
        xAxis: [{
          type: 'category',
          // axisTick: {show: false},
          data: ['v_1.0.1', 'v_1.0.2', 'v_1.0.3', 'v_1.0.4', 'v_1.0.5']
        }],
        yAxis: [{
          type: 'value'
        }],
        dataZoom: [{
          show: false,
          xAxisIndex: [0],
          start: 0,
          end: 100
        }, {
          type: 'inside',
          show: false,
          start: 1,
          end: 35
        }],
        series: [{
          name: '1',
          type: 'bar',
          itemStyle: {
            normal: {
              barBorderRadius: 30
            }
          },
          data: [98, 77, 101, 99, 40]
        },
        {
          name: '2',
          type: 'bar',
          itemStyle: {
            normal: {
              barBorderRadius: 30
            }
          },
          data: [320, 332, 301, 334, 390]
        },

        {
          name: '4',
          type: 'bar',
          itemStyle: {
            normal: {
              barBorderRadius: 30
            }
          },
          data: [150, 232, 201, 154, 190]
        }
        ]
      };
      this.myChart.setOption(option);
      setTimeout(() => {
        this.$emit('sendMychart', this.msg);
        // alert(1)
      }, 3000);
    },
    // 生成64位图片
    get64Bata() {
      if (typeof (this.myChart) === 'undefined') {
        this.myChart64 = '';
      } else {
        this.myChart64 = this.myChart.getDataURL({ type: 'png' }).split(',')[1];
      }
      return this.myChart64;
    }
  }

};
</script>

<style>
</style>
