<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">用户ID/姓名</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.idxm" placeholder="用户ID/姓名"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">部门</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.bmmc" placeholder="部门"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">角色</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.jsmc" placeholder="角色"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">手机号</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.sjh" placeholder="手机号"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">电子邮箱</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.dzyx" placeholder="电子邮箱"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">身份描述</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.sfms" placeholder="身份描述"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">是否自建</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.sfzj" placeholder="是否自建"
                   size="small">
          <el-option
            v-for="item in sfzjOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">可登录方式</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.kdlfs" placeholder="可登录方式"
                   size="small">
          <el-option
            v-for="item in kdlfsOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">备注</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.bz" placeholder="备注"
                  size="small"></el-input>
        <!-- v-model 存放属性-->
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">用户状态</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.yhzt" placeholder="用户状态"
                   size="small">
          <el-option
            v-for="item in yhztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">创建时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.cjsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">变更时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.bgsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
        <p
          class="search-fold"
          @click="()=>{this.isShowLabel = !this.isShowLabel}">
          {{this.isShowLabel ? '收缩' : '展开'}}
          <i :class="!this.isShowLabel ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></p>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'yhglSearch',
  data() {
    return {
      isShowLabel: false,
      // label 宽度
      lableWidth: '120px',
      // labelWidth
      lableWidthSingle: '450px',
      // search params
      listQuery: {
        idxm: null,
        bmmc: null,
        jsmc: null,
        sjh: null,
        dzyx: null,
        sfms: null,
        kdlfs: null,
        bz: null,
        sfzj: null,
        yhzt: null,
        cjsjkssj: null,
        cjsjjssj: null,
        bgsjkssj: null,
        bgsjjssj: null
      },
      sfzjOptions: [{
        label: '否',
        value: 0
      }, {
        label: '是',
        value: 1
      }],
      yhztOptions: [{
        label: '停用',
        value: 0
      }, {
        label: '启用',
        value: 1
      }],
      kdlfsOptions: [{
        label: '全部',
        value: '1100000000'
      }, {
        label: '统一身份认证',
        value: '1000000000'
      }, {
        label: '其他身份登录',
        value: '0100000000'
      }],
      // time date
      valueDate: ''
    };
  },
  methods: {
    /**
       * 搜索查询
       */
    search() {
      const param = {
        idxm: this.listQuery.idxm,
        bmmc: this.listQuery.bmmc,
        jsmc: this.listQuery.jsmc,
        sjh: this.listQuery.sjh,
        dzyx: this.listQuery.dzyx,
        sfms: this.listQuery.sfms,
        kdlfs: this.listQuery.kdlfs,
        bz: this.listQuery.bz,
        sfzj: this.listQuery.sfzj,
        yhzt: this.listQuery.yhzt,
        cjsjkssj: '',
        cjsjjssj: '',
        bgsjkssj: '',
        bgsjjssj: ''
      };

      if (this.listQuery.cjsj) {
        param.cjsjkssj = this.listQuery.cjsj[0];
        param.cjsjjssj = this.listQuery.cjsj[1];
      }
      if (this.listQuery.bgsj) {
        param.bgsjkssj = this.listQuery.bgsj[0];
        param.bgsjjssj = this.listQuery.bgsj[1];
      }

      this.$emit('search', param);
    },
    /**
       * 重置搜索条件
       */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });

      this.$emit('search', this.listQuery);
    }
  }
};
</script>

<style lang="scss" scoped>
  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
</style>
