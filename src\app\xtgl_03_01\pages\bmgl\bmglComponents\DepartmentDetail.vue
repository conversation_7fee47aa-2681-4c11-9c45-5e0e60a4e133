<template>
  <div class="department-user" ref="element">
    <el-button type="primary" size="small" @click="buttonAction"> {{
        isEdit ? '编辑' :
          '保存'
      }}
    </el-button>
    <el-form :disabled="isEdit" label-width="150px" :model="formData"
             class="zhxy-form zhxy-form-search-part"
             :class="isEdit ? '' : 'form-status-edit'">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">部门码</span>
        <el-input class="zhxy-form-inline" v-model="formData.bmm" placeholder="部门代码" :disabled="true"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span slot="label" class="zhxy-form-label">部门名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.bmmc" placeholder="部门名称"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">是否二级部门</span>
        <el-select class="zhxy-form-inline" v-model="formData.sfejbm" placeholder="是否二级部门"
                   size="small">
          <el-option
            v-for="item in partOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">是否为保密行政管理单位</span>
        <el-select class="zhxy-form-inline"    v-model="formData.sfwbmxzgldw" placeholder="是否为保密行政管理单位"
                   size="small">
          <el-option
            v-for="item in isBmOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">所属保密行政管理单位</span>
        <el-select class="zhxy-form-inline" filterable  v-model="formData.ssbmxzgldw" placeholder="所属保密行政管理单位"
                   size="small">
          <el-option
            v-for="item in SjBmOptions"
            :key="item.bmm"
            :label="item.bmmc"
            :value="item.bmm">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh" placeholder="排序号"
                  size="small"></el-input>
      </el-form-item>

<!--      <el-form-item>-->
<!--        <span class="zhxy-form-label" slot="label">人事机构码</span>-->
<!--        <el-input class="zhxy-form-inline" v-model="formData.rsjgm" placeholder="人事机构码"-->
<!--                  size="small"></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <span class="zhxy-form-label" slot="label">本科生学院码</span>-->
<!--        <el-input class="zhxy-form-inline" v-model="formData.bksxym" placeholder="本科生学院码"-->
<!--                  size="small"></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <span class="zhxy-form-label" slot="label">研究生学院码</span>-->
<!--        <el-input class="zhxy-form-inline" v-model="formData.yjsxym" placeholder="研究生学院码"-->
<!--                  size="small"></el-input>-->
<!--      </el-form-item>-->
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'DepartmentDetail',
  props: {
    formData: {
      type: Object,
      default: () => ({
      })
    },
    isHideParent: {
      type: Boolean,
      default: true
    },
    SjBmOptions: {
      type: Array,
      default: () => ({
      })
    }
  },
  data() {
    return {
      // 所属保密行政管理单位列表数据
      // SjBmOptions: this.SjBmOptions,
      // 是否可编辑
      isEdit: true,
      // 是否二级部门 options
      partOptions: [
        {
          value: 0,
          label: '否'
        },
        {
          value: 2,
          label: '是'
        }
      ],
      isBmOptions: [
        {
          value: 0,
          label: '否'
        },
        {
          value: 1,
          label: '是'
        }
      ]
    };
  },
  methods: {
    buttonAction() {
      // 切换编辑状态
      this.isEdit = !this.isEdit;
      if (this.isEdit) {
        this.$emit('save', this.formData);
      }
    }
  }
};
</script>
<style lang="scss">
.department-user {
  .el-input.is-disabled .el-input__inner {
    background-color: #FFFFFF;
    cursor: default;
  }
}
</style>
<style lang="scss" scoped>
.department-user {
  .el-input.is-disabled .el-input__inner {
    background-color: #FFFFFF;
  }

  .zhxy-form-inline {
    width: 60%;
    min-width: 500px;
    margin-right: 0;
  }

  .zhxy-form.zhxy-form-search-part.form-status-edit {
    .el-form-item {
      margin-bottom: 20px !important;
    }
  }
}
</style>
