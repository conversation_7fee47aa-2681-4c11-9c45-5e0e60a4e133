import { MessageBox } from 'element-ui';
/**
 * 删除提示弹窗操作
 * @param val
 */
export const deletePrompt = (config = {
  text: '是否删除?', btnText: '执行中...', title: '', confirmButtonText: '确定', cancelButtonText: '取消'
}, callback) => {
  MessageBox.confirm(config.text, config.title, {
    confirmButtonText: config.confirmButtonText,
    cancelButtonText: config.cancelButtonText,
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true;
        instance.confirmButtonText = config.btnText;
        // 加载成功后的 btn loading操作
        const successDone = () => {
          instance.confirmButtonLoading = false;
          done();
        };
        callback(successDone);
      } else {
        done();
      }
    }
  })
    .then(() => {
    })
    .catch(() => {
    });
};
