<template>
  <div class="yjjbgl-content">
    <div class="yjjbgl-content-main">
      <v-title name="预警级别管理"></v-title>
      <div class="button-list qran">
        <el-button type="primary" size="small" @click="addYjjb()">新增</el-button>
      </div>
      <div class="table">
        <div class="table-content">
          <yjjbgl-table :scroller-height="scrollerHeight" :table-data="tableData" @modifyYjjb="modifyYjjb" @delYjjb="delYjjb"></yjjbgl-table>
        </div>
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>

        <!--新增修改弹窗-->
        <el-dialog
          ref="dialogEdit"
          customClass="zhxy-dialog-view"
          :visible.sync="addDialogVisible"
          width="750px"
          :title="dialogType === 'edit'?'修改预警级别':'新增预警级别'"
          :close-on-click-modal="false" @close="closeYjjb('yjjbForm')">
          <yjjbxx-edit ref="dialogEditContent" :form-data="yjjb" :yjjbm-visible="yjjbmVisible"></yjjbxx-edit>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" size="small" @click="confirmYjjb('yjjbForm')">确定</el-button>
            <el-button type="" size="small" @click="closeYjjb('yjjbForm')">取消</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import {
  findYjjbList, delYjjb, findOneAdd, addYjjb, updateYjjb
} from '@/app/xtjkyyj_01_01/api/yjjbgl/yjjbgl.js';
import YjjbglTable from './yjjbglComponents/yjjbglTable';
import YjjbxxEdit from './yjjbglComponents/yjjbxxEdit';

const defaultYjjb = {
  yjjbm: '',
  yjjbmc: '',
  txfswb: '',
  txfsArray: []
};

export default {
  name: 'jghsjygl',
  components: {
    VTitle,
    YjjbglTable,
    YjjbxxEdit
  },
  data() {
    return {
      scrollerHeight: 0,
      currentPage: 1, // 初始页
      yjjb: { ...defaultYjjb },
      tableData: [
        {
          yjjbm: '',
          yjjbmc: '',
          txfswb: '',
          txfsArray: []
        }
      ], // 列表数据集合
      yjjbmVisible: true,
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      addDialogVisible: false, // 弹窗是否可见
      dialogType: 'new', // 弹窗名称控制
      options: {
        type: Array,
        default: () => []
      }
    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 监听页面尺寸改变table size
    handleResize() {
      this.scrollerHeight = window.innerHeight - 220;
    },
    search(params) {
      const param = {
        pageSize: 30,
        page: 1
      };
      if (params) {
        Object.assign(param, params);
      }
      findYjjbList(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    reset() {
      this.listQuery.sjymc = null;
      this.search();
    },
    addYjjb() {
      this.yjjb = { ...defaultYjjb };
      this.dialogType = 'new';
      this.yjjbmVisible = true;
      this.addDialogVisible = true;
    },
    closeYjjb(formName) {
      this.$refs.dialogEditContent.$refs[formName].clearValidate();
      this.addDialogVisible = false;
    },
    modifyYjjb(row) {
      this.addDialogVisible = true;
      this.dialogType = 'edit';
      this.yjjbmVisible = false;
      Object.assign(this.yjjb, row);
    },
    delYjjb(row) {
      const yjjbm = row.yjjbm || ''; // sjyid
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            delYjjb({ yjjbm }).then((res) => {
              if (res.code === 200) {
                const index = this.tableData.findIndex((item) => item.yjjbm === yjjbm);
                if (index > -1) {
                  this.tableData.splice(index, 1);
                }
                this.$message.success('删除成功');
                this.search();
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    confirmYjjb(formName) {
      // eslint-disable-next-line consistent-return
      this.$refs.dialogEditContent.$refs.yjjbForm.validate((valid) => {
        if (valid) {
          if (this.dialogType !== 'edit') {
            const param = {
              yjjbm: this.yjjb.yjjbm
            };
            findOneAdd(param).then((res) => {
              if (Object.keys(res.data).length === 0) {
                const params = {
                  yjjbm: this.yjjb.yjjbm,
                  yjjbmc: this.yjjb.yjjbmc,
                  txfsArray: this.yjjb.txfsArray
                };
                addYjjb(params).then((result) => {
                  this.search();
                  this.$message.success('新增成功！');
                }).finally(() => {
                  this.closeYjjb(formName);
                });
              } else {
                this.$message.error('该预警级别码已存在！');
              }
            });
          } else {
            const params = {
              yjjbm: this.yjjb.yjjbm,
              yjjbmc: this.yjjb.yjjbmc,
              txfsArray: this.yjjb.txfsArray
            };
            updateYjjb(params).then((result) => {
              this.search();
              this.$message.success('修改成功！');
            }).finally(() => {
              this.closeYjjb(formName);
            });
          }
        } else {
          this.$message.error('请重新填写信息！');
          return false;
        }
      });
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page // 页码
      };
      this.search(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage // 页码
      };
      this.search(param);
    }
  }
};
</script>
<style lang="scss" scoped>
  .yjjbgl-content {
    &-main{
      background-color: #ffffff;
      padding: $page-content-padding;
    }
    .table {
      &-content {
        margin-top: $page-content-margin;
      }
    }
    .dialog-footer{
      text-align: center;
      display: flex;
      justify-content: flex-end;
    }
    .qran{
      margin-top: -2rem;
      margin-right: 1rem;
      float: right;
    }
  }
</style>
