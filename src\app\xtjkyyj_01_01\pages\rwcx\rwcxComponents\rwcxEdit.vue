<template>
  <div class="dialog-content">
    <el-form ref="rwxxForm" :model="FormData" :rules="rules" label-width="180px" label-position="right">
      <el-form-item label="程序标识" prop="cxbs">
        <el-input :disabled="!cxbsVisible" v-model="FormData.cxbs" placeholder="必填" size="small"/>
      </el-form-item>

<!--      <el-form-item prop="rwlb" label="业务系统类别" style="margin-bottom: 10px;">-->
<!--        <el-radio-group v-model="FormData.rwlb">-->
<!--          <el-radio :label=0>平台类</el-radio>-->
<!--          <el-radio :label=1>数据类</el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-form-item>-->

      <el-form-item label="程序名称" prop="cxmc">
        <el-input v-model="FormData.cxmc" placeholder="必填" maxlength="100" size="small"/>
      </el-form-item>
      <el-form-item label="报告最长间隔时间（秒）" prop="bgzcjgsj">
        <el-input v-model="FormData.bgzcjgsj" placeholder="必填" maxlength="10" size="small"/>
      </el-form-item>
      <el-form-item label="执行间隔时间（秒）" prop="zxjgsj" v-show="false">
        <el-input :disabled="!cxbsVisible" v-model="FormData.zxjgsj" placeholder="必填" maxlength="10" size="small"/>
      </el-form-item>
      <el-form-item label="运行服务器IP" prop="yxip" v-show="false">
        <el-input v-model="FormData.yxip" maxlength="20" size="small"/>
      </el-form-item>
      <el-form-item label="登录用户名" prop="dlyhm" v-show="false">
        <el-input v-model="FormData.dlyhm" maxlength="20" size="small"/>
      </el-form-item>
      <el-form-item label="登录密码" prop="dlmm" v-show="false">
        <el-input v-model="FormData.dlmm" maxlength="20" size="small"/>
      </el-form-item>
      <el-form-item label="启动命令" prop="qdml" v-show="false">
        <el-input v-model="FormData.qdml" maxlength="2000" size="small"/>
      </el-form-item>
      <el-form-item label="关闭命令" prop="gbml" v-show="false">
        <el-input v-model="FormData.gbml" maxlength="2000" size="small"/>
      </el-form-item>

    </el-form>

  </div>

</template>

<script>

export default {
  name: 'kettlerwglEdit',
  components: {
  },
  props: {
    FormData: {
      type: Object,
      default: () => ({
        rwlb: '',
        cxmc: '',
        cxbs: '',
        bgzcjgsj: '',
        zxjgsj: '',
        scbgsj: '',
        yxip: '',
        dlyhm: '',
        dlmm: '',
        qdml: '',
        gbml: ''
      })
    },
    cxbsVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        // rwlb: [{
        //   required: true,
        //   trigger: 'blur',
        //   message: '请选择任务类别'
        // }],
        cxmc: [{
          required: true,
          trigger: 'blur',
          message: '请选择程序名称'
        }],
        cxbs: [{
          required: true,
          trigger: 'blur',
          message: '请选择程序标识'
        }],
        bgzcjgsj: [{
          required: true,
          trigger: 'blur',
          message: '请选择报告最长间隔时间'
        }]
        // ,
        // zxjgsj: [{
        //   required: true,
        //   trigger: 'blur',
        //   message: '请选择执行间隔时间'
        // }]
      }
    };
  },
  mounted() {
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 20px;
  }
  .jbmc{
    width: 80%;
  }
  .zq{
    width: 150px;
  }
  .jbmcxz{
    width: 50px;
    display: inline-block;
    margin-left: 25px;
    color: #409EFF;
    cursor: pointer;
  }
  .jbmcqk{
    width: 50px;
    color: #409EFF;
    cursor: pointer;
  }
  .zhxy-form-inline{
    width: 100%;
  }
</style>
