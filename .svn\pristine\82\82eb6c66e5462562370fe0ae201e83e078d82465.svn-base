<template>
  <div class="dialog-content">
    <el-form ref="yjjsForm" :model="FormData" :rules="rules" label-width="140px" label-position="right">
      <el-form-item label="代码分类标识" prop="dmflbs">
        <el-input v-model="FormData.dmflbs" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="代码分类名称" prop="dmflmc">
        <el-input v-model="FormData.dmflmc" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="显示层级" prop="xscj">
        <el-input v-model="FormData.xscj" placeholder="选填且只能为数字" size="small" oninput="value=value.replace(/[^\d]/g,'')"/>
      </el-form-item>
      <el-form-item label="排序号" prop="pxh">
        <el-input v-model="FormData.pxh" placeholder="选填且只能为数字" size="small" oninput="value=value.replace(/[^\d]/g,'')"/>
      </el-form-item>
      <el-form-item prop="sfky" label="是否可用">
        <el-radio-group v-model="FormData.sfky">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Dmflgl',
  props: {
    FormData: {
      type: Object,
      default: () => ({
        dmflbs: '',
        dmflmc: '',
        xscj: '',
        pxh: '',
        sfky: ''
      })
    }
  },
  data() {
    return {
      yjjsVisible: false,
      rules: {
        dmflbs: [{
          required: true,
          trigger: 'blur',
          message: '请输入代码分类标识'
        },
        {
          max: 50,
          message: '代码分类标识长度不能多于50位'
        }
        ],
        dmflmc: [{
          required: true,
          trigger: 'blur',
          message: '请输入代码分类名称'
        },
        {
          max: 100,
          message: '代码分类名称长度不能多于100位'
        }
        ],
        pxh: [
          {
            max: 10,
            message: '排序号长度不能多于10位'
          }
        ],
        xscj: [
          {
            max: 5,
            message: '显示层级长度不能多于5位'
          }
        ]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 10px;
  }
</style>
