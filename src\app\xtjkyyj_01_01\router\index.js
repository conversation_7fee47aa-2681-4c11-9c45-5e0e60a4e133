const router = [
  {
    name: '预警级别管理',
    path: '/xtjkyyj/yjjbgl',
    component: () => import('../pages/yjjbgl/yjjbgl'),
    meta: { name: '预警级别管理' }
  },
  {
    name: '预警配置管理',
    path: '/xtjkyyj/yjpzgl',
    component: () => import('../pages/yjpzgl/yjpzgl'),
    meta: { name: '预警配置管理' }
  },
  {
    name: '预警信息管理',
    path: '/xtjkyyj/yjxxgl',
    component: () => import('../pages/yjxxgl/yjxxgl'),
    meta: { name: '预警信息管理' }
  },
  {
    name: '数据库管理',
    path: '/xtjkyyj/sjkgl',
    component: () => import('../pages/sjkgl/sjkgl'),
    meta: { name: '数据库管理' }
  },
  {
    name: '预警历史管理',
    path: '/xtjkyyj/yjlsgl',
    component: () => import('../pages/yjlsgl/yjlsgl'),
    meta: { name: '预警历史管理' }
  },
  {
    name: '服务器管理',
    path: '/xtjkyyj/fwqgl',
    component: () => import('../pages/fwqgl/fwqgl'),
    meta: { name: '服务器管理' }
  },
  {
    name: '业务系统监测',
    path: '/xtjkyyj/rwcx',
    component: () => import('../pages/rwcx/rwcx'),
    meta: { name: '业务系统监测' }
  },
  {
    name: '中间件监测',
    path: '/xtjkyyj/zjjjc',
    component: () => import('../pages/zjjjc/index'),
    meta: { name: '中间件监测' }
  },
  {
    name: '应用系统监测',
    path: '/xtjkyyj/ywjc',
    component: () => import('../pages/ywjc/index'),
    meta: { name: '应用系统监测' }
  }
];

export default router;
