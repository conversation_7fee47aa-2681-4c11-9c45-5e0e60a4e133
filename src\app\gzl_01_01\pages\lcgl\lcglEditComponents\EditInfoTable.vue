<template>
  <div class="info-edit">
    <el-form
      ref="form"
      label-width="150px"
      :model="formData"
      class="zhxy-form zhxy-form-search-part"
      :rules="rules"
    >
      <el-form-item prop="hjmc">
        <span class="zhxy-form-label" slot="label">环节名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.hjmc" placeholder="环节名称"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh" placeholder="排序号"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">帮助信息</span>
        <el-input class="zhxy-form-inline" v-model="formData.bzxx" placeholder="帮助信息"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item prop="sfky">
        <span class="zhxy-form-label" slot="label">是否可用</span>
        <el-select class="zhxy-form-inline" v-model="formData.sfky" placeholder="是否可用"
                   size="small">
          <el-option
            v-for="item in sfkyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'LcglEditInfoEdit',
  props: {
    formData: {
      type: Object,
      default: () => ({
        Fwdyid: '', // 服务ID
        Hjid: '', // 环节ID
        Hjmc: '', // 环节名称
        Mbhj: '', // 目标环节
        Bzxx: '', // 帮助信息
        Pxh: '', // 排序号
        Sfky: 1, // 是否可用
        Cjr: '', // 创建人
        Cjsj: '', // 创建时间
        bgr: '', // 变更人
        Bgsj: '' // 变更时间
      })
    }
  },
  data() {
    return {
      // 服务类型 options
      sfkyOptions: [
        {
          label: '可用',
          value: 1
        },
        {
          label: '禁用',
          value: 2
        }
      ],
      // form rules
      rules: {
        hjmc: [
          {
            required: true,
            message: '环节名称不可为空',
            trigger: 'blur'
          }
        ],
        sfky: [
          {
            required: true,
            message: '状态不可为空',
            trigger: 'blur'
          }
        ]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .zhxy-form-inline {
    width: 100%;
  }

  .zhxy-form-search-part .el-form-item {
    margin-bottom: 2*$page-content-padding !important;
  }

  .info-edit {
    padding-right: 2*$page-content-padding
  }
</style>
