<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">预警类别</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.yjlb" placeholder="请选择预警类别" size="small">
          <el-option
            v-for="item in yjlbOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">预警分类</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.yjfl" placeholder="请选择预警分类" size="small">
          <el-option
            v-for="item in yjflOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">预警级别</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.yjjbm" placeholder="请选择预警级别" size="small">
          <el-option
            v-for="item in yjjbOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">处理状态</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.sfhl" placeholder="请选择处理状态" size="small">
          <el-option
            v-for="item in sfhlOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">生成时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.scsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">预警名称</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.yjmc" placeholder="请输入内容" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()" size="small"  style="margin-left: 15px;">查询</el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  findYjlb, findYjfl, findYjjb
} from '@/app/xtjkyyj_01_01/api/yjlsgl/yjlsgl.js';
import dayjs from 'dayjs';

export default {
  name: 'YjlsglSearch',
  data() {
    return {
      isShowLabel: false,
      // label 宽度
      lableWidth: '85px',
      // labelWidth
      lableWidthSingle: '415px',
      // search params
      listQuery: {
        yjlb: null,
        yjfl: null,
        yjjbm: null,
        sfhl: null,
        scsjq: null,
        scsjz: null,
        yjmc: null,
        yjbs: null,
        glywid: null
      },
      yjlbOptions: [{ label: '全部', value: null }],
      yjflOptions: [{ label: '全部', value: null }],
      yjjbOptions: [{ label: '全部', value: null }],
      sfhlOptions: [
        {
          label: '全部',
          value: null
        }, {
          label: '未处理',
          value: 0
        },
        {
          label: '已忽略',
          value: 1
        }
      ]
    };
  },
  mounted() {
    if (undefined === this.$route.query.yjbs) {
      this.listQuery.yjbs = null;
    } else {
      this.listQuery.yjbs = this.$route.query.yjbs;
    }
    if (undefined === this.$route.query.glywid) {
      this.listQuery.glywid = null;
    } else {
      this.listQuery.glywid = this.$route.query.glywid;
    }
    const param = {
      yjlb: this.listQuery.yjlb,
      yjfl: this.listQuery.yjfl,
      sfhl: this.listQuery.sfhl,
      yjjbm: this.listQuery.yjjbm,
      yjmc: this.listQuery.yjmc,
      scsjq: '',
      scsjz: '',
      yjbs: this.listQuery.yjbs,
      glywid: this.listQuery.glywid
    };
    if (this.listQuery.scsj) {
      param.scsjq = dayjs(this.listQuery.scsj[0]).format('YYYY-MM-DD HH:mm:ss');
      param.scsjz = dayjs(this.listQuery.scsj[1]).format('YYYY-MM-DD HH:mm:ss');
    }
    this.$emit('search', param);

    this.findYjlb();
    this.findYjfl();
    this.findYjjb();
  },
  methods: {
    dayjs,
    findYjlb() {
      findYjlb().then((res) => {
        if (res.code === 200) {
          this.yjlbOptions = [];
          this.yjlbOptions = res.data.content;
          const qb = { label: '全部', value: null };
          this.yjlbOptions.unshift(qb);
        }
      });
    },
    findYjfl() {
      findYjfl().then((res) => {
        if (res.code === 200) {
          this.yjflOptions = [];
          this.yjflOptions = res.data.content;
          const qb = { label: '全部', value: null };
          this.yjflOptions.unshift(qb);
        }
      });
    },
    findYjjb() {
      findYjjb().then((res) => {
        if (res.code === 200) {
          this.yjjbOptions = [];
          this.yjjbOptions = res.data.content;
          const qb = { label: '全部', value: null };
          this.yjjbOptions.unshift(qb);
        }
      });
    },
    search() {
      const param = {
        yjlb: this.listQuery.yjlb,
        yjfl: this.listQuery.yjfl,
        sfhl: this.listQuery.sfhl,
        yjjbm: this.listQuery.yjjbm,
        yjmc: this.listQuery.yjmc,
        scsjq: '',
        scsjz: '',
        yjbs: '',
        glywid: ''
      };
      if (this.listQuery.scsj) {
        param.scsjq = dayjs(this.listQuery.scsj[0]).format('YYYY-MM-DD HH:mm:ss');
        param.scsjz = dayjs(this.listQuery.scsj[1]).format('YYYY-MM-DD HH:mm:ss');
      }
      this.$emit('search', param);
    },
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = null;
        });
      this.$emit('search', this.listQuery);
    }
  }
};
</script>

<style lang="scss" scoped>
  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
</style>
