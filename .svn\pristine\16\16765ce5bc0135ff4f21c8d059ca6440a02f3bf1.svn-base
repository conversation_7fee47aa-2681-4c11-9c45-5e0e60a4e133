<template>
  <div style="height: calc(100% - 200px)">
    <el-table
      :data="tableData"
      stripe
      border
      class="zhxy-table"
      @selection-change="handleSelectionChange"
      height="calc(100% - 2px)">
      <el-table-column align="center" type="selection" width="50"></el-table-column>
      <el-table-column prop="bmm" label="部门码" width="100"></el-table-column>
      <el-table-column prop="bmmc" label="部门名称" width="100"></el-table-column>
      <el-table-column prop="sfejbm" label="是否二级部门" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.sfejbm == 2">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="sfwbmxzgldw" label="是否为保密行政管理单位" width="180">
        <template slot-scope="scope">
          <span>{{scope.row.sfwbmxzgldw == 1 ? '是' : '否'}}</span>
        </template>
      </el-table-column>
<!--      <el-table-column prop="rsjgm" label="人事机构码" width="100"></el-table-column>-->
<!--      <el-table-column prop="bksxym" label="本科生学院码" width="130"></el-table-column>-->
<!--      <el-table-column prop="yjsxym" label="研究生学院码" width="130"></el-table-column>-->
      <el-table-column prop="bgrxm" label="变更人" width="100"></el-table-column>
      <el-table-column prop="bgsj" label="变更时间" width="100"></el-table-column>
      <el-table-column prop="" label="操作" width="100">
        <template slot-scope="scope">
<!--          <el-button size="small" @click="updateXjbm(scope.row)" type="text">修改</el-button>
          <i style="color: #e8eaec;"> | </i>-->
          <el-button size="small" type="text" @click="delXjbm(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pageIn">
      <el-pagination
        :page-size="pageSize"
        @size-change="departmentSubPageSizeChange"
        @current-change="departmentSubCurrentPage"
        :page-sizes="[30, 50, 100, 200,500]"
        :total=total
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DepartmentSub',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    page: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 30
    }
  },
  data() {
    return {
      // table select array
      multipleSelection: []
    };
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /**
     * 部门用户 pagesize change
     * @param val
     */
    departmentSubPageSizeChange(val) {
      this.$emit('departmentSubPageSizeChange', val);
    },
    /**
     * 部门用户 当前page
     * @param val
     */
    departmentSubCurrentPage(val) {
      this.$emit('departmentSubCurrentPage', val);
    },
    // updateXjbm(val) {
    //   this.$emit('updateSonDepartment', val);
    // },
    delXjbm(val) {
      this.$emit('delSonDepartment', val);
    }
  }
};
</script>

<style scoped>

</style>
