<template>
  <div>
    <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
      <el-table-column prop="xh" label="序号" width="60" ></el-table-column>
      <el-table-column prop="yjjbm" label="预警级别码" width="300"></el-table-column>
      <el-table-column prop="yjjbmc" label="预警级别名称" width="350" ></el-table-column>
      <el-table-column prop="txfswb" label="提醒方式" ></el-table-column>
      <el-table-column prop="txsf" label="提醒方式" width="200" v-if="false"></el-table-column>
      <el-table-column fixed="right" prop="" label="操作" width="140">
        <template slot-scope="scope">
          <el-button size="small" @click="modifyYjjb(scope.row)" type="text">修改</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" type="text" @click="delYjjb(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'YjjbglTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
    };
  },
  methods: {
    modifyYjjb(val) {
      this.$emit('modifyYjjb', val);
    },
    delYjjb(val) {
      this.$emit('delYjjb', val);
    }
  }
};
</script>

<style scoped>

</style>
