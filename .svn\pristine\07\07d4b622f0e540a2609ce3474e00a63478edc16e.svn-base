<template>
  <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
    <el-table-column prop="xh" label="序号" width="60"></el-table-column>

<!--    <el-table-column prop="zjbh" label="主机编号" width="150"></el-table-column>-->
    <el-table-column prop="" label="主机类型" width="120">
      <template slot-scope="scope">
        <span v-if="scope.row.zjlx === '1'">管理服务器</span>
        <span v-else-if="scope.row.zjlx === '2' ">文件服务器</span>
      </template>
    </el-table-column>
    <el-table-column prop="zjip" label="主机IP" width="150"></el-table-column>
    <el-table-column prop="syywxt" label="程序名称" ></el-table-column>
<!--    <el-table-column prop="zjyt" label="主机用途" width="200"></el-table-column>-->
    <el-table-column prop="czxt" label="操作系统" width="120"></el-table-column>
<!--    <el-table-column prop="yhm" label="用户名" width="120"></el-table-column>-->
<!--    <el-table-column prop="mm" label="密码" width="120"></el-table-column>-->
    <el-table-column prop="" label="可用状态" width="100">
      <template slot-scope="scope">
        <span v-if="scope.row.kyzt === 1 ">正常</span>
        <span v-else-if="scope.row.kyzt === 0 ">异常</span>
      </template>
    </el-table-column>
    <el-table-column prop="bgsj" label="变更时间" width="180">
      <template slot-scope="scope">
        <div v-if="scope.row.bgsj != '' && scope.row.bgsj != null">
          {{dayjs(scope.row.bgsj).format('YYYY-MM-DD HH:mm:ss')}}
        </div>
      </template>
    </el-table-column>

<!--    <el-table-column prop="fzr" label="负责人" width="150"></el-table-column>-->

    <el-table-column fixed="right" prop="" label="操作" width="180">
      <template slot-scope="scope">
<!--        <el-button size="small" @click="updateCjrwgz(scope.row)" type="text">修改</el-button>
        <i style="color: #e8eaec;"> | </i>-->
        <el-button size="small" @click="Tbjl(scope.row)" type="text">主机监测信息</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" type="text" @click="delCjrwgz(scope.row)">删除</el-button>
      </template>
    </el-table-column>

  </el-table>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'CjrwgzTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dayjs
    };
  },
  methods: {
    updateCjrwgz(val) {
      this.$emit('updateCjrwgz', val);
    },
    delCjrwgz(val) {
      this.$emit('delCjrwgz', val);
    },
    Tbjl(val) {
      this.$emit('Tbjl', val);
    }
  }
};
</script>
