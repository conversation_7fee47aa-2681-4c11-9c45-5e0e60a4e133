<template>
  <div ref="myChart" style="width: 100%;height: 100%;box-sizing: border-box;"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/bar');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  props: {
    data: {
      type: Object
    },
    color: {
      type: String,
      default() {
        return '#3398DB';
      }
    }
  },
  data() {
    return {};
  },
  computed: {},
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);

      const data = this.data;

      const hexToRgba = (hex, opacity) => {
        let rgbaColor = '';
        const reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
          // eslint-disable-next-line radix
          rgbaColor = `rgba(${parseInt(`0x${ hex.slice(1, 3)}`)},${parseInt(
            `0x${ hex.slice(3, 5)}`
            // eslint-disable-next-line radix
          )},${parseInt(`0x${ hex.slice(5, 7)}`)},${opacity})`;
        }
        return rgbaColor;
      };

      // 数据整理
      const xData = this.data.xData;
      const yData = this.data.yData;
      const max = Math.max(yData);
      const labelColor = ['#FD5360', '#FF962B', '#FFAA00'];
      const emptyData = yData.map((v, i) => {
        const color = i > 2 ? '#1890FF' : labelColor[i];
        const item = {
          value: max,
          label: {
            formatter: `{a|${ v }}`,
            position: 'right',
            distance: 20,
            rich: {
              a: {
                color,
                borderColor: color,
                borderWidth: 1,
                borderType: 'dashed',
                padding: [0, 0, 2, 0],
                width: 60,
                height: 18,
                align: 'center',
                verticalAlign: 'middle',
                backgroundColor: hexToRgba(color, 0.05)
              }
            }

          }
        };
        return item;
      });
      const xDataFormat = xData.map((v, i) => {
        const color = i > 2 ? '#333333' : labelColor[i];
        const item = {
          value: v,
          textStyle: {
            rich: {
              a: {
                color,
                width: 20,
                height: 20,
                align: 'center',
                verticalAlign: 'middle',
                backgroundColor: '#fff',
                borderRadius: 10,
                borderColor: hexToRgba(color, 0.2),
                borderWidth: 1,
                shadowColor: hexToRgba(color, 0.1),
                shadowBlur: 5
              },
              b: {
                padding: [0, 5]
              },
              value: {
                color: '#666666'
              }
            }
          }
        };
        return item;
      });
      xData.reverse();
      xDataFormat.reverse();
      yData.reverse();
      emptyData.reverse();

      const option = {
        backgroundColor: '#fff',
        grid: {
          top: '5%',
          left: '5%',
          right: '8%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'value',
          splitLine: {
            show: false
          },
          max(value) {
            return value.max;
          },
          axisLine: {
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          axisLabel: {
            color: '#666'
          }
        }],
        yAxis: [{
          type: 'category',
          name: '',
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          axisLabel: {
            color: '#666'
            // formatter: function(value) {
            //     return '{a|' + value.substr(value.length - 1) + '}{b|}{value|' + value + '}'
            // }
          },
          data: xDataFormat
        }, {
          type: 'category',
          name: '',
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          data: xData
        }],
        series: [{
          type: 'bar',
          barWidth: '50%',
          yAxisIndex: 1,
          itemStyle: {
            normal: {
              // barBorderRadius: [0, 6, 6, 0],
              color: 'rgba(225,225,225,0.4)'
            }
          },
          label: {
            show: true,
            position: 'right',
            formatter(a) {
              console.log(a);
            }
          },
          data: emptyData
        },
        {
          type: 'bar',
          barWidth: '50%',
          zlevel: 1,
          itemStyle: {
            normal: {
              // barBorderRadius: [0, 6, 6, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [{
                offset: 0,
                color: '#3D9FFF'
              }, {
                offset: 1,
                color: '#41D7F3'
              }], false)
            }
          },
          data: yData
        }
        ]
      };

      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
