<template>
  <div style="height: calc(100% - 50px)">
    <el-table
      :data="tableData"
      stripe
      border
      class="zhxy-table"
      @selection-change="selectionChange"
      height="calc(100% - 40px)">
      <el-table-column align="center" type="selection"></el-table-column>
      <el-table-column align="center" type="index" label="序号" width="52"></el-table-column>
      <el-table-column prop="id" width="130" v-if=false></el-table-column>
      <el-table-column prop="jsmc" label="所属角色名称" width="130"></el-table-column>
      <el-table-column prop="cjr" label="创建人" width=""></el-table-column>
      <el-table-column prop="cjsj" label="创建时间" width="">
        <template slot-scope="scope">
          {{scope.row.cjsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column prop="bgr" label="变更人" width=""></el-table-column>
      <el-table-column prop="bgsj" label="变更时间" width="">
        <template slot-scope="scope">
          {{scope.row.bgsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="50">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="delJs(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pageIn">
      <el-pagination
        size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[30, 50, 100, 200,500]"
        layout="total, sizes, prev, pager, next, jumper"
        :total=total>
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { dateFilter } from '@/utils/date-utils';
import { delPlZyjs, delZyjs, findListByJsPage } from '../../../api/zygl/zygl';

export default {
  name: 'DepartmentUser',
  filters: {
    dateFilter
  },
  data() {
    return {
      // table pageSize page
      pageSize: 30,
      page: 1,
      // 功能资源id
      xzgnzyid: '',
      // 角色资源数据
      tableData: [],
      // 数据总量
      total: 0,
      // 勾选
      selections: ''
    };
  },
  methods: {
    action(params) {
    },
    // 资源角色每页数量
    handleSizeChange(val) {
      this.pageSize = val;
      this.getGmjs();
    },
    // 分页
    handleCurrentChange(val) {
      this.page = val;
      this.getGmjs();
    },
    // 资源角色
    getGmjs() {
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        gnzyid: this.xzgnzyid
      };
      /**
       * 获取 资源角色 接口
       */
      findListByJsPage(param).then((res) => {
        this.tableData = res.data.content;
        this.total = res.data.pageInfo.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    /**
     * 删除 角色资源 接口
     */
    delJs(data) {
      this.$confirm('请确认删除该资源角色？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteZymxCurrent(data, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 删除 角色资源 接口
     */
    deleteZymxCurrent(data, instance, done) {
      const param = {
        id: data.id
      };
      delZyjs(param).then((res) => {
        this.getGmjs();
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    // 勾选资源明细
    selectionChange(selection) {
      this.selections = selection;
    },
    // 批量删除角色资源
    deleteJsyh() {
      if (this.selections === '') {
        this.$message.success('请勾选角色资源');
      } else {
        const arr = [];
        this.selections.forEach((item) => {
          const itemparam = {
            id: item.id
          };
          arr.push(itemparam);
        });
        const param = {
          idarr: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },
    /**
     * 批量删除 角色资源 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      delPlZyjs(param).then((res) => {
        this.getGmjs();
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    modifyRole(val) {
      this.$emit('modifyUser', val);
    }
  }
};
</script>

<style lang="scss" scoped>
  .button-tab {
    margin-bottom: $page-content-padding;
  }

  .department-user {
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }

    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
