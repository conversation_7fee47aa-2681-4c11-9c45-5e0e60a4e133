<template>
  <div class="info-table">
    <el-table
      class="zhxy-table"
      :data="tableData"
      stripe
      border
      @row-click="getTargetCurrent"
      height="100%">
      <el-table-column prop="qshj" label="起始环节" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="mbhj" label="目标环节" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="bllx" label="办理类型" width="150">
        <template v-if="scope.row.mbhjid !=-1" slot-scope="scope">
          <span v-if="!scope.row.bllx"><el-button @click="selectHandleType(scope.row)" type="text">请选择办理类型</el-button></span>
          <span v-else class="button-text" @click="selectHandleType(scope.row)">{{bllxGet(scope.row.bllx)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理目标" show-overflow-tooltip>
        <template v-if="scope.row.mbhjid !=-1" slot-scope="scope">
          <span v-if="!scope.row.clmb"><el-button @click="selectHandleTarget(scope.row)" type="text">请选择处理目标</el-button></span>
          <span style="text-overflow: ellipsis;white-space: nowrap" @click="selectHandleTarget(scope.row)" class="button-text" v-else>{{scope.row.clmb}}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="" label="操作" width="50">
        <template slot-scope="scope">
          <el-button size="small" @click="deleteTarget(scope.row)" type="text">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

</template>

<script>
export default {
  props: {
    // table 数据
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  computed: {
    // 办理类型 解析
    bllxGet() {
      const json = {
        1: '单一签核',
        2: '多人单一签核',
        3: '多人全部处理'
      };
      return (val) => {
        if (val) {
          return json[val];
        }
        return '';
      };
    }
  },
  methods: {
    /**
     * 选择办理类型 按钮事件
     * @param val
     */
    selectHandleType(val) {
      this.$emit('selectHandleType', val);
    },
    /**
     * 选择处理目标 按钮事件
     * @param val
     */
    selectHandleTarget(val) {
      this.$emit('selectHandleTarget', val);
    },
    /**
     * 删除 目标环节
     * @param val
     */
    deleteTarget(val) {
      this.$emit('deleteTarget', val);
    },
    /**
     * 获取目标信息数据
     * @param row
     */
    getTargetCurrent(row) {
      this.$emit('getTargetData', row);
    }
  }
};
</script>

<style lang="scss" scoped>
  .info-table {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    .button-text{
      cursor: pointer;
      color: $page-font-hover-color;
    }
  }
</style>
