import { createAPI } from '@/utils/request.js';

const BASE_URL = '';

/* 查询列表数据 */
export const findList = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findList`, 'get', data);

/* 查询单条数据 */
export const findOne = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findOne`, 'get', data);

/* 新增 */
export const add = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/add`, 'post', data);

/* 校验 */
export const select = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/select`, 'post', data);

/* 新增角色资源 */
export const addJszy = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/addJszy`, 'post', data);

/* 修改 */
export const update = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/update`, 'post', data);

/* 删除 */
export const del = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/delete`, 'post', data);

/* 获取角色下的角色数量 */
export const findCountZycdjs = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findCountZycdjs`, 'get', data);

/* 获取所有资源列表 */
export const findYyzylistAll = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findYyzylistAll`, 'get', data);

/* 获取角色下资源列表 */
export const findCkeckedYyzy = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findCkeckedYyzy`, 'get', data);

/* 修改功能资源信息 */
export const updateJsxzy = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/updateJsxzy`, 'post', data);

/* 获取角色下用户列表 */
export const findJsxyhlist = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findJsxyhlist`, 'get', data);

/* 获取用户列表 */
export const findYhList = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findYhList`, 'get', data);

/* 新增角色用户 */
export const addJsyh = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/addJsyh`, 'post', data);

/* 删除角色用户 */
export const delJsyh = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/delJsyh`, 'post', data);

/* 批量删除角色用户 */
export const delPlJsyh = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/delPlJsyh`, 'post', data);

/* 获取用户角色明细列表 */
export const findYhjsmxList = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findYhjsmxList`, 'get', data);

/* 获取部门列表 */
export const findBmlist = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findBmlist`, 'get', data);

/* 获取部门列表 */
export const findBmlistAll = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findBmlistAll`, 'get', data);

/* 获取部门用户列表 */
export const findBmYhlist = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findBmYhlist`, 'get', data);

/* 用户是否在相应部门 */
export const findYhbmCount = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/findYhbmCount`, 'get', data);

/* 添加用户角色明细 */
export const addYhjsmx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/addYhjsmx`, 'post', data);

/* 删除用户角色明细 */
export const deleteYhjsmx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/deleteYhjsmx`, 'post', data);

/* 修改角色父节点 */
export const updateFjsid = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJs/updateFjsid`, 'post', data);
