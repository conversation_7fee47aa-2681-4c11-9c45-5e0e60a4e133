import { mapMutations, mapState } from 'vuex';
import { cloneDeep } from 'lodash';
// 用户信息 store 存储路径
const STORE_LAYOUT = 'layout_03_01';
// 缓存list temp
let includeMap = [];
// 缓存组件个数
const CACHE_MAX_NUM = 10;

/**
 * 获取组件的default name
 * @param params 路由router Object
 * @returns {*}
 */
const getComponentName = (params) => {
  const arr = params.path.split('/');
  arr.splice(0, 1);
  // 获取组件的name
  return arr.join('-');
};
export const setKeepAliveMixins = {
  data() {
    return {
    };
  },
  watch: {
    // 检测router 变更
    $route: {
      handler(to) {
        if (to) {
          // 判断 组件是否需要缓存: 如果 router.meta.isKeep 不存在 不会对组件进行缓存操作
          if (!to.meta || !to.meta.isKeep) {
            return;
          }
          // 获取组件的name
          const componentsName = getComponentName(to);
          includeMap = cloneDeep(this.keepaliveList);
          // 获取缓存列表中页面所在的位置
          const index = this.getKeepList.findIndex((x) => x === componentsName);
          if (index === -1) {
            // 如果缓存中没有 add
            includeMap.push({ label: componentsName, value: to.fullPath });
            // 如果超出了缓存数量限制 去除缓存最旧的
            if (includeMap.length > CACHE_MAX_NUM) {
              includeMap.splice(0, 1);
            }
            this.changeKeepaliveList(cloneDeep(includeMap));
          } else if (includeMap[index].value !== to.fullPath && index > -1) { // 如果缓存中存在 并且fullPath不同 就去更新
            includeMap.splice(index, 1);
            this.changeKeepaliveList(cloneDeep(includeMap));
            this.reload({ label: componentsName, value: to.fullPath });
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(`${STORE_LAYOUT}/tagView`, ['keepaliveList', 'isReloadComponent']),
    // 缓存组件的列表 Array
    getKeepList() {
      const include = [];
      console.log(this.keepaliveList);
      this.keepaliveList.forEach((item) => {
        include.push(item.label);
      });
      return include;
    }
  },
  methods: {
    ...mapMutations(`${STORE_LAYOUT}/tagView`, ['changeKeepaliveList', 'changeReloadComponent']),
    // 组件reload 刷新
    reload(params) {
      // 设置 router-view v-if
      this.changeReloadComponent(false);
      this.$nextTick(() => {
        this.changeReloadComponent(true);
        includeMap.push(params);
        this.changeKeepaliveList(cloneDeep(includeMap));
      });
    }
  }
};

// 缓存操作方法
export const cacheActionMixins = {
  data() {
    return {
    };
  },
  computed: {
    ...mapState(`${STORE_LAYOUT}/tagView`, ['keepaliveList', 'isReloadComponent'])
  },
  methods: {
    ...mapMutations(`${STORE_LAYOUT}/tagView`, ['changeKeepaliveList', 'changeReloadComponent']),
    /**
     * 组件reload 刷新
     */
    reloadCurrent() {
      // 设置 router-view v-if
      this.changeReloadComponent(false);
      this.$nextTick(() => {
        this.changeReloadComponent(true);
      });
    },
    /**
     * 将当前组件从缓存中移除
     * @param to router object
     */
    deleteCurrentCache(to) {
      const componentsName = getComponentName(to);
      const tempArr = cloneDeep(this.keepaliveList);
      const newCache = tempArr.filter((x) => x.label !== componentsName);
      this.changeKeepaliveList(newCache);
    },
    /**
     * 将组件添加到缓存中
     * @param to router object
     */
    addCurrentCache(to) {
      const componentsName = getComponentName(to);
      const tempArr = cloneDeep(this.keepaliveList);
      const index = tempArr.findIndex((x) => x === componentsName);
      if (index === -1) {
        // 如果缓存中没有 add
        tempArr.push({ label: componentsName, value: to.fullPath });
        // 如果超出了缓存数量限制 去除缓存最旧的
        if (tempArr.length > CACHE_MAX_NUM) {
          tempArr.splice(0, 1);
        }
        this.changeKeepaliveList(cloneDeep(tempArr));
      } else { // 如果缓存中存在 并且fullPath不同 就去更新
        tempArr[index] = { label: componentsName, value: to.fullPath };
      }
      this.changeKeepaliveList(cloneDeep(tempArr));
    }
  }
};
