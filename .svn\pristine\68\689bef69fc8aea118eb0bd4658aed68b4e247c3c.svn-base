<template>
  <div class="dialog-content">
    <el-form ref="yjjsForm" :model="FormData" :rules="rules" label-width="140px" label-position="right">
      <el-form-item v-if="yjjsVisible" label="父角色ID" prop="fjsid">
        <el-input disabled :value="yjjsFjsid" v-model="yjjsFjsid" placeholder="父角色ID" size="small"/>
      </el-form-item>
      <el-form-item label="角色ID" prop="jsid">
        <el-input v-model="FormData.jsid" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="角色名称" prop="jsmc">
        <el-input v-model="FormData.jsmc" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item prop="xm" label="角色类型">
        <el-radio-group v-model="FormData.jslx">
          <el-radio :label="1">固化角色</el-radio>
          <el-radio :label="2">自建角色</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序号" prop="pxh">
        <el-input v-model="FormData.pxh" placeholder="选填且只能为数字" size="small" oninput="value=value.replace(/[^\d]/g,'')"/>
      </el-form-item>
      <el-form-item label="工作流岗位同步方式" prop="gzlgwtbfs">
        <el-input v-model="FormData.gzlgwtbfs" placeholder="选填" size="small"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Jsgl',
  props: {
    FormData: {
      type: Object,
      default: () => ({
        fjsid: '',
        jsid: '',
        jsmc: '',
        jslx: '',
        pxh: '',
        gzlgwtbfs: ''
      })
    }
  },
  data() {
    return {
      yjjsFjsid: '',
      yjjsVisible: false,
      rules: {
        jsid: [{
          required: true,
          trigger: 'blur',
          message: '请输入角色id'
        },
        {
          max: 20,
          message: '角色id长度不能多于20位'
        }
        ],
        jsmc: [{
          required: true,
          trigger: 'blur',
          message: '请输入角色名称'
        },
        {
          max: 32,
          message: '角色名称长度不能多于32位'
        }
        ],
        jslx: [{
          required: true,
          trigger: 'blur',
          message: '请选择角色类型'
        }
        ],
        pxh: [
          {
            max: 5,
            message: '排序号长度不能多于5位'
          }
        ],
        gzlgwtbfs: [
          {
            max: 20,
            message: '工作流岗位同步方式长度不能多于20位'
          }
        ]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 10px;
  }
</style>
