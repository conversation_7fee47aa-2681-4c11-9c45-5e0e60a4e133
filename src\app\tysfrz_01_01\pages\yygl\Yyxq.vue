<template>
  <div>
    <!--    应用基本信息修改 弹窗-->
    <el-dialog title="应用基本信息修改" :visible.sync="editFormVisible" :close-on-click-modal="false" @close="formeditClose">
      <el-form :model="formeditTemp" :rules="rulesedit" ref="formedit">
        <el-form-item  label="系统ID" :label-width="formLabelWidth" prop="yyid">
          <el-input disabled size="small" v-model="formeditTemp.yyid" placeholder="必填"></el-input>
        </el-form-item>
        <el-form-item label="系统名称" :label-width="formLabelWidth" prop="yymc">
          <el-input size="small" v-model="formeditTemp.yymc" placeholder="必填"></el-input>
        </el-form-item>
        <el-form-item label="所属分类" :label-width="formLabelWidth" prop="flid">
          <el-select style="width: 100%" v-model="formeditTemp.flid" placeholder="请选择" size="small">
            <el-option v-for="item in editoptions" :key="item.dmz" :label="item.dmmc"
                       :value="item.dmz"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="秘钥" :label-width="formLabelWidth" prop="yymy">
          <el-input size="small" v-model="formeditTemp.yymy"></el-input>
        </el-form-item>
        <el-form-item label="认证方式" :label-width="formLabelWidth" prop="checkList">
          <el-checkbox-group v-model="formeditTemp.checkList" prop="checkList">
            <el-checkbox label="authorization_code">授权码</el-checkbox>
            <el-checkbox label="password">账号密码</el-checkbox>
          </el-checkbox-group>
          <!--          <el-input size="small" v-model="form.sqfs"  autocomplete="off"></el-input>-->
        </el-form-item>
        <el-form-item v-if="formeditTemp.checkList && formeditTemp.checkList.includes('authorization_code')" label="回调地址:" :label-width="formLabelWidth" prop="hddz">
          <el-input size="small" v-model="formeditTemp.hddz" autocomplete="off"></el-input>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="formLoading" size="small" type="primary" @click="editsubmitForm">确 定</el-button>
        <el-button size="small" @click="editFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 群组信息弹窗-->
    <el-dialog title="群组信息" :visible.sync="qzmessageVisible" :close-on-click-modal="false">
      <div v-loading="groupTreeLoading" style="min-height: 400px;overflow: auto">
        <el-tree
          show-checkbox
          :default-checked-keys="checkedList"
          :data="groupTree"
          ref="groupMessageTree"
          node-key="qzid"
          :props="{
            label:'qzmc',
            id:'qzid',
            children:'children'
          }">
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="groupSubmit">确定</el-button>
        <el-button size="small" @click="qzmessageVisible = false">取消</el-button>
      </div>
    </el-dialog>

    <!-- todo 用户信息弹窗-->
    <el-dialog title="用户信息" :visible.sync="outerVisible">
      <yyxq-yhxx-edit
        v-loading="userTabelDataLoading"
        ref="userShowTable"
        :is-add="true"
        @addTabel="addTabel"
        @search="searchUser"
        @reset="resetUser"
        @changePageSize="changUserPageSize"
        @changePage="changUserPage"
        @deleteTarget="deleteTarget"
        :page="userPage"
        :page-size="userPageSize"
        :total="userShowTotal"
        :table-data="userTabelData">
      </yyxq-yhxx-edit>
      <el-dialog width="1000px"
                 title="新增用户"
                 :visible.sync="innerVisible"
                 append-to-body>
        <yyxq-yhxx-edit
          v-loading="userTabelDataLoading"
          ref="userAddTable"
          @changePageSize="changUserPageSize"
          @changePage="changUserPage"
          @search="searchUser"
          @reset="resetUser"
          :page="userAddPage"
          :page-size="userAddPageSize"
          :total="userAddTotal"
          :table-data="userAddTabelData">
        </yyxq-yhxx-edit>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="userAddCertainLoading" size="small" type="primary" @click="userAddCertain">确定</el-button>
          <el-button size="small" @click="outerVisible = false;innerVisible = false">取消</el-button>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="outerVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!--新增IP地址-->
    <el-dialog title="新增IP地址" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="IP地址" :label-width="formLabelWidth" prop="IP">
          <el-input size="small" v-model="form.IP" placeholder="必填"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
        <el-button :loading="formLoading" size="small" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <section class="jksqgl-page">
      <v-title name="应用详情"></v-title>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane v-loading="baseLoading" name="first" label="基本信息">
          <div>
            <!--基本信息编辑 ok-->
            <yyxq-jbxx @editForm="editForm" :edit-base-data="editBaseData"></yyxq-jbxx>
            <!--            可访问授权-->
            <div>
              <div class="jksqgl-pageform-header">
                <span class="zhxy-form-label">可访问授权</span>
              </div>
              <div class="qzmessage">
                <p class="qzmessage-title">群组信息</p>
                <div class="qzmessage-sj" @click="groupMessageShow">{{ count.qzcount }}</div>
              </div>

              <div class="qzmessage">
                <p class="qzmessage-title">用户信息</p>
                <div class="qzmessage-sj" @click="userMessageShow">{{ yhcount.yhcount }}</div>
              </div>
            </div>
            <!--IP列表-->
            <div>
              <div class="jksqgl-pageform-header">
                <span class="zhxy-form-label">IP列表</span>
                <el-button type="primary" size="small" @click="showDialog">新增</el-button>
              </div>
              <yyxq-iplb :tableData="tableData" @deleteIP="deleteIP" @changeIPStatus="changeIPStatus"></yyxq-iplb>
            </div>

          </div>
        </el-tab-pane>
        <el-tab-pane name="second" label="认证日志" style="background-color: #FFFFFF">
          <!--调用接口组件-->
          <YyxqRz :log-info="baseData"></YyxqRz>
          <!--认证日志查询table组件-->
          <rzrz-search
            ref="searchElement"
            @search="search"
            @reset="reset">
          </rzrz-search>
          <!--      table 表单部分 -->
          <div class="table" v-loading="tableLoading">
            <div class="table-content" style="height: 400px">
              <rzrz-table :table-data="jlTableData" ></rzrz-table>
            </div>
            <!--        table pageInation-->
            <div>
              <el-pagination
                @size-change="handleSizeChangeJl"
                @current-change="handleCurrentChangeJl"
                :current-page="jlPage"
                :page-sizes="[30, 50, 100, 200]"
                :page-size="jlPageSize"
                layout="total,sizes,  prev, pager, next, jumper"
                :total="jlPageTotal">
              </el-pagination>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </section>

  </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import {
  getYyDetailList, getYyDetailOptions, updateYyDetail
} from '@/app/tysfrz_01_01/api/yygl';
import {
  getYyIPList, addYyIP, deleteYyIP, updateYyIP
} from '@/app/tysfrz_01_01/api/yygl/yyxq';
import { getGroupTree } from '@/app/tysfrz_01_01/api/qzgl';
import {
  updateYyGoupTree, getYyUserAddTable, updateYyUserAddTable, deleteYyUserTable
} from '@/app/tysfrz_01_01/api/yygl/yysq';
import { getPageTabel } from '@/app/tysfrz_01_01/api/zhgl';
import { getRzrzPageList, getYyLogInfo } from '@/app/tysfrz_01_01/api/rzrz';
import dayjs from 'dayjs';
import YyxqJbxx from './yyglComponents/YyxqJbxx';
import YyxqIplb from './yyglComponents/YyxqIplb';
import { deletePrompt } from '../../../../utils/action-utils';
// import YyxqYhxx from './yyglComponents/YyxqYhxx';
import YyxqYhxxEdit from './yyglComponents/YyxqYhxxEdit';
import YyxqRz from './yyglComponents/YyxqRz';
import RzrzSearch from '../rzrz/rzrzComponents/InfoSearch';
import RzrzTable from '../rzrz/rzrzComponents/InfoTable';
import VTitle from '../../../../components/title/VTitle';

export default {
  name: 'yyxq',
  components: {
    YyxqIplb,
    YyxqJbxx,
    // YyxqYhxx,
    YyxqYhxxEdit,
    YyxqRz,
    RzrzSearch,
    RzrzTable,
    VTitle
  },
  props: {
    id: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    const validatetitle = (rule, value, callback) => {
      if (value === '' || value === undefined || value == null) {
        callback();
      } else {
        const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if ((!reg.test(value)) && value !== '') {
          callback(new Error('请输入正确的IP地址'));
        } else {
          callback();
        }
      }
    };
    return {
      // 用户弹窗 新增数据 保存loading
      userAddCertainLoading: false,
      // 用户弹窗 数据loading
      userTabelDataLoading: false,
      // 暂存基本信息数据
      formeditTemp: {},
      // 基本信息tab内容loading
      baseLoading: false,
      // 用户弹窗 信息
      userTabelData: [],
      // 用户弹窗 信息 新增
      userAddTabelData: [],
      // 用户弹窗 数据个数
      userShowTotal: 0,
      // 用户弹窗 新增的数据个数
      userAddTotal: 0,
      // 用户弹窗 page
      userPage: 1,
      // 用户弹窗pageSize
      userPageSize: 30,
      // 用户弹窗 新增page
      userAddPage: 1,
      // 用户弹窗新增 pageSize
      userAddPageSize: 30,
      input: '',
      formLabelWidth: '120px',
      // label 宽度
      lableWidth: '120px',
      // 新增dialog loading
      formLoading: false,
      // dialog title
      dialogTitle: '应用管理',
      // 列表内容loading
      contentLoading: false,
      dialogTableVisible: false,
      editFormVisible: false,
      qzmessageVisible: false,
      yhmessageVisible: false,
      outerVisible: false,
      innerVisible: false,
      // ip 新增弹窗visible
      dialogFormVisible: false,
      form: {
        IP: ''
      },
      // 群组信息个数
      count: {
        qzcount: '0'
      },
      // 用户信息个数
      yhcount: {
        yhcount: '0'
      },
      rules: {
        IP: [
          {
            required: true,
            message: '请输入正确的IP地址',
            trigger: 'blur',
            validator: validatetitle
          }
        ]
      },
      qzSearch: {
        qzid: '',
        qzmc: '',
        qzzt: ''
      },
      yhSearch: {
        xmid: '',
        zh: '',
        zt: ''
      },
      editoptions: [],
      // 转换后的认证日志基础信息数据
      baseData: [],
      // 编辑基础信息数据
      formedit: {
        yymc: '',
        flid: '',
        yyid: '',
        yymy: '',
        sqfs: '',
        hddz: ''
      },
      // 基本数据编辑 regular
      rulesedit: {
        yymc: [
          {
            required: true,
            message: '请输入正确的系统名称',
            trigger: 'blur'
          }
        ],
        flid: [
          {
            required: true,
            message: '请输入正确的所属分类',
            trigger: ['change', 'blur']
          }
        ],
        yyid: [
          {
            required: true,
            message: '请输入正确的系统ID',
            trigger: 'blur'
          }
        ],
        yymy: [
          {
            required: true,
            message: '请输入正确的秘钥',
            trigger: 'blur'
          }
        ],
        hddz: [
          {
            required: true,
            message: '请填回调地址',
            trigger: 'blur'
          }
        ]
      },
      // 基本数据转换后结构
      editBaseData: [
        {
          id: 'yymc',
          label: '系统名称',
          value: '',
          isHalf: true
        },
        {
          id: 'flid',
          label: '所属分类',
          value: '',
          isHalf: true
        },
        {
          id: 'yyid',
          label: '系统ID',
          value: '',
          isHalf: true
        },
        {
          id: 'yymy',
          label: '秘钥',
          value: '',
          isHalf: true
        },
        {
          id: 'sqfs',
          label: '授权方式',
          value: '',
          isHalf: true
        },
        {
          id: 'hddz',
          label: '回调地址',
          value: '',
          isHalf: true
        }
      ],
      // tab 显示 active
      activeName: 'first',
      // 基础信息IP列表数据
      tableData: [],
      // 认证日志接口数据转换样式
      logInfo: {},
      //
      pageTotal: 0,
      page: 1,
      // 每页个数
      pageSize: 30,
      // 调用记录的数据信息
      // 页面loading
      tableLoading: false,
      // 列表data
      jlTableData: [], // 列表数据集合
      // 页码总数
      jlPageTotal: 0,
      jlPage: 1,
      // 每页个数
      jlPageSize: 30,
      // 群组信息弹窗tree data
      groupTree: [],
      // 群组信息弹窗中选中的key arr
      checkedList: [],
      // 群组信息弹窗 数据loading
      groupTreeLoading: false

    };
  },
  created() {
    // 获取IP列表信息
    this.getYyIPList();
    // 获取基本信息
    this.getBaseInfo();
    // 获取认证日志信息
    this.getLogInfo();
  },
  mounted() {
    this.search();
  },
  methods: {
    dayjs,
    // 获取群组/用户 显示数据
    getChangeCount() {
      getYyDetailList({ yyid: this.id }).then((res) => {
        this.count.qzcount = res.data.content.qzcount || 0;
        // 可访问授权用户信息
        this.yhcount.yhcount = res.data.content.zhcount || 0;
      });
    },
    // 获取应用
    getYyIPList() {
      getYyIPList({ yyid: this.id }).then((res) => {
        this.tableData = res.data.content || [];
      }).finally(() => {
      });
    },
    // 获得基本信息数据 ok
    async getBaseInfo() {
      if (!this.id) {
        this.$message.error('页面参数缺失');
        return;
      }
      const params = {
        yyid: this.id
      };
      this.baseLoading = true;
      try {
        // 获取所属应用分类的options
        await getYyDetailOptions(params).then((res) => {
          this.editoptions = res.data.content || [];
        }).finally(() => {
        });
      } catch (err) {
        this.$message.error(err.message);
        this.baseLoading = false;
      }

      // 获取应用详情信息
      getYyDetailList(params).then((res) => {
        this.count.qzcount = res.data.content.qzcount || 0;
        // 可访问授权用户信息
        this.yhcount.yhcount = res.data.content.zhcount || 0;
        this.checkedList = res.data.content.qzlist ? res.data.content.qzlist.split(',') : [];
        this.formedit = {
          yymc: res.data.content.yymc,
          flid: res.data.content.flid,
          yyid: res.data.content.yyid,
          yymy: res.data.content.yymy,
          sqfs: res.data.content.sqfs,
          hddz: res.data.content.hddz
        };
        this.$set(this.formedit, 'checkList', []);
        if (this.formedit.sqfs) {
          this.formedit.checkList = this.formedit.sqfs.split(',');
        }
        // 数据格式转换
        this.changeObj();
      }).finally(() => {
        this.baseLoading = false;
      });
    },
    // 表单数据值的的显示 回显
    changeObj() {
      const index = this.editoptions.findIndex((x) => x.dmz === this.formedit.flid);
      let name = '';
      if (index > -1) {
        name = this.editoptions[index].dmmc;
      }
      this.editBaseData.forEach((item) => {
        if (item.id === 'flid') {
          // eslint-disable-next-line eqeqeq
          item.value = name;
        } else {
          item.value = this.formedit[item.id];
        }
      });
    },
    // 编辑信息调用 展示编辑弹窗
    editForm() {
      this.editFormVisible = true;
      // 暂存当前状态
      this.formeditTemp = cloneDeep(this.formedit);
    },
    // 基础信息的编辑确定
    editsubmitForm() {
      this.$refs.formedit.validate((valid) => {
        if (valid) {
          this.formLoading = true;
          // 添加事件接口
          const params = cloneDeep(this.formeditTemp);
          params.sqfs = params.checkList.join(',');
          delete params.checkList;
          if (!params.sqfs.includes('authorization_code')) {
            params.hddz = '';
          }
          updateYyDetail(params).then(() => {
            this.$message.success('编辑成功');
            this.editFormVisible = false;
            // 获取应用详情信息
            this.baseLoading = true;
            getYyDetailList({ yyid: this.id }).then((res) => {
              this.checkedList = res.data.content.qzlist ? res.data.content.qzlist.split(',') : [];
              this.formedit = {
                yymc: res.data.content.yymc,
                flid: res.data.content.flid,
                yyid: res.data.content.yyid,
                yymy: res.data.content.yymy,
                sqfs: res.data.content.sqfs,
                hddz: res.data.content.hddz
              };
              this.$set(this.formedit, 'checkList', []);
              if (this.formedit.sqfs) {
                this.formedit.checkList = this.formedit.sqfs.split(',');
              }
              // 数据格式转换
              this.changeObj();
            }).finally(() => {
              this.baseLoading = false;
            });
          }).finally(() => {
            this.formLoading = false;
          });
          return true;
        }
        return false;
      });
    },
    // 编辑信息调用 弹窗消失
    formeditClose() {
      this.editFormVisible = false;
      this.$nextTick(() => {
        this.$refs.formedit.clearValidate();
      });
    },
    // IP新增弹窗展示
    showDialog() {
      // 展示弹窗
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
    },
    // ip 新增确定事件
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = {
            yyid: this.id,
            ip: this.form.IP
          };
          this.formLoading = true;
          // 新增ip保存接口
          addYyIP(params).then(() => {
            this.$message.success('添加成功');
            this.dialogFormVisible = false;
            this.getYyIPList();
          }).finally(() => {
            this.formLoading = false;
          });
          return true;
        }
        return false;
      });
    },
    // ip 删除事件
    deleteIP(item) {
      // 设置参数
      const config = {
        text: '是否删除?',
        btnText: '执行中...',
        title: '',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        // 删除IP接口
        const params = {
          sjjlid: item.sjjlid
        };
        deleteYyIP(params).then(() => {
          const index = this.tableData.findIndex((x) => x.sjjlid === item.sjjlid);
          if (index > -1) {
            this.tableData.splice(index, 1);
            this.$message.success('删除成功');
          }
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // ip 状态更改
    changeIPStatus(params) {
      const text = params.zt === 1 ? '停用' : '启用';
      // 模拟请求数据
      const config = {
        text: `是否${text}?`,
        btnText: '执行中...',
        title: '',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        const paramsTemp = cloneDeep(params);
        if (paramsTemp.zt === 1) {
          paramsTemp.zt = 0;
        } else {
          paramsTemp.zt = 1;
        }
        updateYyIP(paramsTemp).then(() => {
          this.$message.success('状态更改成功');
          if (params.zt === 1) {
            params.zt = 0;
          } else {
            params.zt = 1;
          }
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 群组弹窗显示部分----------------------
    // 群组信息弹窗 展示
    groupMessageShow() {
      this.qzmessageVisible = true;
      this.groupTreeLoading = true;
      // 获取群组信息弹窗
      getGroupTree({ id: this.id }).then((res) => {
        this.groupTree = res.data.content || [];
      }).finally(() => {
        // this.groupTree = [
        //   {
        //     qzid: 1,
        //     qzmc: '一级 1',
        //     sjqzid: null,
        //     children: [{
        //       qzid: 4,
        //       qzmc: '二级 1-1',
        //       sjqzid: 1,
        //       children: [{
        //         sjqzid: 4,
        //         qzid: 9,
        //         qzmc: '三级 1-1-1'
        //       }, {
        //         sjqzid: 4,
        //         qzid: 10,
        //         qzmc: '三级 1-1-2'
        //       }]
        //     }]
        //   }
        // ];
        this.groupTreeLoading = false;
      });
    },
    // 群组信息弹窗 确定
    groupSubmit() {
      const params = this.$refs.groupMessageTree.getCheckedNodes() || [];

      this.groupTreeLoading = true;
      const paramsInfo = {
        qzlist: params.map((x) => x.qzid),
        yyid: this.id
      };
      updateYyGoupTree(paramsInfo).then((res) => {
        this.$message.success('修改成功');
        this.checkedList = params.map((x) => x.qzid);
        this.count.qzcount = res.data.content;
      }).finally(() => {
        this.groupTreeLoading = false;
        this.qzmessageVisible = false;
      });
    },
    // 删除 TODO
    deleteTarget(item) {
      // 设置参数
      const config = {
        text: '是否删除?',
        btnText: '执行中...',
        title: '',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        // 删除IP接口
        const params = {
          sjjlid: item.sjjlid
        };
        deleteYyUserTable(params).then(() => {
          const index = this.userTabelData.findIndex((x) => x.sjjlid === item.sjjlid);
          if (index > -1) {
            this.userTabelData.splice(index, 1);
            this.userShowTotal--;
            this.getChangeCount();
            this.$message.success('删除成功');
          }
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 用户信息弹窗展示
    userMessageShow() {
      this.outerVisible = true;
      this.userPage = 1;
      this.searchUserBase();
    },
    // 用户信息弹窗 search
    searchUser(val) {
      if (val.isAdd) {
        this.searchUserBase();
      } else {
        this.searchUserHandle();
      }
    },
    // 用户信息弹窗 reset
    resetUser(val) {
      if (val.isAdd) {
        this.userPage = 1;
        this.searchUserBase();
      } else {
        this.userAddPage = 1;
        this.searchUserHandle();
      }
    },
    // 用户信息弹窗页面page change
    changUserPage(val) {
      if (val.isAdd) {
        this.changeShowPage(val);
      } else {
        this.changeAddPage(val);
      }
    },
    // 用户信息弹窗页面pagesize change
    changUserPageSize(val) {
      if (val.isAdd) {
        this.changeShowPageSize(val);
      } else {
        this.changeAddPageSize(val);
      }
    },
    // 用户信息弹窗 search
    searchUserBase() {
      this.userTabelDataLoading = true;
      this.$nextTick(() => {
        // 获取用户弹窗table 信息数据 接口
        const info = this.$refs.userShowTable.search;
        const params = {
          page: this.userPage,
          pageSize: this.userPageSize,
          yyid: this.id,
          ...info
        };
        // 用户数据弹窗信息 获取
        getYyUserAddTable(params).then((res) => {
          this.userTabelData = res.data.content || [];
          this.userShowTotal = res.data.pageInfo.total;
        }).finally(() => {
          this.userTabelDataLoading = false;
        });
      });
    },

    // 用户信息弹窗 新增 弹窗页码更改
    changeShowPage(val) {
      this.userPage = val.page;
      this.searchUserBase();
    },
    // 用户信息弹窗 新增 弹窗页码个数更改
    changeShowPageSize(val) {
      this.userPageSize = val.pageSize;
      this.searchUserBase();
    },

    // 用户信息添加弹窗(新增)
    addTabel() {
      this.innerVisible = true;
      this.userAddPage = 1;
      this.searchUserHandle();
    },
    // 用户信息弹窗 checked
    searchUserHandle() {
      this.userTabelDataLoading = true;
      this.$nextTick(() => {
        // 获取用户弹窗table 信息数据 接口
        const info = this.$refs.userAddTable.search;
        const params = {
          page: this.userAddPage,
          pagesize: this.userAddPageSize,
          yyid: this.id,
          ...info
        };
        // 用户数据弹窗信息 获取
        getPageTabel(params).then((res) => {
          this.userAddTabelData = res.data.content || [];
          this.userAddTotal = res.data.pageInfo.total;
        }).finally(() => {
          this.userTabelDataLoading = false;
          // 设置已勾选项
          // this.$nextTick(() => {
          //   this.$refs.userAddTable.toggleSelection([this.userAddTabelData[1]]);
          // });
        });
      });
    },
    // 用户信息 弹窗页码更改
    changeAddPage(val) {
      this.userAddPage = val.page;
      this.searchUserHandle();
    },
    // 用户信息 弹窗页码个数更改
    changeAddPageSize(val) {
      this.userAddPageSize = val.pageSize;
      this.searchUserHandle();
    },
    // 用户信息 弹窗 新增 确定
    userAddCertain() {
      this.userAddCertainLoading = true;
      const params = {
        yyid: this.id,
        zhlist: this.$refs.userAddTable.multipleSelection.map((x) => x.zhid)
      };
      updateYyUserAddTable(params).then(() => {
        this.$message.success('数据新增成功');
        this.getChangeCount();
      }).finally(() => {
        this.userAddCertainLoading = false;
        this.innerVisible = false;
        this.outerVisible = false;
      });
    },

    // 获取日志
    getLogInfo() {
      // 获取日志内容接口
      getYyLogInfo({ mbyyid: this.id }).then((res) => {
        this.logInfo = res.data.content;
      }).finally(() => {
        /* this.logInfo = {
          jyy: {
            id: 1,
            time: '近一个月调用接口',
            sumnumber: '582',
            succnumber: '582',
            fallnumber: '0',
            xytime: '200ms'
          },
          jyz: {
            id: 2,
            time: '近一周调用接口',
            sumnumber: '171',
            succnumber: '171',
            fallnumber: '0',
            xytime: '200ms'
          },
          jyt: {
            id: 3,
            time: '近一天调用接口',
            sumnumber: '37',
            succnumber: '37',
            fallnumber: '0',
            xytime: '200ms'
          }
        }; */
        this.changeBaseData();
      });
    },
    // 统计信息接口数据转换
    changeBaseData() {
      /* const needArr = [
        {
          id: 1,
          label: 'jyy',
          time: '',
          sumnumber: '',
          succnumber: '',
          fallnumber: ''
        },
        {
          id: 2,
          label: 'jyz',
          time: '',
          sumnumber: '',
          succnumber: '',
          fallnumber: ''
        },
        {
          id: 3,
          label: 'jyt',
          time: '',
          sumnumber: '',
          succnumber: '',
          fallnumber: ''
        }
      ]; */
      // this.logInfo = [
      //   {
      //     label: '近一月认证情况',
      //     sumcount: '111',
      //     successcount: '1',
      //     failcount: '1'
      //   },
      //   {
      //     label: '近一周认证情况',
      //     sumcount: '111',
      //     successcount: '1',
      //     failcount: '1'
      //   },
      //   {
      //     label: '近一天认证情况',
      //     sumcount: '111',
      //     successcount: '1',
      //     failcount: '1'
      //   }
      // ];
      /* needArr.forEach((item) => {
        const key = item.label;
        const obj = this.logInfo[key];
        Object.assign(item, obj);
      }); */
      this.baseData = this.logInfo;
    },
    handleClick(tab, event) {
    },
    // 调用记录里面的操作
    /**
     * search 搜索事件
     * @param params
     * @returns {Promise<void>}
     */
    search() {
      const searchParams = this.$refs.searchElement.listQuery;
      const params = {
        yyid: this.id,
        pageSize: this.jlPageSize,
        page: this.jlPage,
        ...searchParams
      };
      // 模拟接口
      this.tableLoading = true;
      getRzrzPageList(params).then((res) => {
        this.jlTableData = res.data.content;
        this.jlPageTotal = res.data.pageInfo.total;
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    /**
     * 查询条件重置
     */
    reset() {
      this.jlPage = 1;
      this.search();
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChangeJl(val) {
      this.jlPageSize = val;
      this.search();
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChangeJl(val) {
      this.jlPage = val;
      this.search();
    }
  }
};
</script>

<style lang="scss" scoped>
.jksqgl-pageform-header {
  height: 50px;
  padding: 0 20px;
}

.jksqgl-pageform-header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  background-color: #EAEAEA;
  border: 1px solid #ededed;
  width: 100%;
  height: 50px;
  margin-top: 10px;
  padding: 0 10px;
  box-sizing: border-box;
}

.jksqgl-page-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  background: #FFFFFF;
  padding-left: 10px;
  color: #3a8ee6;

  .page-header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    line-height: 50px;
    font-size: 20px;
    font-family: inherit;

    .el-icon-s-platform {
      width: 50px;
      height: 50px;
      display: block;
      line-height: 50px;

      /*.jksqgl-page-header {
        color: #000000;
        margin: 0;
        padding: 0;
        padding-left: 10px;
      }*/
    }
  }
}

.jbxx-page-content-top {
  width: 33.33%;
  display: flex;
  justify-content: space-between;
  padding-left: 100px;
  background-color: #FFFFFF;
}

.tjxx-page-content-first {
  width: 100%;
  height: 350px;
  padding-top: 30px;
  padding-left: 15px;

  .tjxx-page-content-first-title {
    background-color: #ededed;
    width: 400px;
    height: 50px;
    color: #3a8ee6;
    line-height: 50px;
    padding: 0px 50px;
    box-sizing: border-box;
  }

  .tjxx-page-content-first-mid {
    display: flex;
    justify-content: space-between;
    width: 400px;
    height: 200px;
    border: #ededed 1px solid;
    border-bottom: none;
    padding: 50px 50px 0px;
    text-align: center;
    box-sizing: border-box;

    .tjxx-page-content-first-mid-left {
      .tjxx-page-content-first-number {
        color: #2d8cf0;
      }

      .tjxx-page-content-first- written {
      }
    }

    .tjxx-page-content-first-mid-centre {
      .tjxx-page-content-first-number-centre {
        color: darkorange;
      }
    }

    .tjxx-page-content-first-mid-right {
      .tjxx-page-content-first-number-right {
        color: red;
      }
    }
  }

  .tjxx-page-content-first-last {
    background-color: #FFFFFF;
    width: 400px;
    height: 40px;
    display: flex;
    justify-content: center;
    line-height: 40px;
    border: #ededed 1px solid;
    border-top: none;
    box-sizing: border-box;

    .tjxx-page-content-first-last-title {
      color: #515a6e;
    }

    .tjxx-page-content-first-last-number {
      color: green;
    }
  }
}

.qzmessage {
  display: flex;
  padding: 10px 20px;

  .qzmessage-title {
    padding-right: 10px;
  }

  .qzmessage-sj {
    color: $page-font-hover-color;
    cursor: pointer;
  }
}

.card-bottom-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
  cursor: pointer;
}

.card-bottom-right {
  color: #90a4af;
}
</style>
