<template>
  <div class="yyxq-dialog-content">
    <div class="zhxy-form zhxy-form-search-part search">
      <el-form label-width="120px" inline :model="search" ref="searchForm">
        <el-form-item prop="zhid">
          <span class="zhxy-form-label" slot="label">账号/姓名</span>
          <el-input class="zhxy-form-inline" v-model="search.zhid"
                    placeholder="账号/姓名"
                    size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <span class="zhxy-form-label" slot="label">状态</span>
          <el-select
            class="zhxy-form-inline"
            v-model="search.zhzt"
            placeholder="状态"
            size="small">
            <el-option
              v-for="item in yhztOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchTable" size="small">查询</el-button>
          <el-button type="primary" @click="resetTable" size="small">重置</el-button>
        </el-form-item>

      </el-form>
    </div>
    <div class="table-content">
      <div class="button-list" v-if="isAdd">
        <el-button type="primary" size="small" @click="addTable">新增用户</el-button>
      </div>
      <el-table
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        class="zhxy-table"
        :data="tableData"
        height="100%"
        border
        style="width: 100%">
        <el-table-column
          v-if="!isAdd"
          type="selection"
          width="55"></el-table-column>
        <el-table-column prop="zhid" label="职工号" width="180"></el-table-column>
        <el-table-column prop="xm" label="用户姓名" width="180"></el-table-column>
<!--        <el-table-column prop="yhbm" label="部门" width="180"></el-table-column>-->
        <el-table-column prop="sjh" label="手机号" width="180"></el-table-column>
        <el-table-column prop="yx" label="电子邮箱" width="180"></el-table-column>
        <el-table-column prop="zhzt" label="用户状态" width="180">
          <template slot-scope="scope">
            {{ scope.row.zhzt | getZhzt}}
          </template>
        </el-table-column>

        <el-table-column v-if="isAdd" fixed="right" prop="" label="操作" width="50">
          <template slot-scope="scope">
            <el-button size="small" @click="deleteTarget(scope.row)" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[30, 50, 100, 200]" :page-size="pageSize"
                     :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"/>
    </div>
  </div>
</template>

<script>
export default {
  filters: {
    getZhzt(val) {
      if (!val) {
        return '';
      }
      const list = {
        1: '启用',
        2: '停用',
        3: '锁定'
      };
      return list[val];
    }
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    page: {
      type: Number,
      default: () => 1
    },
    pageSize: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    },
    isAdd: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      multipleSelection: [],
      // 搜索部分数据
      search: {
        zhid: '',
        zhzt: ''
      },
      // 用户状态 options
      yhztOptions: [
        {
          label: '停用',
          value: 2
        }, {
          label: '启用',
          value: 1
        }, {
          label: '锁定',
          value: 3
        }]
    };
  },
  created() {},
  methods: {
    // 删除
    deleteTarget(item) {
      this.$emit('deleteTarget', item);
    },
    // 查询事件
    searchTable() {
      const params = {
        ...this.search,
        isAdd: this.isAdd
      };
      this.$emit('search', params);
    },
    // 查询重置
    resetTable() {
      Object.keys(this.search).forEach((item) => {
        this.search[item] = '';
      });
      const params = {
        ...this.search,
        isAdd: this.isAdd
      };
      this.$emit('reset', params);
      // this.searchTable();
    },
    // 设置 tabel 选中list
    toggleSelection(rows) {
      console.log('jinru', rows);
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    // table 选中的list
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 新增
    addTable() {
      this.$emit('addTabel');
    },
    // 页码变更
    handleSizeChange(val) {
      console.log(val);
      const params = {
        isAdd: this.isAdd,
        page: this.page,
        pageSize: val,
        ...this.search
      };
      this.$emit('changePageSize', params);
    },
    // 页码内个数 调整
    handleCurrentChange(val) {
      const params = {
        isAdd: this.isAdd,
        page: val,
        pageSize: this.pageSize,
        ...this.search
      };
      this.$emit('changePage', params);
    }
  }
};
</script>

<style lang="scss" scoped>
.yyxq-dialog-content{
  height: 450px;
  display: flex;
  flex-direction: column;
  .search{

  }
  .table-content{
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
}
.button-list{
  display: flex;
  justify-content: flex-end;
  margin-bottom: $page-content-margin;
}
</style>
