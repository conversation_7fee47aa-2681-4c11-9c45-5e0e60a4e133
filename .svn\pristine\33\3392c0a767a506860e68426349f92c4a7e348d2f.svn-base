<template>
  <div class="info-edit">
    <el-form
      label-width="150px"
      :model="formData"
      class="zhxy-form zhxy-form-search-part"
      :rules="rules"
      ref="form"
    >
      <el-form-item prop="fwmc">
        <span class="zhxy-form-label" slot="label">服务名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.fwmc" placeholder="服务名称"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item prop="fwsm">
        <span class="zhxy-form-label" slot="label">服务说明</span>
        <el-input class="zhxy-form-inline" v-model="formData.fwsm" placeholder="服务说明"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">服务分类</span>
        <el-select class="zhxy-form-inline" v-model="formData.fwfl" placeholder="服务类型"
                   size="small">
          <el-option
            v-for="item in fwflOptions"
            :key="item.dmz"
            :label="item.dmmc"
            :value="item.dmz">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh" placeholder="排序号"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item prop="fwzt">
        <span class="zhxy-form-label" slot="label">状态</span>
        <el-select class="zhxy-form-inline" v-model="formData.fwzt" placeholder="状态"
                   size="small">
          <el-option
            v-for="item in ztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>

</template>

<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => ({
        fwdyid: '', // 服务ID
        pxh: '', // 排序号
        fwmc: '', // 服务名称
        fwsm: '', // 服务说明
        fwfl: '', // 服务分类
        cjr: '', // 创建人
        cjsj: '', // 创建时间
        bgr: '', // 变更人
        bgsj: '', // 变更时间
        fwzt: 1 // 服务状态
      })
    },
    // 状态类型 options
    ztOptions: {
      type: Array,
      default: () => []
    },
    // 服务类型 options
    fwflOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // form rules
      rules: {
        fwmc: [
          {
            required: true,
            message: '服务名称不可为空',
            trigger: 'blur'
          }
        ],
        fwzt: [
          {
            required: true,
            message: '状态不可为空',
            trigger: 'blur'
          }
        ]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .zhxy-form-inline {
    width: 100%;
  }

  .zhxy-form-search-part .el-form-item {
    margin-bottom: 2*$page-content-padding !important;
  }

  .info-edit {
    padding-right: 2*$page-content-padding
  }
</style>
