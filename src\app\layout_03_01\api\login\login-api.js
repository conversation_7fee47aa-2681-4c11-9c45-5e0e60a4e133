import { createAPI } from '@/utils/request.js';

const BASE_URL = '';

/**
 * 获取登陆信息接口
 * @param data
 * @returns {*}
 */
export const loginApi = (data) => createAPI(`${BASE_URL}/layout_03_01/login/checkUser`, 'post', data);
/**
 * 获取登陆验证码
 * @param data
 * @returns {AxiosPromise}
 */
export const verificationGet = (data) => createAPI(`${BASE_URL}/layout_03_01/login/getCode`, 'post', data);
/**
 * 登出
 * @param data
 * @returns {AxiosPromise}
 */
export const logoutApi = (data) => createAPI(`${BASE_URL}/layout_03_01/logout/logout`, 'post', data);
/**
 * 统一身份认证登出
 * @param data
 */
export const authLogoutApi = (data) => createAPI(`${BASE_URL}/layout_03_01/logout/logoutByTysfrz`, 'post', data);
/**
 * 统一身份验证登录
 * @param data
 * @returns {AxiosPromise}
 */
export const authLoginApi = (data) => createAPI(`${BASE_URL}/layout_03_01/login/loginBycode`, 'post', data);
