<template>
  <div class="dialog-content">
    <el-form ref="rwxxForm" :model="FormData" :rules="rules" label-width="130px" label-position="right">
      <el-form-item label="数据源ID" prop="sjyid" style="display: none">
        <el-input :disabled="!sjyidVisible" v-model="FormData.sjyid" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="数据源名称" prop="sjymc">
        <el-input v-model="FormData.sjymc" placeholder="必填" maxlength="50" size="small"/>
      </el-form-item>
      <el-form-item label="数据源实例名称" prop="sjymc">
        <el-input v-model="FormData.sjyslmc" placeholder="必填" maxlength="50" size="small"/>
      </el-form-item>
      <el-form-item label="数据源IP：" prop="sjyip">
        <el-input v-model="FormData.sjyip" placeholder="必填" maxlength="50" size="small"/>
      </el-form-item>
      <el-form-item prop="sjylx" label="数据源类型" style="margin-bottom: 10px;">
        <el-radio-group v-model="FormData.sjylx">
          <el-radio :label="1">结构化</el-radio>
          <el-radio :label="2">非结构化</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="端口" prop="dk">
        <el-input v-model="FormData.dk" maxlength="50" size="small"/>
      </el-form-item>
      <el-form-item label="用户名" prop="yhm">
        <el-input v-model="FormData.yhm" maxlength="50" size="small"/>
      </el-form-item>
      <el-form-item label="密码" prop="mm">
        <el-input v-model="FormData.mm" maxlength="50" size="small"/>
      </el-form-item>

      <el-form-item label="预警级别" required prop="yjjbm">
        <el-select class="zhxy-form-inline" v-model="FormData.yjjbm" placeholder="预警级别" size="small" >
          <el-option
            v-for="item in yjjbmOptions"
            :key="item.yjjbm"
            :label="item.yjjbmc"
            :value="item.yjjbm">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="sjyzt" label="数据源状态" style="margin-bottom: 10px;">
        <el-radio-group v-model="FormData.sjyzt">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数据源描述" prop="sjyms">
        <el-input v-model="FormData.sjyms" maxlength="500" size="small"/>
      </el-form-item>

    </el-form>

  </div>

</template>

<script>

import {
  findYjjb
} from '@/app/xtjkyyj_01_01/api/sjkgl/sjkgl.js';

export default {
  name: 'kettlerwglEdit',
  components: {
  },
  props: {
    FormData: {
      type: Object,
      default: () => ({
        sjyip: '',
        sjymc: '',
        sjyslmc: '',
        sjyms: '',
        sjyzt: '',
        sjylx: '',
        dk: '',
        yhm: '',
        mm: '',
        zxztjcid: '',
        yjjbm: ''
      })
    },
    sjyidVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        sjymc: [{
          required: true,
          trigger: 'blur',
          message: '请选择数据源名称'
        }],
        sjyslmc: [{
          required: true,
          trigger: 'blur',
          message: '请选择数据源实例名称'
        }],
        sjyip: [{
          required: true,
          trigger: 'blur',
          message: '请选择数据源IP'
        }],
        sjylx: [{
          required: true,
          trigger: 'blur',
          message: '请选择数据源类型'
        }],
        sjyzt: [{
          required: true,
          trigger: 'blur',
          message: '请选择数据源状态'
        }],
        yjjbm: [{
          required: true,
          trigger: 'blur',
          message: '请选择预警级别码'
        }]
      },
      yjjbmOptions: []
    };
  },
  mounted() {
    this.search();
  },
  methods: {
    // 加载部门树
    search() {
      findYjjb()
        .then((res) => {
          this.yjjbmOptions = res.data.content;
        })
        .finally(() => {

        });
    }
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 20px;
  }
  .jbmc{
    width: 80%;
  }
  .zq{
    width: 150px;
  }
  .jbmcxz{
    width: 50px;
    display: inline-block;
    margin-left: 25px;
    color: #409EFF;
    cursor: pointer;
  }
  .jbmcqk{
    width: 50px;
    color: #409EFF;
    cursor: pointer;
  }
  .zhxy-form-inline{
    width: 100%;
  }
</style>
