<template>
  <div>
    <el-drawer
      :title="title"
      :visible.sync="visible"
      :direction="'rtl'"
      :before-close="handleClose">
      <div style="padding: 10px;height: 100%;" v-loading="loading">
        <div style="padding-bottom: 20px">
          <el-button type="primary" size="small" @click="handledefinezy()">确定</el-button>
          <el-button type="" size="small" @click="hideDialog()">取消</el-button>
        </div>
        <div style="max-height: calc(100% - 80px);overflow: auto">
          <el-tree
            show-checkbox
            :default-checked-keys="checkedList"
            :data="treeData"
            ref="zyTree"
            node-key="yyid"
            :props="defaultZyProps">
          </el-tree>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import {
  findCkeckedYyzy, findYyzylistAll, updateJsxzy, findBmlistAll, addYhjsmx
} from '@/app/xtgl_03_01/api/jsgl/jsgl.js';

export default {
  name: 'roleTreeDialog',
  props: {
    width: {
      type: String,
      default: '500px'
    },
    title: {
      type: String,
      default: '选择用户111'
    },
    defaultZyProps: {
      type: Object,
      default: () => ({
        label: 'yymc',
        id: 'yyid',
        children: 'children'
      })
    },
    treeData: {
      type: Array,
      default: () => []
    },
    checkedList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // dialog 显隐
      visible: false,
      // 数据加载loading
      loading: false
    };
  },
  methods: {
    // 新增 用户角色明细
    handledefinezy() {
      const params = this.$refs.zyTree.getCheckedNodes(true) || [];
      this.$emit('sourceConfirm', params);
    },
    // 角色资源显示
    showDialog() {
      this.visible = true;
    },
    // 隐藏弹窗
    hideDialog() {
      this.visible = false;
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done();
        })
        .catch((_) => {
        });
    }
  }
};
</script>
<style lang="scss">
  .el-drawer__header {
    margin-bottom: 0;
  }
</style>
