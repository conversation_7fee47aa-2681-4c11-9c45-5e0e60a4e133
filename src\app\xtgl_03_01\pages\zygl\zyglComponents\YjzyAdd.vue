<template>
  <div class="dialog-content">
    <el-form ref="yjzyForm" :model="FormData" :rules="rules" label-width="160px" label-position="right">
      <el-form-item v-if="yjzyVisible" label="父角色ID" prop="fjsid">
        <el-input disabled :value="yjzyFjsid" v-model="yjzyFjsid" placeholder="父角色ID" size="small"/>
      </el-form-item>
      <el-form-item label="功能资源名称" prop="gnzymc">
        <el-input v-model="FormData.gnzymc" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="资源名称后缀" prop="zymchz">
        <el-input v-model="FormData.zymchz" placeholder="选填，加（）显示" size="small"/>
      </el-form-item>
      <el-form-item label="请求路径" v-if="yjzyVisible" prop="qqlj">
        <el-input v-model="FormData.qqlj" placeholder="选填" size="small"/>
      </el-form-item>
      <el-form-item label="排序号" prop="pxh">
        <el-input v-model="FormData.pxh" placeholder="选填且只能为数字" size="small" oninput="value=value.replace(/[^\d]/g,'')"/>
      </el-form-item>
      <el-form-item prop="sfsqxxz" label="是否受权限限制">
        <el-radio-group v-model="FormData.sfsqxxz">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="szhj" label="宿主环境">
        <el-radio-group v-model="FormData.szhj">
          <el-radio :label="1">PC</el-radio>
          <el-radio :label="2">移动</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="待办所属小应用标识" prop="dbssxyybs">
        <el-input v-model="FormData.dbssxyybs" placeholder="选填" size="small"/>
      </el-form-item>
      <el-form-item label="待办类型标识" prop="dblxbs">
        <el-input v-model="FormData.dblxbs" placeholder="选填" size="small"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Zygl',
  props: {
    FormData: {
      type: Object,
      default: () => ({
        gnzyid: '',
        gnzymc: '',
        zymchz: '',
        qqlj: '',
        fcdid: '',
        pxh: '',
        sfsqxxz: '',
        szhj: '',
        dbssxyybs: '',
        dblxbs: '',
        gzlgwtbfs: ''
      })
    }
  },
  data() {
    return {
      // 父功能资源id
      yjzyFjsid: '',
      yjzyVisible: false,
      // 规则
      rules: {
        gnzymc: [{
          required: true,
          trigger: 'blur',
          message: '请输入功能资源名称'
        },
        {
          max: 100,
          message: '功能资源名称长度不能多于100位'
        }
        ],
        sfsqxxz: [{
          required: true,
          trigger: 'blur',
          message: '请选择是否受权限限制'
        }
        ],
        szhj: [{
          required: true,
          trigger: 'blur',
          message: '请选择宿主环境'
        }
        ],
        zymchz: [
          {
            max: 50,
            message: '资源名称后缀长度不能多于50位,加（）显示'
          }
        ],
        qqlj: [
          {
            max: 100,
            message: '请求路径长度不能多于100位'
          }
        ],
        pxh: [
          {
            max: 5,
            message: '排序号长度不能多于5位'
          }
        ],
        dbssxyybs: [
          {
            max: 100,
            message: '待办所属小应用标识长度不能多于100位'
          }
        ],
        dblxbs: [
          {
            max: 100,
            message: '待办类型标识长度不能多于100位'
          }
        ]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 10px;
  }
</style>
