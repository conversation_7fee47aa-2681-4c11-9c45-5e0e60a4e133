<template>
  <div class="qzgl-content">
    <!--    可访问应用弹窗-->
    <role-tree-dialog
      :checked-list="groupSourceTreeChecked"
      :title="editDialogName"
      :tree-data="groupSourceTree"
      ref="roleTree"
      @sourceConfirm="sourceConfirm">
    </role-tree-dialog>
    <!--    新增群组下用户弹窗-->
    <choose-user-dialog
      v-loading="chooseLoading"
      ref="chooseTree"
      :defaultProps="{
        label: 'xm',
        id: 'zhid',
        children: 'children'
      }"
      :tree-detail="treeData"
      :treeDetailLoading="treeDetailLoading"
      :page="userPage"
      :pageSize="userPageSize"
      :total="userTotal"
      @searchInfo="searchInfo"
      @pageChange="pageChange"
      @cancel="closeFixedPost"
      @certain="getFixedPost">
    </choose-user-dialog>
    <div class="title">
      <v-title name="群组管理"></v-title>
    </div>
    <div class="content">
      <div class="qzgl-content-left" v-loading="groupTreeLoading">
        <div class="qzgl-content-left-content">
          <div class="tree-top-part">
            <el-input
              placeholder="请输入关键字"
              type="text"
              class="input-search"
              size="small"
              prefix-icon="el-icon-search"
              v-model="treeinput"
              clearable>
            </el-input>
            <div style="padding-left: 25px">
              <el-button style="font-weight: bold" size="default" icon="el-icon-plus" type="text" @click="addGroup">
                新建根群组
              </el-button>
            </div>
          </div>
          <div class="qzgl-content-left-tree" ref="groupTree">
            <el-tree
              ref="groupTreeLeft"
              :props="defaultJsProps"
              highlight-current
              :data="groupTree"
              node-key="qzid"
              default-expand-all
              :filter-node-method="filterNode"
              @node-contextmenu="openMenu"
              @node-click="handleNodeClick">
              <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{ data.qzmc }}
              </span>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="qzgl-content-right" ref="right">
        <el-tabs type="card" v-model="activeName" style="height: 100%" @tab-click="groupTabChange">
          <el-tab-pane label="群组详情" name="roleDetail" class="content-tab-pane">
            <div class="qzgl-user-edit-none" v-show="!groupId">暂无选择数据</div>
            <department-detail v-show="groupId" v-loading="formDataLoading" ref="detail" :form-data="formData" @saveForm="saveForm"></department-detail>
          </el-tab-pane>
          <el-tab-pane label="群组下用户" name="roleUser" class="content-tab-pane">
            <div class="zhxy-form zhxy-form-search-part">
              <el-form label-width="120px" inline :model="departmentSearch" ref="searchForm">
                <el-form-item>
                  <span class="zhxy-form-label" slot="label">用户ID/姓名</span>
                  <el-input class="zhxy-form-inline" v-model="departmentSearch.zhid" placeholder="用户ID/姓名"
                            size="small"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="getGroupUser"
                    size="small">
                    查询
                  </el-button>
                  <el-button type="" @click="resetGroupUser()" size="small">
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="button-tab">
              <el-button type="primary" icon="el-icon-plus" size="small" @click="showYhDialog">新增群组下用户
              </el-button>
<!--              <el-button type="danger" icon="el-icon-delete-solid" size="small"
                         @click="()=>{$refs.userDetail.deleteJsyh()}">批量删除
              </el-button>-->
            </div>
            <department-user
              v-loading="groupUserLoading"
              :table-data="tableDataUser"
              ref="userDetail"
              :scrollerHeight="scrollerHeight"
              @delCurrent="delCurrent">
            </department-user>
            <div class="pageIn">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-sizes="[30, 50, 100, 200,500]"
                layout="total, sizes, prev, pager, next, jumper"
                :total=total>
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane v-loading="groupSourceLoading" label="可访问应用" name="roleSource" class="content-tab-pane role-source">
            <el-button class="role-source-button" type="primary" size="small" @click="getMmodifySource">修改可访问应用</el-button>
            <div style="height: 100%;overflow: auto">
              <el-tree
                ref="tree"
                :props="yyProps"
                highlight-current
                :data="yyTree"
                node-key="yyid"
                default-expand-all>
              <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{ data.yymc }}
              </span>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <right-menu ref="rightMenuPart" :options="rightMenuOptions"></right-menu>
    <!--新增一级角色-->
    <el-dialog
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="qzEditFormVisible"
      :title="editDialogType === 'edit'?'新建子群组':'新建根群组'"
      :close-on-click-modal="false"
      @close="qzClose">
      <yjjs-add ref="qzEditRef" :edit-dialog-type="editDialogType" :form-data="qzEditForm"></yjjs-add>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" :loading="qzConfirmLoading" @click="qzConfirm">确定</el-button>
        <el-button type="" size="small" @click="qzClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import { getPageTabel } from '@/app/tysfrz_01_01/api/zhgl';
import {
  getGroupTree, addGroupTree, getGroupBase, deleteGroupBase, updateGroupBase, getGroupUser, deleteGroupUser, updateGroupUserTree, getSourceTree, getSourceTreeAll, updateSourceTree
} from '@/app/tysfrz_01_01/api/qzgl';
import RightMenu from '@/app/xtgl_03_01/components/RightMenu/index.vue';

import { deletePrompt } from '@/utils/action-utils';

import VTitle from '../../../../components/title/VTitle';
import DepartmentDetail from './qzglComponents/DepartmentDetail';
import DepartmentUser from './qzglComponents/DepartmentUser';
import ChooseUserDialog from './qzglComponents/ChooseUserDialog';
import RoleTreeDialog from './qzglComponents/RoleTreeDialog';
import YjjsAdd from './qzglComponents/YjjsAdd';

export default {
  components: { // 注册组件
    VTitle,
    DepartmentDetail,
    DepartmentUser,
    ChooseUserDialog,
    RoleTreeDialog,
    RightMenu,
    YjjsAdd
  },
  data() {
    return {
      // groupId
      groupId: '',
      // 群组下用户确定loading
      chooseLoading: false,
      // 群组数据tree loading
      groupTreeLoading: false,
      // 应用资源加载loading
      groupSourceLoading: false,
      // 群组详情的加载loading
      formDataLoading: false,
      // 群组详情的展示数据
      formData: {
        qzid: '',
        qzmc: '',
        pxh: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      },
      // table pageSize page total
      total: 0,
      pageSize: 30,
      page: 1,
      // 群组 新建/编辑 弹窗数据
      qzEditForm: {
        sjqzid: '',
        qzid: '',
        qzmc: '',
        pxh: ''
      },
      // 群组 编辑/新增 弹窗Visible
      qzEditFormVisible: false,
      // 群组 编辑/新增 确定loading
      qzConfirmLoading: false,
      // 群组 弹窗 编辑new or 新增 edit
      editDialogType: 'new',
      // 角色弹窗名称
      editDialogName: '可访问的应用',
      // 部门详情------------------
      departmentSearch: {
        zhid: ''
      },

      // 右键菜单menu list init
      rightMenuOptions: {
        list: [],
        top: 0,
        left: 0,
        event: {},
        ref: {}
      },
      // 界面定义属性
      scrollerHeight: 'calc(100% - 150px)',
      // tree search input
      treeinput: '',

      // tab标签
      activeName: 'roleDetail',
      // 添加用户的 tree 弹窗数据
      treeData: [],
      // 添加用户的 数据loading
      treeDetailLoading: false,
      // 添加用户的page
      userPage: 1,
      // 添加用户的pageSize
      userPageSize: 50,
      // 添加用户的 total
      userTotal: 0,
      // 群组用户table 数据 loading
      groupUserLoading: false,
      // 群组用户 table 数据
      tableDataUser: [],
      // 部门table
      tableDataBm: [],
      // table 选中Array
      multipleSelection: [],
      // 应用资源树结构
      yyTree: [],
      // 角色树结构
      groupTree: [],
      // 抽屉资源弹窗tree
      groupSourceTree: [],
      // 抽屉资源选中数组
      groupSourceTreeChecked: [],
      // 全部角色树
      defaultJsProps: {
        label: 'qzmc',
        id: 'qzid',
        children: 'children'
      },
      // 部门管理树结构数据格式
      defaultProps: {
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      },
      // 资源树结构数据格式
      yyProps: {
        label: 'yymc',
        id: 'yyid',
        children: 'children'
      },
      // 部门管理树结构默认展开数组
      defaultTree: [],
      // 当前部门
      currentBm: ''
    };
  },
  watch: {
    treeinput(val) {
      // tree 结构搜索过滤
      this.$refs.groupTreeLeft.filter(val);
    }
  },
  created() {
    this.getLeftGroupTree();
  },
  mounted() {
    // 角色树默认展开几级
    setTimeout(() => {
      // this.expendDefaultLevel(this.groupTree, 1, 2);
    }, 2000);
    this.getGroupId();
  },
  methods: {
    // 获取groupId
    getGroupId() {
      this.groupId = this.$refs.groupTreeLeft.getCurrentKey();
    },
    // 左侧 树结构 功能------------------------------------------
    // 获取左侧 群组数据tree
    getLeftGroupTree() {
      this.groupTreeLoading = true;

      // 获取 群组数据接口
      getGroupTree().then((res) => {
        this.groupTree = res.data.content || [];
      }).finally(() => {
        this.groupTreeLoading = false;
      });
    },
    /**
     * tree node 过滤左侧树组件的过滤
     * @param value
     * @param data
     * @returns {boolean}
     */
    filterNode(value, data) {
      if (!value) return true;
      return data.qzmc.indexOf(value) !== -1;
    },
    /**
     * 一级群组新增弹窗事件(新建)
     */
    addGroup() {
      this.qzEditForm = {
        sjqzid: '',
        qzid: '',
        qzmc: '',
        pxh: ''
      };
      this.editDialogType = 'new';
      this.qzEditFormVisible = true;
    },
    /**
     * 一级群组新增/修改
     * @param formName
     */
    qzConfirm() {
      this.$refs.qzEditRef.$refs.form.validate((valid) => {
        if (valid) {
          // 群组新增/ 新增下级
          this.qzConfirmLoading = true;
          const params = cloneDeep(this.qzEditForm);
          addGroupTree(params).then(() => {
            this.groupId = '';
            this.$message.success('添加成功');
            this.qzEditFormVisible = false;
            // 刷新群组列表
            this.getLeftGroupTree();
          }).finally(() => {
            this.qzConfirmLoading = false;
          });
        }
      });
    },
    /**
     * 关闭一级角色窗口时处理
     * @param formName
     */
    qzClose() {
      this.$refs.qzEditRef.$refs.form.clearValidate();
      this.qzEditFormVisible = false;
    },
    /**
     * tree 默认代开层级
     * @param data
     * @param startLevel
     * @param stopLevel
     */
    expendDefaultLevel(data, startLevel, stopLevel) {
      this.defaultTree = [];
      const handleTree = (dataTree, level, needLevel) => {
        dataTree.forEach((item) => {
          // this.$set(item, 'privateLevel', level);
          item.privateLevel = level;
          if (item.privateLevel <= needLevel) {
            this.defaultTree.push(item.bmm);
          }
          if (item.privateLevel <= needLevel && item.children && item.children.length > 0) {
            const index = item.privateLevel + 1;
            handleTree(item.children, index, needLevel);
          }
        });
      };
      handleTree(data, startLevel, stopLevel);
    },

    // 右键事件
    openMenu(event, item) {
      // 设置 右键菜单列表
      this.rightMenuOptions.list = [
        {
          label: '查看详情',
          onClick: () => {
            this.groupId = item.qzid;
            this.getDetail(item);
            // 设置 左侧树结构 选择状态
            this.$refs.groupTreeLeft.setCurrentKey(item.qzid);
          }
        },
        {
          label: '新建子群组',
          onClick: () => {
            this.groupId = item.qzid;
            this.addChildGroup(item);
          }
        },
        {
          label: '编辑',
          onClick: () => {
            this.groupId = item.qzid;
            this.rightMenuEdit(item);
            // 设置 左侧树结构 选择状态
            this.$refs.groupTreeLeft.setCurrentKey(item.qzid);
          }
        },
        {
          label: '删除',
          onClick: () => {
            this.groupId = item.qzid;
            this.deleteGroup(item);
          },
          style: 'color:red'
        }
      ];
      // 右键菜单展示
      this.$refs.rightMenuPart.showRightMenu();
      // 获取当前点击位置数据信息 mouseevent
      this.rightMenuOptions.event = event;
      // 获取当前点击的dom
      this.rightMenuOptions.ref = this.$refs.rightMenuPart.$el;
    },
    // 右键菜单 编辑群组详情
    rightMenuEdit(item) {
      this.getDetail(item);
      this.$refs.detail.isEdit = false;
    },
    // 右键 群组详情查看
    getDetail(item) {
      this.activeName = 'roleDetail';
      // 按钮置为 '保存'状态 页面不可编辑
      this.$refs.detail.isEdit = true;

      // 模拟接口 获得数据
      this.formDataLoading = true;
      const params = {
        qzid: item.qzid
      };
      // 获取群组详情数据
      getGroupBase(params).then((res) => {
        this.formData = res.data.content || {
          qzid: '',
          qzmc: '',
          pxh: '',
          cjr: '',
          cjsj: '',
          bgr: '',
          bgsj: ''
        };
      }).finally(() => {
        this.formDataLoading = false;
      });
    },
    // 右键 菜单 新增子群组
    addChildGroup(item) {
      this.qzEditForm = {
        sjqzid: item.qzid,
        qzid: '',
        qzmc: '',
        pxh: ''
      };
      this.editDialogType = 'edit';

      this.qzEditFormVisible = true;
    },
    // 右键菜单 删除 群组
    deleteGroup(item) {
      // 设置参数
      const config = {
        text: '是否删除?',
        btnText: '执行中...',
        title: '',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        const params = {
          qzid: item.qzid
        };
        // 删除群组接口
        deleteGroupBase(params).then(() => {
          this.$message.success('删除成功');
          // 刷新左侧群组树
          this.groupId = '';
          this.getLeftGroupTree();
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 右键 操作列表 end--------------------------------------------

    // 群组树 点击后执行 查询当前详情的数据
    handleNodeClick(data) {
      // 取消右键弹窗
      this.$refs.rightMenuPart.hideRightMenu();
      this.getDetail(data);
      this.getGroupId();
    },
    // 左侧功能 end--------------------------------------------------

    // 右侧 详情tab 保存功能
    saveForm() {
      // 保存群组详情接口
      this.formDataLoading = true;
      updateGroupBase(this.formData).then(() => {
        this.$refs.detail.isEdit = !this.$refs.detail.isEdit;
        this.$message.success('保存成功');
      }).finally(() => {
        this.formDataLoading = false;
      });
    },
    // 右侧 tab change 操作
    groupTabChange(tab, event) {
      // 获取当前 选中的group
      this.getGroupId();
      // 获取 当前tab active
      const tabName = tab.name;
      if (this.groupId) {
        switch (tabName) {
          case 'roleDetail':
            this.getDetail({ qzid: this.groupId });
            break;
          case 'roleUser':
            this.getGroupUser({ qzid: this.groupId });
            break;
          case 'roleSource':
            this.getGroupSource({ qzid: this.groupId });
            break;
          default:
        }
      } else {
        this.$message.warning('未选择群组');
      }
    },
    // 群组下用户查询
    getGroupUser() {
      this.getGroupId();
      const params = {
        qzid: this.groupId,
        page: this.page,
        pageSize: this.pageSize,
        ...this.departmentSearch
      };
      this.groupUserLoading = true;
      // 获取群组下用户 table数据
      getGroupUser(params).then((res) => {
        this.tableDataUser = res.data.content || [];
        this.total = res.data.pageInfo.total || 0;
      }).finally(() => {
        this.groupUserLoading = false;
      });
    },
    /**
     * 群组下用户查询重置事件
     */
    resetGroupUser() {
      this.page = 1;
      this.departmentSearch.zhid = '';
      this.getGroupUser();
    },
    /**
     * 群组下用户 pageSize 分页
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.getGroupUser();
    },
    /**
     * 群组下用户 页面page 更改
     * @param val
     */
    handleCurrentChange(val) {
      this.page = val;
      this.getGroupUser();
    },
    /**
     * 删除群组下用户 某个
     * @param val
     */
    delCurrent(val) {
      // 设置参数
      const config = {
        text: '是否删除?'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        const params = {
          sjjlid: val.sjjlid
        };
        deleteGroupUser(params).then(() => {
          const index = this.tableDataUser.findIndex((x) => x.sjjlid === val.sjjlid);
          if (index > -1) {
            this.tableDataUser.splice(index, 1);
          }
          this.$message.success('删除成功');
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 添加群组下用户的弹窗
    // 新增群组下用户
    showYhDialog() {
      // 展示弹窗
      this.$refs.chooseTree.showDialog();
      this.$nextTick(() => {
        this.searchInfo({ init: true });
      });
    },
    // search 搜索
    searchInfo(val) {
      const isInit = val.init;
      this.getGroupId();
      if (isInit) {
        this.$refs.chooseTree.clearNode();
      }
      this.treeDetailLoading = true;
      const params = {
        page: isInit ? 1 : this.userPage,
        pagesize: 50,
        qzid: this.groupId,
        zhid: '',
        zhzt: 1
      };
      if (val.info) {
        params.zhid = val.info;
      }

      // 获取群组用户数据 树
      getPageTabel(params).then((res) => {
        this.treeData = res.data.content || [];
        this.userTotal = res.data.pageInfo.total || 0;
        // // 设置已经选中的数据
        // this.$refs.chooseTree.toggleSelection([{ label: '1', id: 1 },
        //   { label: '2', id: 2 }]);
      }).finally(() => {
        // this.treeData = [
        //   {
        //     zhid: '121212',
        //     xm: '11111'
        //   }
        // ];
        this.treeDetailLoading = false;
      });
    },
    // page更改
    pageChange(val) {
      this.userPage = val.page;
      this.searchInfo({ init: false });
    },
    // 取消
    closeFixedPost() {
      this.$refs.chooseTree.hideDialog();
    },
    // 确定
    getFixedPost(val) {
      // 保存 编辑新增的群组下用户
      const params = {
        zhid: val.map((x) => x.zhid),
        qzid: this.groupId
      };
      this.chooseLoading = true;
      // 保存群组下新增用户
      updateGroupUserTree(params).then(() => {
        this.$message.success('数据修改成功');
        this.closeFixedPost();
      }).finally(() => {
        this.chooseLoading = false;
      });
    },
    // 添加群组下用户
    //
    // 授权tab页-----------------------------------
    // 查询应用资源
    getGroupSource(val) {
      this.getGroupId();
      this.groupSourceLoading = true;
      // 模拟加载数据
      const params = {
        qzid: this.groupId
      };
      // 获取 应用资源接口
      getSourceTree(params).then((res) => {
        this.yyTree = res.data.content || [];
      }).finally(() => {
        this.groupSourceLoading = false;
        // this.yyTree = [
        //   { yyid: '1', yymc: '111111' },
        //   { yyid: '2', yymc: '222222' },
        //   { yyid: '8', yymc: '333333' },
        //   { yyid: '9', yymc: '444444' }
        // ];
      });
    },
    // 编辑引用资源 获取资源数据
    getMmodifySource() {
      // 展示弹窗
      this.$refs.roleTree.showDialog();
      // 模拟获取资源数据
      this.$nextTick(() => {
        this.$refs.roleTree.loading = true;
        this.getGroupId();
        const params = {
          qzid: this.groupId
        };
        getSourceTreeAll(params).then((res) => {
          this.groupSourceTree = res.data.content || [];
          // 已选中的数据
          this.groupSourceTreeChecked = [];
        }).finally(() => {
          this.$refs.roleTree.loading = false;
          /*  this.groupSourceTree = [
            {
              yyid: 1,
              yymc: '一级 1',
              sjyyid: null,
              disabled: false, // 可选
              children: [{
                yyid: 4,
                yymc: '二级 1-1',
                sjyyid: 1,
                disabled: false, // 可选
                children: [{
                  sjyyid: 4,
                  yyid: 9,
                  disabled: false, // 可选
                  yymc: '三级 1-1-1'
                }, {
                  sjyyid: 4,
                  yyid: 10,
                  disabled: false, // 可选
                  yymc: '三级 1-1-2'
                }]
              }]
            }
          ]; */
          this.groupSourceTreeChecked = this.yyTree.map((x) => x.yyid);
        });
      });
    },
    // 授权资源抽屉确定
    sourceConfirm(val) {
      // 模拟获取资源数据
      this.$refs.roleTree.loading = true;
      const params = {
        qzid: this.groupId,
        yyidlist: val.map((x) => x.yyid)
      };
      // 授权资源数据更新接口
      updateSourceTree(params).then(() => {
        this.$message.success('数据更新成功');
        this.$refs.roleTree.hideDialog();
        this.getGroupSource();
      }).finally(() => {
        this.$refs.roleTree.loading = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.qzgl-content {
  display: flex;
  flex-direction: column;

  .content {
    display: flex;
    flex: 1;
    overflow: auto
  }

  .title {
    background-color: #FFFFFF;
    padding: 10px 10px 0;
  }

  .rightMenu {
    background-color: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);

    ul {
      padding: $page-content-padding;

      li {
        padding: 5px;
        border-bottom: 1px solid $page-bg-color;
        cursor: pointer;

        &:hover {
          color: $page-font-hover-color;
        }
      }
    }
  }

  &-left {
    width: 300px;
    height: 100%;
    padding: $page-content-padding;
    flex-shrink: 0;
    margin-right: $page-content-padding;
    background-color: #ffffff;

    &-content {
      position: relative;
      padding-top: 70px;
      height: 100%;

      .tree-top-part {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }
    }

    &-tree {
      height: 100%;
      overflow: auto;
    }
  }

  &-right {
    padding: $page-content-padding;
    flex: 1;
    background-color: #ffffff;
    overflow: auto;
    .qzgl-user-edit-none{
      text-align: center;
      padding-top: 20px;
      color: #a3a3a3;
    }

    .button-tab {
      margin-bottom: $page-content-padding;
    }

    .content-tab-pane {
      height: 100%;
      overflow: auto;
    }

    .role-source {
      position: relative;
      padding-top: 40px;

      &-button {
        position: absolute;
        top: 0;
        left: 0
      }
    }
  }
}
</style>
<style lang="scss">
.qzgl-content {
  .el-tabs__content {
    height: calc(100% - 42px)
  }

  .custom-tree-node {
    width: 100%;
  }
}
</style>
