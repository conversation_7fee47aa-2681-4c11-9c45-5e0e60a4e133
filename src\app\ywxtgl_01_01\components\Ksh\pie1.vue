<template>
  <div ref="myChart" style="width: 100%;height: 100%;box-sizing: border-box;"></div>
</template>

<script>
// 引入基本模板
import { echarsMixins } from '@/app/ywxtgl_01_01/mixins/echars-resize';

const echarts = require('echarts/lib/echarts');

// 引入饼状图组件
require('echarts/lib/chart/pie');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');

export default {
  mixins: [echarsMixins],
  props: {
    data: {
      type: Array
    },
    colorlist: {
      type: Array,
      default() {
        return ['#49c7ad', '#ffa55b', '#4a90e2', '#CB5555'];
      }
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    data(newValue, oldValue) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);
      this.echarsDom = myChart;

      const data = this.data;
      const colorlist = this.colorlist;

      const option = {
        legend: {
          top: '85%',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#000',
            fontSize: '12'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)'
        },
        color: colorlist,
        series: [{
          type: 'pie',
          // 如果radius是百分比则必须加引号
          radius: [0, '60%'],
          center: ['50%', '45%'],
          data,
          // 修饰饼形图文字相关的样式 label对象
          label: {
            show: false,
            fontSize: 10
          },
          // 修饰引导线样式
          labelLine: {
            show: false,
            // 连接到图形的线长度
            length: 10,
            // 连接到文字的线长度
            length2: 10
          }
        }]
      };

      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
