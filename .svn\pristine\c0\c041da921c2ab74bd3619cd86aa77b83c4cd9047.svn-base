<template>
  <div>
    <el-tabs v-if="navbarList.length>0" v-model="activeTabName" @tab-click="handleClick">
      <el-tab-pane v-for="item in navbarList" :key="item.id" :label="item.id" :name="item.id"><span slot="label"><i v-if="item.icon" class="icon-tab" :class="item.icon"></i>{{item.name}}</span></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'VTab',
  props: {
    navbarList: {
      type: Array,
      default: () => []
    },
    activeName: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {

    };
  },
  computed: {
    activeTabName: {
      get() {
        return this.activeName;
      },
      set(val) {
        this.$emit('update:activeName', val);
      }
    }
  },
  methods: {
    handleClick(tab) {
      this.$emit('changeTab', tab);
    }
  }
};
</script>

<style lang="scss" scoped>
.icon-tab{
  margin-right: ($page-content-margin/2);
  font-size: $title-font-size;
}

</style>
