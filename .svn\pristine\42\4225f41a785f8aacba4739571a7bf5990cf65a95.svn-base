<template>
  <div :style="`height: ${scrollerHeight}`">
    <el-table
      :data="tableData"
      stripe
      border
      class="zhxy-table"
      @selection-change="handleSelectionChange"
      height="calc(100% - 40px)">
      <el-table-column align="center" type="selection"></el-table-column>
      <el-table-column v-if="false" prop="id"></el-table-column>
      <el-table-column type="index" label="序号" width="52"></el-table-column>
      <el-table-column prop="xm" label="姓名" width="100"></el-table-column>
      <el-table-column prop="yhid" label="职工号" width="100"></el-table-column>
      <el-table-column prop="bmmc" label="所属部门" width=""></el-table-column>
      <el-table-column prop="yhzt" label="用户状态" width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.yhzt === 0">停用</span>
          <span v-else-if="scope.row.yhzt === 1">启用</span>
        </template>
      </el-table-column>
      <el-table-column prop="sfzz" label="任职方式" width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.sfzz == 0">兼职</span>
          <span v-else-if="scope.row.sfzz == 1">主职</span>
        </template>
      </el-table-column>
      <el-table-column prop="sjh" label="联系电话（手机）" width="140"></el-table-column>
      <el-table-column prop="dzxx" label="联系方式（邮箱）" width="140"></el-table-column>
<!--      <el-table-column prop="" label="操作" width="80">-->
<!--        <template slot-scope="scope">           &lt;!&ndash; (scope.row) 为单个行对象 &ndash;&gt;-->
<!--          <el-button size="small" @click="action(scope.row)" type="text">调出</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>
    <div class="pageIn">
      <el-pagination
        :page-size="pageSize"
        @size-change="departmentUserPageSizeChange"
        @current-change="departmentUserCurrentPage"
        :current-page="page"
        :page-sizes="[30, 50, 100, 200,500]"
        layout="total, sizes, prev, pager, next, jumper"
        :total=total>
      </el-pagination>
    </div>
  </div>

</template>

<script>
export default {
  name: 'DepartmentUser',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: [String, Number],
      default: 0
    },
    total: {
      type: Number,
      default: 0
    },
    page: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 30
    }
  },
  data() {
    return {
      // table select array
      multipleSelection: []
    };
  },
  methods: {
    action(val) {
      this.$emit('delDepartmentUserCurrent', val);
    },
    handleSelectionChange(val) {
      console.log(val);
      this.multipleSelection = val;
    },
    /**
     * 部门用户 pagesize change
     * @param val
     */
    departmentUserPageSizeChange(val) {
      this.$emit('departmentUserPageSizeChange', val);
    },
    /**
     * 部门用户 当前page
     * @param val
     */
    departmentUserCurrentPage(val) {
      this.$emit('departmentUserCurrentPage', val);
    }
  }
};
</script>

<style lang="scss" scoped>
.button-tab {
  margin-bottom: $page-content-padding;
}

.department-user {
  .zhxy-form-inline {
    width: 60%;
    min-width: 500px;
    margin-right: 0;
  }

  .zhxy-form.zhxy-form-search-part.form-status-edit {
    .el-form-item {
      margin-bottom: 20px !important;
    }
  }
}
</style>
