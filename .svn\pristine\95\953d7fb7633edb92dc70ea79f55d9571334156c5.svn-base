<template>
  <div>
    <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
      <el-table-column prop="yjbs" label="预警标识" width="180" v-if="false"></el-table-column>
      <el-table-column type=index label="序号" width="60" ></el-table-column>
      <el-table-column prop="yjlb" label="预警类别" width="180" ></el-table-column>
      <el-table-column prop="yjfl" label="预警分类" width="180"></el-table-column>
      <el-table-column prop="yjmc" label="预警名称" width="180" >
        <template slot-scope="scope">
          <router-link :to="{path:'/xtjkyyj/yjlsgl',query:{yjbs:scope.row.yjbs,glywid:scope.row.glywid}}">
            <span>{{scope.row.yjmc}}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column prop="yjms" label="故障预警描述" min-width="180" ></el-table-column>
      <el-table-column prop="yjjbm" label="预警级别码" width="180" v-if="false"></el-table-column>
      <el-table-column prop="yjjbmc" label="预警级别" width="180" ></el-table-column>
      <el-table-column prop="yjfs" label="预警方式" width="180" ></el-table-column>
      <el-table-column prop="scsj" label="生成时间" width="180">
        <template slot-scope="scope">
          <div v-if="scope.row.scsj != '' && scope.row.scsj != null">
            {{dayjs(scope.row.scsj).format('YYYY-MM-DD HH:mm:ss')}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="gzfscjsj" label="故障发生采集时间" width="160">
        <template slot-scope="scope">
          <div v-if="scope.row.gzfscjsj != '' && scope.row.gzfscjsj != null">
            {{dayjs(scope.row.gzfscjsj).format('YYYY-MM-DD HH:mm:ss')}}
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="" label="操作" width="100">
        <template slot-scope="scope">
          <el-button size="small" @click="modifySfhl(scope.row)" type="text" >
            <span v-if="scope.row.sfhl === 0">忽略</span>
            <span v-else-if="scope.row.sfhl === 1">处理</span>
          </el-button>
          <el-button size="small" @click="del(scope.row)" type="text" >
            <span>删除</span>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'YjxxglTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dayjs
    };
  },
  methods: {
    modifySfhl(val) {
      this.$emit('modifySfhl', val);
    },
    del(val) {
      this.$emit('del', val);
    }
  }
};
</script>

<style scoped>

</style>
