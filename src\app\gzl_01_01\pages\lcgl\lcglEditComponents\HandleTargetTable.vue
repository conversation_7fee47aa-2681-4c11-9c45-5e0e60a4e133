<template>
  <div class="info-table">
    <el-table
      ref="multipleTable"
      class="zhxy-table"
      :data="tableData"
      stripe
      border
      height="100%"
      @row-click="getHandleTargetRow"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" label="" width="50"></el-table-column>
      <el-table-column prop="clmblx" label="处理目标名称">
        <template slot-scope="scope">
          <span>{{list[scope.row.clmblx]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理目标明细" show-overflow-tooltip>
        <template slot-scope="scope">
          <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap"  class="cell-row" v-if="scope.row.checked && isShowDetail(scope.row.clmblx)">
            <span v-if="!scope.row.clmbmx"><el-button @click="selectHandleTargetDetail(scope.row)" type="text">请选择办理类型</el-button></span>
            <span style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap" v-else class="button-text" @click="selectHandleTargetDetail(scope.row)">{{scope.row.clmbmx}}</span>
          </div>
          <div class="cell-row" v-else>
          </div>
         </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    // table数据
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 选中selection list
      multipleSelection: [],
      list: {
        1: '起草者',
        2: '起草单位固定岗位类型',
        3: '起草单位任意岗位',
        4: '起草单位任意人员',
        5: '本单位固定岗位类型',
        6: '本单位任意岗位',
        7: '本单位任意人员',
        8: '任意单位固定岗位类型',
        9: '任意单位任意岗位',
        10: '任意单位任意人员',
        11: '',
        12: '指定单位固定岗位',
        13: '指定单位固定人员',
        14: '指定单位任意岗位',
        15: '指定单位任意人员'
      }
    };
  },
  computed: {
    isShowDetail() {
      return (val) => {
        const list = [
          2, 5, 8, 12, 13, 15
        ];
        return list.some((x) => x === val);
      };
    }
  },
  methods: {
    /**
     * 默认勾选row设置
     * @param rows
     */
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    /**
     * selection change 事件
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.tableData.forEach((originData) => {
        originData.checked = false;
      });
      this.multipleSelection.forEach((item) => {
        this.tableData.forEach((originData) => {
          if (originData.clmblx === item.clmblx) {
            originData.checked = true;
          }
        });
      });
    },
    /**
     * 处理目标 按钮(文字)点击事件
     * @param val
     */
    selectHandleTargetDetail(val) {
      this.$emit('selectHandleTargetDetail', val);
    },
    /**
     * 获取当前table row 数据
     * @param row
     */
    getHandleTargetRow(row) {
      this.$emit('getHandleTargetRow', row);
    }
  }
};
</script>

<style lang="scss" scoped>
  .info-table {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    height: 100%;
    .cell-row{
      height: 42px;
      line-height: 42px;
    }
    .button-text{
      color: $page-font-hover-color;
      cursor: pointer;
    }
  }
</style>
