import store from '@/store';
import { sessionData } from '@/utils/local-utils'; // 获取权限列表
import router, { asyncRoutes, resetRouter } from '../../../router/index'; // 引入 router 和 所有 asyncRoutes
import {
  getRoleList, getUserBaseInfo, challengeUrl, getIdauthUrl
} from '../api/user/user';
import { authLoginApi } from '../api/login/login-api';

let reload = false; // 判断页面是否需要addrouter  刷新后是 false; 加载router后是 true;

const STORE_LAYOUT = 'layout_03_01'; // 用户信息 store 存储路径

/**
 * whiteList中配置免登录的路径，在后台系统中主要为登录页和404页面
 */
const whiteList = ['/login', '/404', '/403', '/login_common']; // 免登录白名单

/**
 * 停止页面切换前pending中的请求
 */
const stopAxiosPending = () => {
  // const axioswhiteList = ['/layout_03_01/logout/logout&post'];
  window.axiosPromiseArr.forEach((el, index) => {
    console.log(el);
    el.f(); // 路由跳转之前，中止请求
    //  重置 window.axiosPromiseArr
    delete window.axiosPromiseArr[index];
  });
};

router.beforeEach( async (to, from, next) => {
  if (whiteList.indexOf(to.path) === -1) {
    stopAxiosPending();
  }

  const options = {
    needDebug: false, //是否需要打印调试信息
    challengeUrl: "/sys/xtgl/sso/getRandom", //到业务应用系统获取挑战数的地址
    idauthUrl: "/sys/xtgl/sso/getUserInfo", //到业务应用系统获取挑战数的地址
    controlId: "399c24fa-efb9-425e-8879-ea84941a394a", //插件object的id
    allowDefaultPwdLoginPage: true, //单点登录失败后，是否允许跳转到已有的口令登录页面
    simpleLoginUrl: "http://222.72.37.36/portal/login.jsp", // 已有的口令登录页面
    webSocketUrl:"ws://127.0.0.1:30318"
  }
  const challenge = await challengeUrl();
  if (challenge.code !== 0) {
    return;
  }
  const currChallenge = challenge.message;
  if (!currChallenge) {
    return;
  }
  const body = "<?xml version=\"1.0\" encoding=\"utf-8\"?><getsignandtokenreq version=\"1\"><challenge>" + currChallenge + "</challenge></getsignandtokenreq>";

  // 获取WebSocket消息体长度
  let bodyLength = 0;
  for (let i = 0; i < body.length; i++) {
    if (body.charAt(i).match(/^[\u4e00-\u9fa5]+$/)) {
      bodyLength += 3;
    } else {
      bodyLength++;
    }
  }

  bodyLength = String(bodyLength);
  let length = 10 - bodyLength.length;
  for (let i = 0; i < length; i++) {
    bodyLength = "0" + bodyLength;
  }

  // 创建到认证客户端WebSocket服务的连接
  const webSocket = new WebSocket(options.webSocketUrl);
  let ticketData = null;
  webSocket.onopen = function (event) {
    webSocket.send(body);
  }
  webSocket.onerror = function (event) {
    // 未登录时，没有进入到错误事件方法里
    const notice = "申请票据时，客户端处理错误!"
    handleSingleLoginFailed(options.allowDefaultPwdLoginPage, options.simpleLoginUrl, '/', notice);
  }
  webSocket.onclose = function (event) {
    if (!ticketData) {
      let notice = "客户端未返回票据信息";
      handleSingleLoginFailed(options.allowDefaultPwdLoginPage, options.simpleLoginUrl, '/', notice);
    }
  }
  webSocket.onmessage = function (event) {
    ticketData = event.data;
    if (!ticketData) {
      handleSingleLoginFailed(options.allowDefaultPwdLoginPage, options.simpleLoginUrl, '/', "客户端未返回票据信息，请检查是否插KEY、是否配置设备以及设备是否工作正常等");
    }
    let result = ticketData.split("</result>")[0].split("<result>")[1];
    if (result == 0) {
      const signAndToken = ticketData.split("</tokeninfo>")[0].split("<tokeninfo>")[1];
      idauthSubmit(currChallenge,signAndToken, options.idauthUrl, options,to, next);
    } else {
      const notice = ticketData.split("</errorinfo>")[0].split("<errorinfo>")[1];
      handleSingleLoginFailed(options.allowDefaultPwdLoginPage, options.simpleLoginUrl, options.failPageUrl, notice);
    }
  }

});

/**
 * 获取roleList
 * @param next
 * @param to
 */
// eslint-disable-next-line complexity
async function getCurrentRoleList(next, to) {
  const currentRoleId = store.state[STORE_LAYOUT].user.roleId;
  const currentUserInfo = store.state[STORE_LAYOUT].user.userInfo;
  const params = {
    jsid: currentRoleId,
    yhid: currentUserInfo.info.userId
  };
  store.commit(`${STORE_LAYOUT}/user/setRoleLoading`, true);
  try {
    const role = await getRoleList(params); // 配置动态路由 addRoutes
    store.commit(`${STORE_LAYOUT}/user/setRoleLoading`, false);
    const rolesList = role.data.content || [];
    if (to.path === '/' && rolesList.length === 0 && window.location.href.indexOf('/403') > -1) {
      if (currentUserInfo && currentUserInfo.info && currentUserInfo.info.loginMode && currentUserInfo.info.loginMode === 1) {
        await router.push({ path: '/login' });
      } else {
        await router.push({ path: '/login_common' });
      }

      store.commit(`${STORE_LAYOUT}/tagView/changeHistoryList`, []);
      // 清空资源list
      store.commit(`${STORE_LAYOUT}/user/setResourceList`, []);
      // 清空菜单list
      store.commit(`${STORE_LAYOUT}/user/setMenuList`, []);
      sessionData('clean', 'roleList');
      sessionData('clean', 'menuList');
      sessionData('clean', 'layout_03_01/tagView');
      sessionData('clean', 'userInfo');
      sessionData('clean', 'roleId');
      return;
    }
    if (rolesList.length === 0) {
      await router.push({ path: '/403' });
      return;
    }
    // eslint-disable-next-line no-use-before-define
    routerAdd(asyncRoutes, rolesList);
    if (Array.isArray(role.data.content)) {
      store.commit(`${STORE_LAYOUT}/user/setResourceList`, rolesList);
    } else {
      store.commit(`${STORE_LAYOUT}/user/setResourceList`, []);
    }
    next({
      ...to,
      replace: true
    }); // 保证路由已挂载
  } catch (e) {
    throw new Error('加载数据出错,请手动刷新');
  }
}

/**
 * 判断是否有用户基础信息
 * @returns {boolean}
 */
function isHasUserInfo() {
  const userInfo = store.state[STORE_LAYOUT].user.userInfo;
  const roleId = store.state[STORE_LAYOUT].user.roleId;
  if (!userInfo || !roleId) {
    return false;
  }
  return true;
}

/**
 * 获取并配置路由跳转
 * @param next
 * @param to
 * @returns {Promise<void>}
 */
async function routerNext(next, to) {
  // 获取资源列表
  // eslint-disable-next-line no-use-before-define
  const roleNow = sessionData('get', 'roleList');
  if (roleNow && isHasUserInfo()) {
    if (!reload) {
      const role = store.state[STORE_LAYOUT].user.resourceList;
      // eslint-disable-next-line no-use-before-define
      routerAdd(asyncRoutes, role);
      next({
        ...to,
        replace: true
      }); // 保证路由已挂载
    } else {
      next();
    }
  } else {
    try {
      // eslint-disable-next-line no-use-before-define
      await getUserInfo(next, to);
    } catch (err) {
      await router.push('/404');
      store.commit(`${STORE_LAYOUT}/user/setRoleLoading`, false);
    }
  }
}

/**
 * 判断用户基础信息是否存在
 */
async function getUserInfo(next, to) {
  if (!isHasUserInfo()) {
    let jsid = '';
    try {
      const response = await getUserBaseInfo();
      if (response.data.content.roleList) {
        jsid = response.data.content.roleList[0] ? response.data.content.roleList[0].jsid : '';
      }
      const userInfoCurrent = {
        info: response.data.content.data,
        list: response.data.content.roleList
      };
      store.commit(`${STORE_LAYOUT}/user/setLogin`, userInfoCurrent || {});
      store.commit(`${STORE_LAYOUT}/user/setLoginRole`, jsid);
    } catch (err) {
      await router.replace('/login');
      throw new Error('资源获取错误');
    }
  }

  await getCurrentRoleList(next, to);
}

/**
 * 真实挂载路由解析
 * @param  {Array} userRouter 后台返回的用户权限json
 * @param  {Array} allRouter  前端配置好的所有动态路由的集合
 * @return {Array} realRoutes 过滤后的路由
 */
function recursionRouter(allList, userList) {
  const accessedRouters = allList.filter((v) => {
    // eslint-disable-next-line no-use-before-define
    if (hasPermission(userList, v)) {
      return v;
    }
    return false;
  });
  return accessedRouters;
}

/**
 * 判断是否需要挂载路由
 * @param roles
 * @param route
 * @returns {boolean|*}
 */
function hasPermission(roles, route) {
  if (route.path) {
    const index = roles.findIndex((role) => route.path === role.path);
    if (index > -1) {
      route.meta.name = roles[index].name || route.meta.name;
      // route.meta.name = roles[index].id || route.meta.name;
      route.meta.parentId = roles[index].rootId || route.meta.rootId;
      route.meta.type = roles[index].openType || route.meta.openType;
      return true;
    }
    return false;
  }
  return true;
}

/**
 * 挂载到router实例上的真实routes
 * @param routeAll
 * @param routerRole
 */
function routerAdd(routeAll, routerRole) {
  const realRoleList = recursionRouter(routeAll, routerRole);
  // 暂时不进行判断 加载全部路由
  // const realRoleList = routeAll;
  const routes = [
    {
      path: '/',
      redirect: '/home',
      name: 'AppIndex',
      component: () => import('../pages/index/index'),
      children: [
        {
          path: '/home',
          name: 'Home',
          component: () => import('../pages/home/<USER>'),
          meta: {
            name: '首页',
            type: '1'
          }
        },
        {
          path: '/demo_03_01/demo',
          name: '样例测试',
          component: () => import('../../demo_03_01/pages/demo/demo'),
          meta: {
            name: '样例测试',
            type: '1'
          }
        },
        ...realRoleList
      ]
    },
    {
      path: '*',
      redirect: '/404'
    }
  ];
  resetRouter();
  router.options.routes = [];
  router.addRoutes(routes);
  router.options.routes.push(...routes);
  reload = true;
}

// 提交认证
async function idauthSubmit(challenge,signAndToken, idauthUrl, options,to,next) {
  // 由于未登录时，没有进入到错误事件方法里，因此这里先应急处理。
  let notice = null;
  if (signAndToken === "sign random error") {
    notice = "客户端处理错误，请确认是否进行身份认证！";
  } else if (!signAndToken) {
    notice = "票据为空，客户端处理错误!"
  }
  if (notice) {
    handleSingleLoginFailed(options.allowDefaultPwdLoginPage, options.simpleLoginUrl, options.failPageUrl, notice);
    return;
  }
  let params = {
    signAndToken: signAndToken,
    challenge:challenge
  };
  const idauth = await getIdauthUrl(params);
  if (idauth.code !== 0) {
    const notice = idauth.message;
    handleSingleLoginFailed(options.allowDefaultPwdLoginPage, options.simpleLoginUrl, options.failPageUrl, notice);
  }
  else{
      await authLoginApi();
      routerNext(next, to);
  }
}

function idauthViaWebSocket2(challenge, webSocketUrl, idauthUrl, options) {
  // 调用WebSocket进行挑战数签名并获取身份票据

}

function handleSingleLoginFailed(allowDefaultPwdLoginPage, pwdLoginPage, errorPage, notice) {
  if (allowDefaultPwdLoginPage && pwdLoginPage) {
    window.location.href = pwdLoginPage;
  } else if (!errorPage) {
    window.location.href = errorPage;
  } else {
    alert(notice);
  }
}
