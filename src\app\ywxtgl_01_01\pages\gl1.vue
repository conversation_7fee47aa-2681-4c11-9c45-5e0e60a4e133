<!--属性类型主组件-->
<template>
  <div class="app_content_gl">
    <div style="width: 100%;background-color: #FFFFFF;border: 4px;">

      <div style="padding: 0 20px;margin-top: 15px;">
        <div style="border-bottom: 2px solid #F5F5F5;">
          <div style="font-size: 16px;font-weight: 700;padding: 15px 0;">
            <div style="width: 100%;position: relative;">
              <div style="display: inline-block;position: relative;">
                <div style="height: 32px; line-height: 32px; font-size: 38px;display: inline-block;color: #0d4aa4;">S
                </div>
                <div style="margin-left: 2px;display: inline-block;">
                  <div style="letter-spacing: 0.5px;height: 20px;line-height: 20px;font-size: 18px;">系统预警情况</div>
                  <div style="font-size: 12px; color: #0d4aa4;height: 12px;line-height: 12px;">ystem alarm situation
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 100%;margin: 0 auto ;">
        <div style="display: inline-block;width: 100%;">
          <div style="padding: 10px;text-align: center;">

            <div style="width: 18%;display: inline-block;;vertical-align: bottom">
              <div style="width: 100%;display: inline-block;padding: 5px;">
                <div style="width: 100%;">
                  <div style="height: 70px;text-align: center;line-height: 70px;font-size: 22px; color:#ff6666;">
                    一级
                    <router-link :to="{path:'/xtjkyyj/yjxxgl',query:{yjjbm:4}}">
                      <span style="padding-left: 20px;">{{xtyjqk.gaoji}}个</span>
                    </router-link>
                  </div>

                </div>
              </div>

              <div style="width: 100%;display: inline-block;padding: 5px;">
                <div style="width: 100%;">
                  <div style="height: 70px;text-align: center;line-height: 70px;font-size: 22px;color:#f7bc16;">
                    二级
                    <router-link :to="{path:'/xtjkyyj/yjxxgl',query:{yjjbm:3}}">
                      <span style="padding-left: 20px;">{{xtyjqk.zhongji}}个</span>
                    </router-link>
                  </div>
                </div>
              </div>

              <div style="width: 100%;display: inline-block;padding: 5px;">
                <div style="width: 100%;">
                  <div style="height: 70px;text-align: center;line-height: 70px;font-size: 22px;color:#409eff;">
                    三级
                    <router-link :to="{path:'/xtjkyyj/yjxxgl',query:{yjjbm:2}}">
                      <span style="padding-left: 20px;">{{xtyjqk.diji}}个</span>
                    </router-link>
                  </div>
                </div>
              </div>
            </div>

            <div style="width: 82%;display: inline-block;">
              <div style="width: 100%;  display: inline-block;">
                <div style="width: 100%;background-color: #FFFFFF;">
                  <div style="height: 250px;">
                    <stackline :rqSet="xtyjqk.rqSet" :arr1="xtyjqk.arr1" :arr2="xtyjqk.arr2" :arr3="xtyjqk.arr3"></stackline>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <div style="width: 100%;background-color: #FFFFFF;border: 4px;">

      <div style="padding: 0 20px;margin-top: 15px;">
        <div style="border-bottom: 2px solid #F5F5F5;">
          <div style="font-size: 16px;font-weight: 700;padding: 15px 0;">
            <div style="width: 100%;position: relative;">
              <div style="display: inline-block;position: relative;">
                <div style="height: 32px; line-height: 32px;  font-size: 38px;display: inline-block;color: #0d4aa4;">H
                </div>
                <div style="margin-left: 2px;display: inline-block;">
                  <div style="letter-spacing: 0.5px;height: 20px;line-height: 20px;font-size: 18px;">硬件资源运行情况</div>
                  <div style="font-size: 12px; color: #0d4aa4;height: 12px;line-height: 12px;">ardware resource
                    operation situation
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 100%;">
        <div style="display: inline-block;width: 100%;padding: 0px;">
          <div style="padding: 10px;">

            <div style="width: 100%; display: inline-block;padding:5px 0;">
              <div style="width: 90%;margin: 0 auto;background-color: #FFFFFF;padding: 20px 0px;">
                <div style="text-align: left;line-height: 30px;font-size: 16px;">

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff;border-right: 0;position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">数据库服务器</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <router-link :to="{path:'/xtjkyyj/sjkgl'}">
                        <span style="padding-right: 20px;color: #0d4aa4; ">{{yjzyyxqk.sjkfwqsl}}</span>个
                      </router-link>
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff;border-right: 0;position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">存储总量</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <span style="padding-right: 20px;color: #0d4aa4; ">{{yjzyyxqk.sjkfwqcczl}}</span>G
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff;position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">存储使用率</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <span style="color: #0d4aa4; ">{{yjzyyxqk.sjkfwqccsyl}}%</span>
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff;border-right: 0; border-top: 0; position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">文件服务器</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <router-link :to="{path:'/xtjkyyj/fwqgl',query:{zjlx:2}}">
                        <span style="padding-right: 20px;color: #0d4aa4; ">{{yjzyyxqk.wjfwqsl}}</span>个
                      </router-link>
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff; border-right: 0;border-top: 0; position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">存储总量</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <span style="padding-right: 20px;color: #0d4aa4; ">{{yjzyyxqk.wjfwqcczl}}</span>G
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff; border-top: 0; position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">存储使用率</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <span style="color: #0d4aa4; ">{{yjzyyxqk.wjfwqccsyl}}%</span>
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff;border-right: 0; border-top: 0; position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">管理服务器</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <router-link :to="{path:'/xtjkyyj/fwqgl',query:{zjlx:1}}">
                        <span style="padding-right: 20px;color: #0d4aa4; ">{{yjzyyxqk.glfwqsl}}</span>个
                      </router-link>
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff; border-right: 0;border-top: 0; position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">存储总量</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <span style="padding-right: 20px;color: #0d4aa4; ">{{yjzyyxqk.glfwqcczl}}</span>G
                    </div>
                  </div>

                  <div
                    style="width: 33.33%;height: 60px;line-height: 60px;background: rgb(242, 245, 255);display: inline-block; border: 1px dashed #409eff; border-top: 0; position: relative; ">
                    <div style="display: inline-block;padding-left: 30px;"><img style="vertical-align: middle;" src="../assets/sy/dian-01.png"/></div>
                    <div style="display: inline-block;padding-left: 10px;">存储使用率</div>
                    <div style="position: absolute; top: 50%; right: 40px; margin-top: -30px ; ">
                      <span style="color: #0d4aa4; ">{{yjzyyxqk.glfwqccsyl}}%</span>
                    </div>
                  </div>

                </div>

              </div>
            </div>

            <div style="width: 100%;  display: inline-block;padding: 5px;">
              <div style="width: 100%;background-color: #FFFFFF;">
                <div style="height: 250px;width: 90%;margin: 0 auto;">
                  <div
                    style="font-size: 20px;writing-mode:vertical-lr;display: inline-block;width: 40px;line-height: 40px;color: #488bff;font-weight: bold;">
                    <img src="../assets/sy/dian-01.png" style="vertical-align: initial;"/>内存使用率TOP10
                  </div>
                  <div style="height: 100%;display: inline-block;width: calc(100% - 40px);">
                    <nc_zhu :data="ncsylTop10"></nc_zhu>
                  </div>
                </div>
              </div>
            </div>

            <div style="width: 100%;  display: inline-block;padding: 5px;">
              <div style="width: 100%;background-color: #FFFFFF;">
                <div style="height: 250px;width: 90%;margin: 0 auto;">
                  <div
                    style="font-size: 20px;writing-mode:vertical-lr;display: inline-block;width: 40px;line-height: 40px;color: #488bff;font-weight: bold;">
                    <img src="../assets/sy/dian-01.png" style="vertical-align: initial;"/>CPU使用率TOP10
                  </div>
                  <div style="height: 100%;display: inline-block;width: calc(100% - 40px);">
                    <nc_zhu :data="cpusylTop10"></nc_zhu>
                  </div>
                </div>
              </div>
            </div>

            <div style="width: 100%;  display: inline-block;padding: 5px;">
              <div style="width: 100%;background-color: #FFFFFF;">
                <div style="height: 250px;width: 90%;margin: 0 auto;">
                  <div
                    style="font-size: 20px;writing-mode:vertical-lr;display: inline-block;width: 40px;line-height: 40px;color: #488bff;font-weight: bold;">
                    <img src="../assets/sy/dian-01.png" style="vertical-align: initial;"/>存储使用率TOP10
                  </div>
                  <div style="height: 100%;display: inline-block;width: calc(100% - 40px);">
                    <nc_zhu :data="ccsylTop10"></nc_zhu>
                  </div>
                </div>
              </div>
            </div>

          </div>

        </div>

      </div>
    </div>

    <div style="width: 100%;background-color: #FFFFFF;border: 4px;" v-if="false">

      <div style="padding: 0 20px;margin-top: 15px;">
        <div style="border-bottom: 2px solid #F5F5F5;">
          <div style="font-size: 16px;font-weight: 700;padding: 15px 0;">
            <div style="width: 100%;position: relative;">
              <div style="display: inline-block;position: relative;">
                <div style="height: 32px; line-height: 32px;  font-size: 38px;display: inline-block;color: #0d4aa4;">S
                </div>
                <div style="margin-left: 2px;display: inline-block;">
                  <div style="letter-spacing: 0.5px;height: 20px;line-height: 20px;font-size: 18px;">软件资源运行情况</div>
                  <div style="font-size: 12px; color: #0d4aa4;height: 12px;line-height: 12px;">oftware resource
                    operation situation
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 50%;border-right: 2px solid #F5F5F5;border-bottom: 2px solid #F5F5F5;display: inline-block;">
        <div style="padding: 10px;">
          <div style="width: 80%; margin: 0 auto;">
            <div
              style="font-size: 20px;writing-mode:vertical-lr;display: inline-block;width: 80px;line-height: 40px;color: #488bff;font-weight: bold;">
              <img src="../assets/sy/dian-01.png" style="vertical-align: initial;"/>元数据采集任务情况
            </div>
            <div style="height: 100%;display: inline-block;width: calc(100% - 80px);   ">
<!--              <div style="width: 100%; display: inline-block;">-->
<!--                <div style="width: 90%;margin: 0 auto;padding: 20px 0;">-->
<!--                  <div style="text-align: center;line-height: 30px;font-size: 18px;">-->
<!--                    <div style="height: 40px;display: inline-block;">任务总数<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">10个</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">平均运行时间<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">5min</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">异常任务数<span-->
<!--                      style="padding-left: 10px;color: #FF6666;;">3个</span></div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
              <div style="height: 250px;">
                <zhuandxian :data="ysjcjrwqk"></zhuandxian>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 50%;border-bottom: 2px solid #F5F5F5;display: inline-block;">
        <div style="padding: 10px;">
          <div style="width: 80%; margin: 0 auto;">
            <div
              style="font-size: 20px;writing-mode:vertical-lr;display: inline-block;width: 80px;line-height: 40px;color: #488bff;font-weight: bold;">
              <img src="../assets/sy/dian-01.png" style="vertical-align: initial;"/>信息资源采集任务情况
            </div>
            <div style="height: 100%;display: inline-block;width: calc(100% - 80px);   ">
<!--              <div style="width: 100%; display: inline-block;">-->
<!--                <div style="width: 90%;margin: 0 auto;padding: 20px 0;">-->
<!--                  <div style="text-align: center;line-height: 30px;font-size: 18px;">-->
<!--                    <div style="height: 40px;display: inline-block;">任务总数<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">10个</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">平均运行时间<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">5min</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">异常任务数<span-->
<!--                      style="padding-left: 10px;color: #FF6666;">3个</span></div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
              <div style="height: 250px;">
                <zhuandxian :data="xxzycjrwqk"></zhuandxian>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 50%;border-right: 2px solid #F5F5F5;display: inline-block;vertical-align: top">
        <div style="padding: 10px;">
          <div style="width: 80%; margin: 0 auto;">
            <div
              style="font-size: 20px;writing-mode:vertical-lr;display: inline-block;width: 80px;line-height: 40px;color: #488bff;font-weight: bold;vertical-align: top">
              <img src="../assets/sy/dian-01.png" style="vertical-align: initial;"/>资源目录对象访问情况
            </div>
            <div style="height: 100%;display: inline-block;width: calc(100% - 80px);   ">
<!--              <div style="width: 100%; display: inline-block;">-->
<!--                <div style="width: 90%;margin: 0 auto;padding: 20px 0;">-->
<!--                  <div style="text-align: center;line-height: 30px;font-size: 18px;">-->
<!--                    <div style="height: 40px;display: inline-block;">访问次数<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">256次</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">访问用户数<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">45个</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">记录条数<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">32765条</span></div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
              <div style="height: 250px;">
                <div style="width: 50%; height: 100%; display: inline-block;">
                  <div
                    style="font-size: 14px;display: inline-block;width: 100%; height: 40px; line-height: 40px;text-align: center;">
                    访问用户分布
                  </div>
                  <div style="width: 100%;display: inline-block;height: calc(100% - 40px);   ">
                    <pie1 :data="pieData"></pie1>
                  </div>

                </div>
                <div style="width: 50%; height: 100%; display: inline-block;">
                  <div
                    style="font-size: 14px;display: inline-block;width: 100%; height: 40px; line-height: 40px;text-align: center;">
                    访问数据分类
                  </div>
                  <div style="width: 100%;display: inline-block;height: calc(100% - 40px);   ">
                    <pie1 :data="pieData1"></pie1>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="width: 50%;border-right: 2px solid #F5F5F5;display: inline-block;vertical-align: bottom">
        <div style="padding: 10px;">
          <div style="width: 80%; margin: 0 auto;">
            <div
              style="font-size: 20px;writing-mode:vertical-lr;display: inline-block;width: 80px;line-height: 40px;color: #488bff;font-weight: bold;">
              <img src="../assets/sy/dian-01.png" style="vertical-align: initial;"/>数据备份情况
            </div>
            <div style="height: 100%;display: inline-block;width: calc(100% - 80px);   ">
<!--              <div style="width: 100%; display: inline-block;">-->
<!--                <div style="width: 90%;margin: 0 auto;padding: 20px 0;">-->
<!--                  <div style="text-align: center;line-height: 30px;font-size: 18px;">-->
<!--                    <div style="height: 40px;display: inline-block;">备份次数<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">25次</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">备份条数<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">52765个</span></div>-->
<!--                    <div style="height: 40px;display: inline-block;padding-left: 30px;">文件大小<span-->
<!--                      style="padding-left: 10px;color: #0d4aa4;">2T</span></div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
              <div style="height: 250px;">
                <fb_pie></fb_pie>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
import {
  getXtgjqk, getYjzyyxqk, getNcsyl, getCpusyl, getCcsyl, getYsjcjrwqk, getXxzycjrwqk
} from '@/app/ywxtgl_01_01/api/gl.js';

import pie1 from '../components/Ksh/pie1.vue';
import stackline from '../components/Ksh/stackline.vue';
// eslint-disable-next-line camelcase
import nc_zhu from '../components/Ksh/nc_zhu.vue';
// eslint-disable-next-line camelcase
import nc_zhu1 from '../components/Ksh/nc_zhu1.vue';
// eslint-disable-next-line camelcase
import nc_zhu2 from '../components/Ksh/nc_zhu2.vue';
import zhuandxian from '../components/Ksh/zhuandxian.vue';
// eslint-disable-next-line camelcase
import fb_pie from '../components/Ksh/fb_pie.vue';

export default {
  name: 'ywxtgl-gl',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    pie1, stackline, nc_zhu, zhuandxian, fb_pie, nc_zhu1, nc_zhu2
  },
  data() {
    return {
      xtyjqk: {
        gaoji: 0,
        zhongji: 0,
        diji: 0,
        rqSet: [],
        arr1: [],
        arr2: [],
        arr3: []
      },
      yjzyyxqk: {
        wjfwqsl: 0,
        wjfwqcczl: 0,
        wjfwqccsyl: 0,
        glfwqccsyl: '37',
        glfwqsl: 1,
        glfwqcczl: 0,
        sjkfwqcczl: 0,
        sjkfwqsl: 1,
        sjkfwqccsyl: 0
      },
      ncsylTop10: {
        xData: [],
        yData: []
      },
      cpusylTop10: {
        xData: [],
        yData: []
      },
      ccsylTop10: {
        xData: [],
        yData: []
      },
      ysjcjrwqk: {
        xData: [],
        yData1: [],
        yData2: []
      },
      xxzycjrwqk: {
        xData: [],
        yData1: [],
        yData2: []
      },
      pieData: [
        { name: '个人用户', value: 200 },
        { name: '系统用户', value: 100 }
      ],
      pieData1: [
        { name: '基础信息类', value: 200 },
        { name: '主题信息类', value: 100 },
        { name: '部门信息类', value: 150 }
      ],
      barData: [
        { name: '类别1', value: 4758 },
        { name: '类别2', value: 3254 },
        { name: '类别3', value: 2454 },
        { name: '类别4', value: 2011 },
        { name: '类别5', value: 1654 },
        { name: '类别6', value: 1211 },
        { name: '类别7', value: 1211 },
        { name: '类别8', value: 254 }
      ],
      barData1: [
        { name: '系统1', gw: 50, zd: 70 },
        { name: '系统2', gw: 60, zd: 70 },
        { name: '系统3', gw: 59, zd: 70 },
        { name: '系统4', gw: 50, zd: 70 },
        { name: '系统5', gw: 50, zd: 70 },
        { name: '系统6', gw: 50, zd: 70 },
        { name: '系统7', gw: 50, zd: 70 },
        { name: '系统8', gw: 50, zd: 70 },
        { name: '系统9', gw: 50, zd: 70 },
        { name: '系统10', gw: 50, zd: 70 }
      ],
      barData2: [
        { name: 'xxx系统1', value: 4758 },
        { name: 'xxx系统2', value: 3254 },
        { name: 'xxx系统3', value: 2454 },
        { name: 'xxx系统4', value: 2311 },
        { name: 'xxx系统5', value: 2254 },
        { name: 'xxx系统6', value: 2111 },
        { name: 'xxx系统7', value: 2011 },
        { name: 'xxx系统8', value: 1904 },
        { name: 'xxx系统9', value: 1800 },
        { name: 'xxx系统10', value: 1700 },
        { name: 'xxx系统11', value: 1600 },
        { name: 'xxx系统12', value: 1500 },
        { name: 'xxx系统13', value: 1400 },
        { name: 'xxx系统14', value: 1300 },
        { name: 'xxx系统15', value: 1200 },
        { name: 'xxx系统16', value: 1111 },
        { name: 'xxx系统17', value: 1011 },
        { name: 'xxx系统18', value: 900 },
        { name: 'xxx系统19', value: 800 },
        { name: 'xxx系统20', value: 700 }
      ]
    };
  },
  computed: {
    barDataPin() {
      const { barData } = this;
      const xData = [];
      const yData = [];
      if (barData !== undefined) {
        // eslint-disable-next-line array-callback-return
        barData.map((item) => {
          xData.push(item.name);
          yData.push(item.value);
        });
      }
      return {
        xData,
        yData
      };
    },
    barDataPin1() {
      const { barData1 } = this;
      const xData = [];
      const gw = [];
      const zd = [];
      const zl = [];
      if (barData1 !== undefined) {
        // eslint-disable-next-line array-callback-return
        barData1.map((item) => {
          xData.push(item.name);
          gw.push(item.gw);
          zd.push(item.zd);
          zl.push(0);
        });
      }
      return {
        title: '近期元数据变化情况(新增/变更)',
        legend: ['高危', '中低'],
        xData,
        gw,
        zd,
        zl
      };
    },
    barDataPin2() {
      const { barData2 } = this;
      const xData = [];
      const yData = [];
      if (barData2 !== undefined) {
        // eslint-disable-next-line array-callback-return
        barData2.map((item) => {
          xData.push(item.name);
          yData.push(item.value);
        });
      }
      return {
        xData,
        yData
      };
    }
  },
  mounted() { // 页面初始化加载(只在页面初始时加载一次)
    this.search();
  },
  methods: {
    search() {
      getXtgjqk()
        .then((res) => {
          if (res.code === 200) {
            if ('gaoji' in res.data.content.zsl) {
              this.xtyjqk.gaoji = res.data.content.zsl.gaoji;
            }
            if ('zhongji' in res.data.content.zsl) {
              this.xtyjqk.zhongji = res.data.content.zsl.zhongji;
            }
            if ('diji' in res.data.content.zsl) {
              this.xtyjqk.diji = res.data.content.zsl.diji;
            }
            // this.xtyjqk.gaoji = res.data.content.zsl.gaoji;
            // this.xtyjqk.zhongji = res.data.content.zsl.zhongji;
            // this.xtyjqk.diji = res.data.content.zsl.diji;
            this.xtyjqk.arr1 = res.data.content.arr1;
            this.xtyjqk.arr2 = res.data.content.arr2;
            this.xtyjqk.arr3 = res.data.content.arr3;
            this.xtyjqk.rqSet = res.data.content.rqSet;
          }
        });
      getYjzyyxqk()
        .then((res) => {
          if (res.code === 200) {
            Object.assign(this.yjzyyxqk, res.data.content);
            console.log('开始处理多余小数');
            // 获取小数点的位置
            this.yjzyyxqk.glfwqccsyl = `${this.yjzyyxqk.glfwqccsyl }`;
            const index1 = this.yjzyyxqk.glfwqccsyl.lastIndexOf('.');
            if (index1 > 0) {
              this.yjzyyxqk.glfwqccsyl = this.yjzyyxqk.glfwqccsyl.substring(0, index1 + 2);
            }
            // 获取小数点的位置
            this.yjzyyxqk.wjfwqccsyl = `${this.yjzyyxqk.wjfwqccsyl }`;
            const index2 = this.yjzyyxqk.wjfwqccsyl.lastIndexOf('.');
            if (index2 > 0) {
              this.yjzyyxqk.wjfwqccsyl = this.yjzyyxqk.wjfwqccsyl.substring(0, index2 + 2);
            }

            // 获取小数点的位置
            this.yjzyyxqk.sjkfwqccsyl = `${this.yjzyyxqk.sjkfwqccsyl }`;
            const index3 = this.yjzyyxqk.sjkfwqccsyl.lastIndexOf('.');
            if (index3 > 0) {
              this.yjzyyxqk.sjkfwqccsyl = this.yjzyyxqk.sjkfwqccsyl.substring(0, index3 + 2);
            }
          }
        });
      getNcsyl()
        .then((res) => {
          if (res.code === 200) {
            this.ncsylTop10 = res.data.content;
          }
        });
      getCpusyl()
        .then((res) => {
          if (res.code === 200) {
            this.cpusylTop10 = res.data.content;
          }
        });
      getCcsyl()
        .then((res) => {
          if (res.code === 200) {
            this.ccsylTop10 = res.data.content;
          }
        });
      /* getYsjcjrwqk()
        .then((res) => {
          if (res.code === 200) {
            this.ysjcjrwqk = res.data.content;
          }
        }); */
      /* getXxzycjrwqk()
        .then((res) => {
          if (res.code === 200) {
            this.xxzycjrwqk = res.data.content;
          }
        }); */
    }
  },
  numFilter(value) {
    console.log(111);
    value = parseFloat(value);
    let realVal = '';
    // eslint-disable-next-line no-restricted-globals
    if (!isNaN(value) && value !== '') {
      // 截取当前数据到小数点后两位
      realVal = parseFloat(value).toFixed(2);
    } else {
      realVal = '--';
    }
    return realVal;
  }
};
</script>

<style scoped>
@import url('../styles/app0301.scss');
/*.app_content_gl * { max-height: 100%; }*/
.sbj {
  margin-top: 15px;
}

.el-input, .el-select {
  width: 100% !important;
}
</style>
<!--<style lang="scss" src="../styles/app0301.scss" scoped></style>-->
