<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">用户ID</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.yhid" placeholder="用户ID"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">姓名</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.xm" placeholder="姓名"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">身份描述</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.sfms" placeholder="身份描述"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">登录类型</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.dllx" placeholder="登录类型"
                   size="small">
          <el-option
            v-for="item in dllxOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">操作时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.czsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
        <p
          class="search-fold"
          @click="()=>{this.isShowLabel = !this.isShowLabel}">
          {{this.isShowLabel ? '收缩' : '展开'}}
          <i :class="!this.isShowLabel ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></p>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'yhglSearch',
  data() {
    return {
      isShowLabel: false,
      // label 宽度
      lableWidth: '120px',
      // labelWidth
      lableWidthSingle: '450px',
      // search params
      listQuery: {
        yhid: null,
        xm: null,
        sfms: null,
        dllx: null,
        kssj: null,
        jssj: null
      },
      dllxOptions: [{
        label: '登录',
        value: 0
      }, {
        label: '登出',
        value: 1
      }],
      // time date
      valueDate: ''
    };
  },
  methods: {
    /**
       * 搜索查询
       */
    search() {
      const param = {
        yhid: this.listQuery.yhid,
        xm: this.listQuery.xm,
        sfms: this.listQuery.sfms,
        dllx: this.listQuery.dllx,
        kssj: '',
        jssj: ''
      };
      if (this.listQuery.czsj) {
        param.kssj = this.listQuery.czsj[0];
        param.jssj = this.listQuery.czsj[1];
      }

      this.$emit('search', param);
    },
    /**
       * 重置搜索条件
       */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });
      this.search();
    }
  }
};
</script>

<style lang="scss" scoped>
  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
</style>
