import { createAPI } from '@/utils/request.js';

const BASE_URL = '';

/* 查询分页列表数据 */
export const findListByPage = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/findListByPage`, 'get', data);

/* 查询单条数据 */
export const findOneByID = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/findOneByID`, 'get', data);

/* 查询单条用户部门数据 */
export const findOne = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYhbm/findOne`, 'get', data);

/* 新增 */
export const add = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/add`, 'post', data);

/* 修改 */
export const update = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/update`, 'post', data);

/* 修改 */
export const updateYhbm = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYhbm/update`, 'post', data);

/* 删除 */
export const del = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/delete`, 'post', data);

/* 批量删除 */
export const deleteBatchYh = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/deleteBatchYh`, 'post', data);

/* 获取可添加的用户部门 */
export const findBmList = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/findBmList`, 'get', data);

/* 添加用户部门 */
export const addYhbm = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxYh/addYhbm`, 'post', data);
