<template>
  <el-table class="zhxy-table" :data="tableData" stripe border
            @selection-change="selectionChange"
            :height="scrollerHeight">
    <el-table-column align="center" type="selection" width="50"></el-table-column>
    <el-table-column prop="csbs" label="参数标识" width="150"></el-table-column>
    <el-table-column prop="csbssm" label="参数标识说明" width="150"></el-table-column>
    <el-table-column prop="csz" label="参数值" width="150" :show-overflow-tooltip="true">
    </el-table-column>
    <el-table-column prop="bz" label="备注"
                     show-overflow-tooltip></el-table-column>
    <el-table-column prop="cjr" label="创建人" width="120"></el-table-column>
    <el-table-column prop="cjsj" label="创建时间" width="180"
                     :show-overflow-tooltip="true">
      <template slot-scope="scope">
        {{scope.row.cjsj | dateFilter}}
      </template>
    </el-table-column>
    <el-table-column prop="bgr" label="变更人" width="130"></el-table-column>
    <el-table-column prop="bgsj" label="变更时间" :show-overflow-tooltip="true" width="180">
      <template slot-scope="scope">
        {{scope.row.bgsj | dateFilter}}
      </template>
    </el-table-column>
    <el-table-column fixed="right" prop="" label="操作" width="100">
      <template slot-scope="scope">
        <el-button size="small" @click="modifyYh(scope.row)" type="text">修改</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" type="text" @click="delYh(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';

export default {
  name: 'YhglTable',
  filters: {
    dateFilter
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {};
  },
  methods: {
    selectionChange(val) {
      console.log(val);
      this.$emit('handleSelectionChange', val);
    },
    modifyYh(val) {
      this.$emit('modifyYh', val);
    },
    delYh(val) {
      this.$emit('delYh', val);
    }
  }
};
</script>

<style scoped>

</style>
