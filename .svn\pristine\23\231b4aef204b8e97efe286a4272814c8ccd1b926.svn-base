<template>
  <div class="tagview" style="padding-right:60px">
    <el-tabs class="" v-model="editableTabsValue" type="card" closable
             @tab-remove="removeTab" @tab-click="changeTab">
      <el-tab-pane
        v-for="(item) in editableTabs"
        :key="item.path"
        :label="item.title"
        :name="item.name"
      >
      </el-tab-pane>
    </el-tabs>
    <div style="position: absolute;right: 20px;top: 5px">
      <el-dropdown @command="removeTag">
        <span class="el-dropdown-link" style="cursor: pointer">
          <div style="padding: 8px;background-color: #FFFFFF">
            <i class="el-icon-arrow-down"></i>
          </div>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="left"><i class="el-icon-back"></i>关闭当前页左侧</el-dropdown-item>
          <el-dropdown-item command="right"><i class="el-icon-right"></i>关闭当前页右侧</el-dropdown-item>
          <el-dropdown-item command="other"><i class="el-icon-close"></i>关闭其它</el-dropdown-item>
          <el-dropdown-item command="all"><i class="el-icon-error"></i>全部关闭</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { cacheActionMixins } from '@/app/layout_03_01/pages/mixin';
import { cloneDeep } from 'lodash';

const homeName = '/home';
export default {
  name: 'TagView',
  mixins: [cacheActionMixins],
  data() {
    return {
      home: homeName,
      editableTabsValue: homeName,
      editableTabs: [
        {
          title: '首页',
          name: homeName
        }
      ]
    };
  },
  computed: {
    ...mapState('layout_03_01/tagView', ['historyList'])
  },
  watch: {
    $route: {
      // 检测路由 状态
      // eslint-disable-next-line complexity
      handler(to) {
        if (to) {
          // tagview list 设置 -----------------------------------------------------
          if (!this.historyList || this.historyList.length === 0) {
            this.editableTabs = [{
              title: '首页',
              name: homeName,
              path: homeName
            }];
          } else {
            this.editableTabs = JSON.parse(JSON.stringify(this.historyList));
          }
          // const hasTag = this.editableTabs.some((x) => x.name === to.path);
          const index = this.editableTabs.findIndex((x) => x.name === to.path);
          if (index === -1) {
            this.editableTabs.push({
              title: to.meta.name,
              name: to.path,
              path: to.fullPath
            });
          } else {
            this.editableTabs[index].path = to.fullPath;
          }
          this.editableTabsValue = to.path;
          this.changeHistoryList(JSON.parse(JSON.stringify(this.editableTabs)));
        }
      },
      immediate: true
    }
  },
  created() {
    // console.log('tagView INNIT', this.historyList, this.editableTabsValue);
  },

  methods: {
    ...mapMutations('layout_03_01/tagView', ['changeHistoryList']),
    /**
     * 切换(路由)
     * @param targetName
     */
    changeTab(tab) {
      this.$router.push(tab.$vnode.key);
    },
    /**
     * 标签功能列表事件
     * @param command
     */
    removeTag(command) {
      const tabs = this.editableTabs;
      const activeName = `${this.editableTabsValue}`;
      const oldActiveName = `${this.editableTabsValue}`;
      let deleteName = '';
      let currentIndex = '';
      tabs.forEach((tab, index) => {
        if (tab.name === oldActiveName) {
          currentIndex = index;
        }
      });
      // 删除左侧标签
      const removeLeft = () => {
        if (currentIndex - 1 < 1) {
          return;
        }
        deleteName = tabs[currentIndex - 1].name;
        this.editableTabsValue = activeName;
        this.editableTabs = tabs.filter((tab) => tab.name !== deleteName);
      };
      // 删除右侧标签
      const removeRight = () => {
        if (currentIndex + 1 >= tabs.length) {
          return;
        }
        deleteName = tabs[currentIndex + 1].name;
        this.editableTabsValue = activeName;
        this.editableTabs = tabs.filter((tab) => tab.name !== deleteName);
      };
      // 删除其他标签
      const removeOther = () => {
        this.editableTabsValue = activeName;
        this.editableTabs = tabs.filter((tab) => (tab.name === activeName || tab.name === this.home));
      };
      // 删除全部标签
      const removeAll = () => {
        this.editableTabsValue = this.home;
        this.editableTabs = tabs.filter((tab) => (tab.name === this.home));
      };
      switch (command) {
        case 'left':
          removeLeft();
          break;
        case 'right':
          removeRight();
          break;
        case 'other':
          removeOther();
          break;
        case 'all':
          removeAll();
          break;
        default:
      }
      // 删除组件的缓存
      this.deleteFromCache(tabs, this.editableTabs);
      const current = this.editableTabs.filter((item) => item.name === this.editableTabsValue);
      let toPath = this.home;
      if (current.length > 0) {
        toPath = current[0].path;
      }
      this.changeTab({
        $vnode: {
          key: toPath
        }
      });
      this.changeHistoryList(JSON.parse(JSON.stringify(this.editableTabs)));
    },
    /**
     * 删除当前标签事件
     * @param targetName
     */
    removeTab(targetName) {
      const tabs = this.editableTabs;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.name;
            }
          }
        });
      }
      const currentObj = tabs.filter((tab) => tab.name === targetName);
      if (currentObj.length > 0) {
        this.deleteCurrentCache({ path: currentObj[0].name, fullPath: currentObj[0].path });
      }
      this.editableTabsValue = activeName;
      this.editableTabs = tabs.filter((tab) => tab.name !== targetName);
      const current = this.editableTabs.filter((item) => item.name === this.editableTabsValue);
      let toPath = this.home;
      if (current.length > 0) {
        toPath = current[0].path;
      }
      this.changeTab({
        $vnode: {
          key: toPath
        }
      });
      this.changeHistoryList(JSON.parse(JSON.stringify(this.editableTabs)));
    },
    /**
     * 删除组件的缓存
     * @param tabs
     * @param tabsNew
     */
    deleteFromCache(tabs, tabsNew) {
      const deleteList = tabs.filter((t) => tabsNew.some((n) => n.name !== t.name));
      const cacheList = cloneDeep(this.keepaliveList);
      const currentObj = deleteList.filter((tab) => cacheList.some((x) => x.value === tab.path));
      if (currentObj.length > 0) {
        currentObj.forEach((item) => {
          this.deleteCurrentCache({ path: item.name, fullPath: item.path });
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
<style lang="scss">
  .app-container .tagview .el-tabs--card > .el-tabs__header .el-tabs__item:nth-child(1) span {
    display: none;
  }

  .app-container .tagview .el-tabs__header.is-top {
    margin-bottom: 0;
  }

  .tagview {
    position: absolute;
    width: 100%;
    padding-top: 2px;
    left: 0;
    background-color: #f8f8f9;
    padding-left: 16px;
    z-index: 1;

    .el-tabs--card > .el-tabs__header .el-tabs__item {
      color: #808695;
      padding: 5px 16px;
      height: 34px;
      line-height: 24px;
      border-radius: 3px;
      border: none;
      margin: 5px 3px;
      background-color: #FFFFFF;
    }

    .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
      color: #2d8cf0 !important;
    }

    .el-tabs--card > .el-tabs__header .el-tabs__item:hover {
      color: #000000;
    }

    .el-tabs--card > .el-tabs__header {
      border-bottom: none;
    }

    .el-tabs--card > .el-tabs__header .el-tabs__nav {
      border: none;
    }
  }
</style>
