<template>
  <div>
    <!--  <div class="dybzx-page-content">-->
    <section class="jksqgl-page">
      <div class="jksqgl-page-header">
        <div class="page-header">
          <i class="el-icon-monitor"></i>
          <p class="jksqgl-page-header">企业微信</p>
        </div>
      </div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane name="first" label="系统基本信息">
          <div>
            <div class="dybzx-page-content">
              <el-dialog title="修改接入系统" :visible.sync="editFormVisible" :close-on-click-modal="false">
                <el-form :model="formedit" :rules="rulesedit" ref="formedit">
                  <el-form-item label="系统名称" :label-width="formLabelWidth" prop="XTMC">
                    <el-input size="small" v-model="formedit.XTMC" placeholder="必填"></el-input>
                  </el-form-item>
                  <el-form-item label="系统标识" :label-width="formLabelWidth" prop="XTBS">
                    <el-input size="small" v-model="formedit.XTBS" placeholder="必填"></el-input>
                  </el-form-item>
                  <el-form-item label="负责部门" :label-width="formLabelWidth" prop="BMMC">
                    <el-select v-model="formedit.BMMC" placeholder="请选择" >
                      <el-option v-for="item in editoptions" :key="item.BMM" :label="item.BMMC" :value="item.BMM"></el-option>
                    </el-select>
                  </el-form-item>
<!--                  <el-form-item label="负责部门" :label-width="formLabelWidth" prop="XTMC">
                    <el-input size="small" v-model="formedit.BMMC" placeholder="必填"></el-input>
                  </el-form-item>-->
                  <el-form-item label="联系人" :label-width="formLabelWidth" prop="XM">
                    <el-input size="small" v-model="formedit.XM"></el-input>
                  </el-form-item>
                  <el-form-item label="联系电话" :label-width="formLabelWidth" prop="DH">
                    <el-input size="small" v-model="formedit.DH"></el-input>
                  </el-form-item>
                  <el-form-item label="排序号" :label-width="formLabelWidth" prop="PXH">
                    <el-input size="small" v-model="formedit.PXH"></el-input>
                  </el-form-item>
                  <el-form-item label="备注" :label-width="formLabelWidth" prop="BZ">
                    <el-input size="small" v-model="formedit.BZ"></el-input>
                  </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                  <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
                  <el-button :loading="formLoading" size="small" type="primary" @click="editsubmitForm">确 定</el-button>
                </div>
              </el-dialog>
            </div>
            <!--编辑-->
            <jksqgl-jbxx @editForm="editForm" :arr="arr"></jksqgl-jbxx>
            <!--          <div style="display: flex;flex-wrap:wrap">
                        <div v-for="(item,index) in arr" :key="index" :style="`width:${item.isHalf ? '50%':'100%'}`">
                          <div>
                            <span class="jksqgl-page-content-jbxx-left" :style="`width:${item.isHalf ? '30%':'15%'}`">{{ item.label }}</span>
                            <span class="jksqgl-page-content-jbxx-right" :style="`width:${item.isHalf ? '70%':'35%'}`">{{ item.value }}</span>
                          </div>
                        </div>
                      </div>-->
            <div class="jksqgl-pageform-header">
              <span class="zhxy-form-label" slot="label">IP列表</span>
              <div class="button-list">
                <el-button type="primary" size="small" @click="showDialog">新增</el-button>
              </div>
            </div>
            <jksqgliplb :tableData="tableData"></jksqgliplb>
            <jksqgljklb :tablejklist="tablejklist"></jksqgljklb>
          </div>
        </el-tab-pane>
        <el-tab-pane name="second" label="代办事项获取">
          <dbsxhq :dyjk="dyjk"></dbsxhq>
        </el-tab-pane>
        <el-tab-pane name="third" label="代办事项获取"></el-tab-pane>
        <el-tab-pane name="fourth" label="代办批量信息">代办批量信息</el-tab-pane>
        <el-tab-pane name="fifth" label="发起事项获取">已办单人获取</el-tab-pane>
        <el-tab-pane name="sixth" label="已办事项获取">已办批量获取</el-tab-pane>
      </el-tabs>
    </section>
    <!--新增IP地址-->
    <el-dialog title="新增IP地址" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="IP地址" :label-width="formLabelWidth" prop="IP">
          <el-input size="small" v-model="form.IP" placeholder="必填"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
        <el-button :loading="formLoading" size="small" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import JksqglJbxx from './sqxtglComponents/JksqglJbxx';
import jksqgliplb from './sqxtglComponents/jksqgliplb';
import jksqgljklb from './sqxtglComponents/jksqgljklb';
import Dbsxhq from './sqxtglComponents/Dbsxhq';

export default {
  name: 'jksqgl',
  components: {
    JksqglJbxx,
    jksqgliplb,
    jksqgljklb,
    Dbsxhq
  },
  data() {
    const validatetitle = (rule, value, callback) => {
      if (value === '' || value === undefined || value == null) {
        callback();
      } else {
        const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if ((!reg.test(value)) && value !== '') {
          callback(new Error('请输入正确的IP地址'));
        } else {
          callback();
        }
      }
    };
    return {
      input: '',
      formLabelWidth: '120px',
      // label 宽度
      lableWidth: '120px',
      // 新增dialog loading
      formLoading: false,
      // dialog title
      dialogTitle: '修改接入系统',
      // 列表内容loading
      contentLoading: false,
      dialogTableVisible: false,
      editFormVisible: false,
      dialogFormVisible: false,
      form: {
        IP: ''
      },
      rules: {
        IP: [
          {
            required: true,
            message: '请输入正确的IP地址',
            trigger: 'blur',
            validator: validatetitle
          }
        ]
      },
      editoptions: [
        {
          BMM: '000001',
          BMMC: '航天学院'
        },
        {
          BMM: '000002',
          BMMC: '机电工程学院'
        },
        {
          BMM: '000003',
          BMMC: '材料科学与工程学院'
        },
        {
          BMM: '000004',
          BMMC: '能源科学与工程学院'
        },
        {
          BMM: '000005',
          BMMC: '电气工程及自动化学院'
        },
        {
          BMM: '000006',
          BMMC: '理学院'
        },
        {
          BMM: '000007',
          BMMC: '经济与管理学院'
        }
      ],
      formedit: {
        XTMC: '',
        XTBS: '',
        MBMC: '',
        BMMC: '',
        DH: '',
        XM: '',
        PXH: '',
        bz: ''
      },
      rulesedit: {
        XTMC: [
          {
            required: true,
            message: '请输入正确的系统名称',
            trigger: 'blur'
          }
        ],
        XTBS: [
          {
            required: true,
            message: '请输入正确的系统标识',
            trigger: 'blur'
          }
        ],
        BMMC: [
          {
            required: true,
            message: '请输入正确的部门名称',
            trigger: 'blur'
          }
        ]
      },
      arr: [
        {
          id: 'xtbs',
          label: '系统标识',
          value: '',
          isHalf: true
        },
        {
          id: 'fzbm',
          label: '负责部门',
          value: '',
          isHalf: true
        },
        {
          id: 'lxr',
          label: '联系人',
          value: '',
          isHalf: true
        },
        {
          id: 'lxdh',
          label: '联系电话',
          value: '',
          isHalf: true
        },
        {
          id: 'pxh',
          label: '排序号',
          value: '',
          isHalf: true
        }, {
          id: 'bz',
          label: '备注',
          value: '',
          isHalf: true
        }
      ],
      obj: {
        xtbs: 'QYWX_',
        fzbm: '网络服务中心',
        lxr: '卢波',
        lxdh: '12254687254',
        pxh: '1',
        bz: '1111',
        xtmc: '企业微信'
      },
      dyjk: [
        {
          id: 1,
          time: '近一个月调用接口',
          sumnumber: '582',
          succnumber: '582',
          fallnumber: '0',
          xytime: '200ms'
        },
        {
          id: 2,
          time: '近一周调用接口',
          sumnumber: '171',
          succnumber: '171',
          fallnumber: '0',
          xytime: '200ms'
        },
        {
          id: 3,
          time: '近一天调用接口',
          sumnumber: '37',
          succnumber: '37',
          fallnumber: '0',
          xytime: '200ms'
        }
      ],
      activeName: 'first',
      tablemessage: [{
        XTMC: '企业微信',
        xtbs: 'QYWX_@DY20191014',
        fzbm: '网络与信息中心',
        lxr: '卢波',
        lxdh: '12222222222',
        pxh: '1',
        bz: ''
      }],
      tableData: [
        {
          address: '1992.168.68.33',
          zt: '启用',
          cjsj: '2021-5-12 11.24.12'
        }, {
          address: '1992.168.68.33',
          zt: '启用',
          cjsj: '2021-5-12 11.24.12'
        },
        {
          address: '1992.168.68.33',
          zt: '启用',
          cjsj: '2021-5-12 11.24.12'
        }, {
          address: '1992.168.68.33',
          zt: '启用',
          cjsj: '2021-5-12 11.24.12'
        }, {
          address: '1992.168.68.33',
          zt: '启用',
          cjsj: '2021-5-12 11.24.12'
        }],
      tablejklist: [
        {
          tyqx: '允许',
          cjsj: '2019-10-14 19.12.11'
        },
        {
          tyqx: '允许',
          cjsj: '2019-10-14 19.12.11'
        },
        {
          tyqx: '允许',
          cjsj: '2019-10-14 19.12.11'
        },
        {
          tyqx: '允许',
          cjsj: '2019-10-14 19.12.11'
        }
      ]
    };
  },
  mounted() {
    console.log(this.arr);
    this.changeObj();
    console.log(this.arr);
  },
  methods: {
    // edit数据改变 将obj的数据与arr数组中value进行绑定
    changeObj() {
      this.arr.forEach((item) => {
        item.value = this.obj[item.id];
      });
    },
    // 编辑信息调用
    editForm() {
      this.editFormVisible = true;
      this.formedit.XTMC = this.obj.xtmc;
      this.formedit.XTBS = this.obj.xtbs;
      this.formedit.BMMC = this.obj.fzbm;
      this.formedit.XM = this.obj.lxr;
      this.formedit.DH = this.obj.lxdh;
      this.formedit.PXH = this.obj.pxh;
      this.formedit.BZ = this.obj.bz;
      this.$nextTick(() => {
        this.$refs.formedit.resetFields();
        this.editFormVisible = true;
      });
      console.log('1111');
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    /**
     * 新增IP
     */
    showDialog() {
      this.status = 0;
      this.dialogTitle = '修改接入系统';
      // 展示弹窗
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = this.form;
          console.log(params);
          this.formLoading = true;
          if (!this.status) {
            // 添加事件接口
            setTimeout(() => {
              // 模拟接口成功事件
              this.$message.success('添加成功');
              this.formLoading = false;
              this.dialogFormVisible = false;
            }, 2000);
          }
          // 调用接口 传递数据
          return true;
        }
        return false;
      });
    },
    edit() {
      this.status = 0;
      this.dialogTitle = '修改接入系统';
      // 展示弹窗
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs.formedit.resetFields();
      });
    },
    editsubmitForm() {
      this.$refs.formedit.validate((valid) => {
        if (valid) {
          const params = this.formedit;
          console.log(params);
          this.formLoading = true;
          if (!this.status) {
            // 添加事件接口
            setTimeout(() => {
              // 模拟接口成功事件
              this.$message.success('编辑成功');
              this.formLoading = false;
              this.dialogFormVisible = false;
            }, 2000);
          }
          // 调用接口 传递数据
          return true;
        }
        return false;
      });
    },
    handleEdit(index, row) {
      this.editFormVisible = true;
      // dialog对话窗口打开
      this.editForm = { ...row };
      // 将数据传入dialog页面
      this.editForm.index = index;
      // 传递当前index
    },
    // editanniu(params) {
    //   this.status = 1;
    //   this.dialogTitle = '待已办编辑';
    //   this.dialogFormVisible = true;
    //   this.$nextTick(() => {
    //     this.$refs.form.resetFields();
    //     Object.assign(this.form, params);
    //   });
    //
    //   console.log(params);
    // },
    // edit() {
    //   const params = {};
    //   console.log(params);
    // },
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
};
</script>

<style lang="scss" scoped>
.jksqgl-pageform-header {
  height: 50px;
  padding: 0 20px;
}

.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    box-sizing: border-box;
  }
}

.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;
}

.jksqgl-pageform-header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  background-color: #EAEAEA;
  border: 1px solid #ededed;
  width: 100%;
  height: 50px;
  margin-top: 10px;
  padding: 0 10px;
  box-sizing: border-box;
}

.jksqgl-page-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  background: #FFFFFF;
  padding-left: 10px;
  color: #3a8ee6;

  .page-header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    line-height: 50px;
    font-size: 20px;
    font-family: inherit;

    .el-icon-s-platform {
      width: 50px;
      height: 50px;
      display: block;
      line-height: 50px;

      /*.jksqgl-page-header {
        color: #000000;
        margin: 0;
        padding: 0;
        padding-left: 10px;
      }*/
    }
  }
}

.jbxx-page-content-top {
  width: 33.33%;
  display: flex;
  justify-content: space-between;
  padding-left: 100px;
  background-color: #FFFFFF;
}

.tjxx-page-content-first {
  width: 100%;
  height: 350px;
  padding-top: 30px;
  padding-left: 15px;

  .tjxx-page-content-first-title {
    background-color: #ededed;
    width: 400px;
    height: 50px;
    color: #3a8ee6;
    line-height: 50px;
    padding: 0px 50px;
    box-sizing: border-box;
  }

  .tjxx-page-content-first-mid {
    display: flex;
    justify-content: space-between;
    width: 400px;
    height: 200px;
    border: #ededed 1px solid;
    border-bottom: none;
    padding: 50px 50px 0px;
    text-align: center;
    box-sizing: border-box;

    .tjxx-page-content-first-mid-left {
      .tjxx-page-content-first-number {
        color: #2d8cf0;
      }

      .tjxx-page-content-first- written {
      }
    }

    .tjxx-page-content-first-mid-centre {
      .tjxx-page-content-first-number-centre {
        color: darkorange;
      }
    }

    .tjxx-page-content-first-mid-right {
      .tjxx-page-content-first-number-right {
        color: red;
      }
    }
  }

  .tjxx-page-content-first-last {
    background-color: #FFFFFF;
    width: 400px;
    height: 40px;
    display: flex;
    justify-content: center;
    line-height: 40px;
    border: #ededed 1px solid;
    border-top: none;
    box-sizing: border-box;

    .tjxx-page-content-first-last-title {
      color: #515a6e;
    }

    .tjxx-page-content-first-last-number {
      color: green;
    }
  }
}
</style>
