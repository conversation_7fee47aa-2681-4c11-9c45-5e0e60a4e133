<template>
  <div ref="myChart" style="width:100%; height:100%"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/bar');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  props: {
    list: {
      type: Object
    },
    msg: {
      type: String
    },
    title: {
      type: String
    }
  },
  data() {
    return {
      myChart: null,
      myChart64: null
    };
  },
  watch: {
    list(val) {
      this.drawLine();
    },
    title(val) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      const { list, title } = this;
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(this.$refs.myChart);

      const dataAxis = ['c1', 'c2', 'c3', 'c4', 'c5', 'c6', 'c7', 'c8', 'c9', 'c10'];
      const data = [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149, 210, 122, 133, 334, 198, 123, 125, 220];
      const yMax = 500;
      const dataShadow = [];

      for (let i = 0; i < data.length; i++) {
        dataShadow.push(yMax);
      }

      const option = {
        title: {
          text: '属性填充率'
        },
        xAxis: {
          data: dataAxis,
          axisLabel: {
            // inside: true,// 显示在内部
            textStyle: {
              // color: '#fff'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          z: 10
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#999'
            }
          }
        },
        dataZoom: [{
          type: 'inside'
        }],
        series: [{ // For shadow
          type: 'bar',
          itemStyle: {
            color: 'rgba(0,0,0,0.05)'
          },
          barWidth: 20,
          barGap: '-100%',
          barCategoryGap: '40%',
          data: dataShadow,
          animation: false
        },
        {
          type: 'bar',
          barWidth: 20,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [{
                offset: 0,
                color: '#83bff6'
              },
              {
                offset: 0.5,
                color: '#188df0'
              },
              {
                offset: 1,
                color: '#188df0'
              }
              ]
            )
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [{
                  offset: 0,
                  color: '#2378f7'
                },
                {
                  offset: 0.7,
                  color: '#2378f7'
                },
                {
                  offset: 1,
                  color: '#83bff6'
                }
                ]
              )
            }
          },
          data
        }
        ]
      };
      this.myChart.setOption(option);
      setTimeout(() => {
        this.$emit('sendMychart', this.msg);
        // alert(1)
      }, 3000);
    },
    // 生成64位图片
    get64Bata() {
      if (typeof (this.myChart) === 'undefined') {
        this.myChart64 = '';
      } else {
        this.myChart64 = this.myChart.getDataURL({ type: 'png' }).split(',')[1];
      }
      return this.myChart64;
    }
  }

};
</script>

<style>
</style>
