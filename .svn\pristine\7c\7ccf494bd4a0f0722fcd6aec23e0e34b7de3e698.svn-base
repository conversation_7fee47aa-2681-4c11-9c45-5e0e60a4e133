<template>
  <div class="dlrz-content">
    <div class="dlrz-content-main">
      <!--      标题-->
      <v-title name="登录日志"></v-title>
      <dlrz-search ref="searchElement" @search="search"></dlrz-search>
      <!--      table 表单部分 -->
      <!--      table-->
      <div class="table">
        <div class="table-content">
          <dlrz-table :table-data="tableData"></dlrz-table>
        </div>
        <!--        table pageInation-->
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import {
  findListByPage
} from '@/app/xtgl_03_01/api/dlrz/dlrz.js';
import DlrzSearch from './dlrzComponents/InfoSearch';
import DlrzTable from './dlrzComponents/InfoTable';

const defaultYh = {
  yhid: '',
  xm: '',
  llqxx: '',
  fwzdlx: '',
  IP: '',
  czsj: '',
  dllx: '',
  sfms: '',
  cjr: '',
  cjsj: '',
  bgr: '',
  bgsj: ''
};

export default {
  name: 'dlrz',
  components: {
    DlrzSearch,
    DlrzTable,
    VTitle
  },
  data() {
    return {
      yh: { ...defaultYh },
      currentPage: 1, // 初始页
      tableData: [], // 列表数据集合
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      addDialogVisible: false,
      yhidVisible: true,
      dialogType: 'new'
    };
  },
  mounted() {
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /**
       * search 搜索事件
       * @param params
       * @returns {Promise<void>}
       */
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        yhid: null,
        xm: null,
        sfms: null,
        dllx: null,
        kssj: null,
        cjsj: null
      };
      if (params) {
        Object.assign(param, params);
      }
      console.log(params);
      findListByPage(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    /**
       * 查询条件重置
       */
    reset() {
      this.listQuery.idxm = null;
      this.listQuery.sfzj = null;
      this.listQuery.yhzt = null;
      this.search();
    },
    /**
       * 每页显示条数改变事件
       * @param val
       */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        wymbs: this.$refs.searchElement.listQuery.wymbs,
        yhid: this.$refs.searchElement.listQuery.yhid,
        xm: this.$refs.searchElement.listQuery.xm,
        sfms: this.$refs.searchElement.listQuery.sfms,
        dllx: this.$refs.searchElement.listQuery.dllx,
        kssj: this.$refs.searchElement.listQuery.kssj,
        jssj: this.$refs.searchElement.listQuery.jssj
      };
      this.search(param);
    },
    /**
       * 当前页数改变事件
       * @param val
       */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage, // 页码
        wymbs: this.$refs.searchElement.listQuery.wymbs,
        yhid: this.$refs.searchElement.listQuery.yhid,
        xm: this.$refs.searchElement.listQuery.xm,
        sfms: this.$refs.searchElement.listQuery.sfms,
        dllx: this.$refs.searchElement.listQuery.dllx,
        kssj: this.$refs.searchElement.listQuery.kssj,
        jssj: this.$refs.searchElement.listQuery.jssj
      };
      this.search(param);
    }
  }
};
</script>
<style lang="scss" scoped>
  .dlrz-content {
    &-main {
      height: 100%;
      background-color: #ffffff;
      padding: $page-content-padding;
      display: flex;
      flex-direction: column;
    }

    .table {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      &-content {
        flex: 1;
        overflow: auto;
        margin-top: $page-content-margin;
      }

    }

    .dialog-footer {
      text-align: center;
    }
  }
</style>
