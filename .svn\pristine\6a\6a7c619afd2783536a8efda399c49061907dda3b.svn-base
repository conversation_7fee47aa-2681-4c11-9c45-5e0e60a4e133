<template>
  <div style="flex: 1;overflow: auto">
    <el-table
      :data="tableData"
      row-key="dmid"
      stripe
      border
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      class="zhxy-table"
      @selection-change="selectionChange"
      height="100%">
      <el-table-column align="center" type="selection" @selection-change="selectionChange" label="" width="52"></el-table-column>
      <el-table-column prop="dmid" width="130" v-if=false></el-table-column>
      <el-table-column prop="sjdmid" v-if=false></el-table-column>
      <el-table-column prop="dmmc" label="代码名称" width="150"></el-table-column>
      <el-table-column prop="dmz" label="代码值" width="150" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="pxh" label="排序号" width="80"></el-table-column>
      <el-table-column prop="kzzd1" label="扩展字段1" width="100"></el-table-column>
      <el-table-column prop="kzzd2" label="扩展字段2" width="100"></el-table-column>
      <el-table-column prop="kzzd3" label="扩展字段3" width="100"></el-table-column>
      <el-table-column prop="sfky" label="是否可用" width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.sfky === 0">否</span>
          <span v-else-if="scope.row.sfky === 1">是</span>
        </template>
      </el-table-column>
      <el-table-column prop="cjr" label="创建人" width="100"></el-table-column>
      <el-table-column prop="cjsj" label="创建时间" width="" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{scope.row.cjsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column prop="bgr" label="变更人" width="100"></el-table-column>
      <el-table-column prop="bgsj" label="变更时间" width="" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{scope.row.bgsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="150">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="addxjdm(scope.row)">新增下级</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" type="text" @click="updateDmx(scope.row)">修改</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" type="text" @click="delDmx(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
<!--    <div class="pageIn">-->
<!--      <el-pagination-->
<!--        size-change="handleSizeChange"-->
<!--        @current-change="handleCurrentChange"-->
<!--        :page-sizes="[30, 50, 100, 200,500]"-->
<!--        layout="total, sizes, prev, pager, next, jumper"-->
<!--        :total=total>-->
<!--      </el-pagination>-->
<!--    </div>-->
  </div>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';
import {
  findListByPage, addGnzymx, updateGnzymx, delZymx, delPlZymx, add
} from '../../../api/zygl/zygl';
import { delPlJsyh } from '../../../api/jsgl/jsgl';

export default {
  name: 'DepartmentUser',
  filters: {
    dateFilter
  },
  data() {
    return {
      // table pageSize page
      pageSize: 30,
      page: 1,
      // 代码分类标识
      dmflbs: '',
      // table数据
      tableData: [],
      // 总数据数
      total: 0,
      selections: ''
    };
  },
  methods: {
    // 勾选资源明细
    selectionChange(val) {
      console.log(val);
      this.$emit('handleSelectionChange', val);
    },
    action(params) {
    },
    // 修改每页数据量
    handleSizeChange(val) {
      this.pageSize = val;
      this.search();
    },
    // 修改页数
    handleCurrentChange(val) {
      this.page = val;
      this.search();
    },
    updateDmx(val) {
      this.$emit('updateDmx', val);
    },
    /**
     * 删除 资源明细 接口
     */
    delDmx(data) {
      this.$emit('delDmx', data);
    },
    // 新增下级代码项
    addxjdm(val) {
      this.$emit('addxjdm', val);
    },
    /**
     * 删除 资源明细 接口
     */
    deleteZymxCurrent(data, instance, done) {
      const param = {
        id: data.id
      };
      delZymx(param).then((res) => {
        this.getGmzymx();
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    // 批量删除资源明细
    deleteJsyh() {
      if (this.selections === '') {
        this.$message.success('请勾选角色用户');
      } else {
        const arr = [];
        this.selections.forEach((item) => {
          const itemparam = {
            id: item.id
          };
          arr.push(itemparam);
        });
        const param = {
          idarr: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },
    /**
     * 批量删除 资源明细 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      delPlZymx(param).then((res) => {
        this.getGmzymx();
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
  .button-tab {
    margin-bottom: $page-content-padding;
  }

  .department-user {
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }

    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
