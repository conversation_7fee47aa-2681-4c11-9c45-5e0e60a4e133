const router = [
  {
    name: '系统参数',
    path: '/xtgl_02_01/jcggXtcs',
    component: () => import('../pages/xtcs/Xtcs'),
    meta: { name: '系统参数' }
  },
  {
    name: '资源管理',
    path: '/xtgl_02_01/jcqxGnzy',
    component: () => import('../pages/zygl/Zygl'),
    meta: { name: '资源管理' }
  },
  {
    name: '部门管理',
    path: '/xtgl_02_01/jcqxBm',
    component: () => import('../pages/bmgl/Bmgl'),
    meta: { name: '部门管理' }
  },
  {
    name: '角色管理',
    path: '/xtgl_02_01/jcqxJs',
    component: () => import('../pages/jsgl/Jsgl'),
    meta: { name: '角色管理' }
  },
  {
    name: '用户管理',
    path: '/xtgl_02_01/jcqxYh',
    component: () => import('../pages/yhgl/Yhgl'),
    meta: { name: '用户管理' }
  },
  {
    name: '菜单管理',
    path: '/xtgl_02_01/tygzt3Tycdpz',
    component: () => import('../pages/cdgl/Cdgl'),
    meta: { name: '菜单管理' }
  },
  {
    name: '操作日志',
    path: '/xtgl_02_01/jcggCzrz',
    component: () => import('../pages/czrz/Czrz'),
    meta: { name: '操作日志' }
  },
  {
    name: '登录日志',
    path: '/xtgl_02_01/jcggDlrz',
    component: () => import('../pages/dlrz/Dlrz'),
    meta: { name: '登录日志' }
  },
  {
    name: '代码表',
    path: '/xtgl_02_01/jcggDmfl',
    component: () => import('../pages/dmb/Dmb'),
    meta: { name: '代码表' }
  }
];

export default router;
