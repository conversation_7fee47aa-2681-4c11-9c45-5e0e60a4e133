<template>
  <el-dialog
    :width="width"
    :title="title"
    :visible.sync="visible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    custom-class="choose-user-dialog"
    append-to-body>
    <div class="origin-user-part" v-if="visible">
      <div class="origin-user-part-left">
        <div class="content-top">
          <div class="zhxy-form zhxy-form-search-part" style="align-items: center;margin: 0">
            <el-form label-width="120" inline :model="departmentSearch" ref="searchForm">
              <el-form-item style="align-items: center">
                <el-input
                  clearable
                  class="zhxy-form-inline" v-model="departmentSearch.name"
                  style="width: 548px;margin: 0"
                  placeholder="请输入用户姓名或ID进行快速查找"
                  size="small">
                  <el-button @click="searchInfo" slot="append">查询</el-button>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="content-main">
          <div class="content-main-left zhxy-tree-nomal">
            <el-tree
              v-loading="treeLoading"
              @node-click="nodeClick"
              :props="defaultProp"
              highlight-current
              :node-key="defaultProp.id"
              :data="treeData"
              ref="tree">
               <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{ data[defaultProp.label] }}
              </span>
            </el-tree>
          </div>
          <div class="content-main-right" style="position: relative">
            <div v-if="single" class="select-hidden"></div>
            <el-table
              v-loading="treeDetailLoading"
              :data="treeDetail"
              stripe
              border
              class="zhxy-table"
              height="calc(100% - 26px)"
              header-row-class-name="origin-table-header-middle"
              :row-key="defaultProps.id"
              ref="treeTable"
              @select="selectSingle"
              @selection-change="handleSelectionChange">
              <el-table-column
                reserve-selection
                align="center"
                type="selection"
                label=""
                width="52">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                :prop="defaultProps.label"
                label="可选用户/岗位">
              </el-table-column>
              <el-table-column
                width="92"
                v-if="isPerson"
                show-overflow-tooltip
                :prop="defaultProps.id"
                label="ID">
              </el-table-column>
            </el-table>
            <el-pagination
              style="text-align: center"
              small
              layout="prev, pager, next"
              @current-change="handleCurrentChange"
              :page="page"
              :page-size="pageSize"
              :total="total">
            </el-pagination>
          </div>
        </div>

      </div>
      <div class="origin-user-part-right">
        <el-table
          :data="currentContent"
          stripe
          border
          class="zhxy-table"
          height="calc(100% - 26px)"
          header-row-class-name="origin-table-header">
          <el-table-column :prop="defaultProps.label" label="已选用户/岗位"></el-table-column>
          <el-table-column align="center" prop="" label="" width="60">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-delete"
                circle size="small"
                @click="deletItem(scope.row)">
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="text-align: center"
          small
          layout="prev, pager, next"
          @current-change="pageChangeSelf"
          :page="pageSelf"
          :page-size="pageSizeSelf"
          :total="multipleSelection.length"/>
      </div>
    </div>
    <div slot="footer">
      <el-button size="small" type="primary" @click="buttonCertain">确 定</el-button>
      <el-button size="small" @click="buttonCancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { cloneDeep } from 'lodash';

export default {
  name: 'ChooseUserDialog',
  props: {
    width: {
      type: String,
      default: '900px'
    },
    // 标题名称
    title: {
      type: String,
      default: '选择用户'
    },
    // 中间table props定义值
    defaultProps: {
      type: Object,
      default: () => ({
        label: 'jsmc',
        id: 'jsid',
        children: 'children'
      })
    },
    // 左侧部门列表props定义值
    defaultProp: {
      type: Object,
      default: () => ({
        label: 'bmmc',
        id: 'bmm', // row-key
        children: 'children'
      })
    },
    // 左侧tree data
    treeData: {
      type: Array,
      default: () => []
    },
    // 是否单选
    single: {
      type: Boolean,
      default: false
    },
    // 中间table 数据
    treeDetail: {
      type: Array,
      default: () => ([])
    },
    // 中间 table分页page
    page: {
      type: Number,
      default: 1
    },
    // 中间 table分页pageSize
    pageSize: {
      type: Number,
      default: 50
    },
    // 中间 table分页page total
    total: {
      type: Number,
      default: 0
    },
    // 中间 table 加载loading
    treeDetailLoading: {
      type: Boolean,
      default: false
    },
    // 左侧tree 加载loading
    treeLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 当前点击节点 node
      currentNode: {},
      // dialog 显隐
      visible: false,
      // 部门用户search
      departmentSearch: {
        name: ''
      },
      // 中间 table 选中的数据
      multipleSelection: [],
      // 单选状态下暂存selected
      selected: [],
      // 右侧table 分页page
      pageSelf: 1,
      // 右侧table 分页pageSize
      pageSizeSelf: 50
    };
  },
  computed: {
    // 自定义选定内容分页
    currentContent() {
      // eslint-disable-next-line array-callback-return,consistent-return
      return this.multipleSelection.filter((x, index) => {
        if (index < this.pageSelf * this.pageSizeSelf && index >= (this.pageSelf - 1) * this.pageSizeSelf) {
          return x;
        }
      });
    },
    isPerson() {
      return this.title.indexOf('人员') > -1;
    }
  },
  methods: {
    /**
     * 右侧table pagechange
     * @param val
     */
    pageChangeSelf(val) {
      this.pageSelf = val;
    },
    /**
     * 中间table checkbox点击(只在single状态下使用)
     * @param rows
     */
    selectSingle(rows) {
      if (!this.single) {
        return;
      }
      if (rows.length > 1) {
        const newRows = rows.filter((it, index) => {
          if (index === rows.length - 1) {
            this.$refs.treeTable.toggleRowSelection(it, true);
            return true;
          }
          this.$refs.treeTable.toggleRowSelection(it, false);
          return false;
        });
        this.multipleSelection = newRows;
      } else {
        this.multipleSelection = rows;
      }
      this.selected = cloneDeep(this.multipleSelection);
    },
    /**
     * dialog show
     */
    showDialog() {
      this.visible = true;
      this.currentNode = {};
      this.departmentSearch.name = '';
      this.pageSelf = 1;
    },
    /**
     * dialog hidden
     */
    hideDialog() {
      this.visible = false;
    },
    /**
     * 中间table checkbox 切换
     * @param val
     */
    handleSelectionChange(val) {
      if (this.single) {
        this.selectSingle(val);
        this.selected = cloneDeep(this.multipleSelection);
      } else {
        this.multipleSelection = val;
      }
      // this.multipleSelection = val;
    },
    /**
     * 右侧table delete
     * @param row
     */
    deletItem(row) {
      const arr = cloneDeep(this.multipleSelection);
      const id = this.defaultProps.id;
      const i = this.multipleSelection.findIndex((x) => x[id] === row[id]);

      arr.splice(i, 1);
      const list = this.multipleSelection.filter((x) => arr.some((y) => y[id] === x[id]));
      this.toggleSelection();
      this.toggleSelection(list, false);
    },
    /**
     * 中间table pageChange
     * @param val
     */
    handleCurrentChange(val) {
      const params = {
        page: val || 1,
        currentNode: this.currentNode,
        info: this.departmentSearch.name
      };
      this.$emit('pageChange', params);
    },
    /**
     * 确定
     */
    buttonCertain() {
      this.$emit('certain', this.multipleSelection);
    },
    /**
     * 取消
     */
    buttonCancel() {
      this.$emit('cancel');
    },
    /**
     * 左侧tree 点击
     * @param node
     */
    nodeClick(node) {
      this.currentNode = node;
      this.$emit('nodeClick', node);
    },
    /**
     * 清空 multipleSelection 初始化
     */
    clearNode() {
      this.multipleSelection = [];
      // this.$set(this.multipleSelection,[])
    },
    /**
     * 中间 table默认选中
     * @param rows
     */
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.treeTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.treeTable.clearSelection();
      }
    },
    /**
     * 查询功能
     */
    searchInfo() {
      const val = {
        node: this.currentNode,
        info: this.departmentSearch.name
      };
      this.$emit('searchInfo', val);
    }
  }
};
</script>

<style lang="scss">
.choose-user-dialog {
  .el-dialog__body {
    padding: 10px 10px 0;
  }

  .el-dialog__footer {
    padding: 10px;
  }

  tr.origin-table-header-middle {
    th {
      background-color: #ffffff !important;
      padding: 5px;
    }
  }

  tr.origin-table-header {
    th {
      padding: 4px;
    }
  }
}
</style>
<style lang="scss" scoped>
.choose-user-dialog {
  .select-hidden {
    position: absolute;
    z-index: 10000;
    background: white;
    width: 50px;
    height: 30px;
    top: 2px;
    left: 2px
  }

  .origin-user-part {
    height: 500px;
    display: flex;
    justify-content: space-between;
    overflow: auto;

    &-left {
      display: flex;
      flex-direction: column
    }

    &-right {
      width: 320px;
      overflow: auto;
    }
  }

  .content-main {
    display: flex;
    flex: 1;
    overflow: auto;

    &-left {
      margin-right: 10px;
      width: 265px;
      flex-shrink: 0;
      height: 100%;
      overflow: auto
    }

    &-right {
      width: 275px;
    }
  }
}
</style>
