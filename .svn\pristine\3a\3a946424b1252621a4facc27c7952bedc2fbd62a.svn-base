<template>
  <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
    <el-table-column prop="xh" label="序号" width="80"></el-table-column>

<!--    <el-table-column prop="" label="任务类别" width="100">-->
<!--      <template slot-scope="scope">-->
<!--        <span v-if="scope.row.rwlb === 0">平台类</span>-->
<!--        <span v-else-if="scope.row.rwlb === 1 ">数据类</span>-->
<!--      </template>-->
<!--    </el-table-column>-->

    <el-table-column prop="cxmc" label="程序名称" width="200"></el-table-column>
    <el-table-column prop="cxbs" label="程序标识" ></el-table-column>
    <el-table-column prop="bgzcjgsj" label="报告最长间隔时间（秒）" width="180"></el-table-column>
<!--    <el-table-column prop="zxjgsj" label="执行间隔时间（秒）" width="200"></el-table-column>-->

    <el-table-column prop="scbgsj" label="上次报告时间" width="180">
      <template slot-scope="scope">
        <div v-if="scope.row.scbgsj != '' && scope.row.scbgsj != null">
          {{dayjs(scope.row.scbgsj).format('YYYY-MM-DD HH:mm:ss')}}
        </div>
      </template>
    </el-table-column>

<!--    <el-table-column prop="yxip" label="运行服务器ip" width="200"></el-table-column>
    <el-table-column prop="dlyhm" label="登录用户名" width="200"></el-table-column>
    <el-table-column prop="dlmm" label="登录密码" width="200"></el-table-column>
    <el-table-column prop="qdml" label="启动命令" width="200"></el-table-column>
    <el-table-column prop="gbml" label="关闭命令" width="200"></el-table-column>-->
    <el-table-column prop="" label="状态" width="80">正常</el-table-column>

    <el-table-column fixed="right" prop="" label="操作" width="120">
      <template slot-scope="scope">
        <el-button size="small" @click="updateCjrwgz(scope.row)" type="text">修改</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" type="text" @click="delCjrwgz(scope.row)">删除</el-button>
      </template>
    </el-table-column>

  </el-table>
</template>

<script>

import dayjs from 'dayjs';

export default {
  name: 'CjrwgzTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dayjs
    };
  },
  methods: {
    updateCjrwgz(val) {
      this.$emit('updateCjrwgz', val);
    },
    delCjrwgz(val) {
      this.$emit('delCjrwgz', val);
    }
  }
};
</script>
