<template>
  <div :style="`height: ${scrollerHeight}`">
    <el-table
      :data="tableData"
      stripe
      border
      class="zhxy-table"
      height="100%"
      @selection-change="selectionChange">
<!--      <el-table-column
        align="center"
        type="selection"
        label=""
        width="52">
      </el-table-column>-->
      <el-table-column align="center" type="index" label="序号" width="52"></el-table-column>
      <el-table-column prop="xm" label="姓名" width="100"></el-table-column>
      <el-table-column prop="zhid" label="用户ID" width="100"></el-table-column>
      <el-table-column prop="sjh" label="手机号" width=""></el-table-column>
      <el-table-column prop="yx" label="电子邮箱" width=""></el-table-column>
      <el-table-column prop="zhzt" label="状态" width="">
        <template slot-scope="scope">
          <span>{{scope.row.zhzt | getZhzt}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="80">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="delCurrent(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import {
  addJsyh, del, delJsyh, findJsxyhlist, delPlJsyh
} from '@/app/xtgl_03_01/api/jsgl/jsgl.js';

export default {
  filters: {
    getZhzt(val) {
      if (!val) {
        return '';
      }
      const list = {
        1: '启用',
        2: '停用',
        3: '锁定'
      };
      return list[val];
    }
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      jsid: '',
      // table page
      page: 1,
      pageSize: 30,
      // 用户查询条件
      cxtj: '',
      userdata: {
        name: ''
      },
      total: 0,
      selections: ''
    };
  },
  methods: {
    // 当前页面勾选的数据
    selectionChange(selection) {
      this.selections = selection;
    },
    // 批量删除角色下用户
    deleteJsyh() {
      if (this.selections === '') {
        this.$message.success('请勾选角色用户');
      } else {
        const arr = [];
        this.selections.forEach((item) => {
          const itemparam = {
            jsid: this.jsid,
            yhid: item.yhid
          };
          arr.push(itemparam);
        });
        /**
         * 批量删除 角色下用户 接口
         */
        const param = {
          yharr: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },

    /**
     * 批量删除 角色下用户 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      delPlJsyh(param).then((res) => {
        this.clickUserJsyh(this.userdata);
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    /**
     * 删除 群组下用户按钮提示
     */
    delCurrent(data) {
      this.$emit('delCurrent', data);
    },
    /**
     * 删除 群组下用户 接口
     */
    deleteCurrentApi(data, instance, done) {
      const param = {
        jsid: this.jsid,
        yhid: data.yhid
      };
      delJsyh(param).then((res) => {
        this.clickUserJsyh(this.userdata);
        this.$message.success('取消授权成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    /**
     * 获取 角色下用户 接口
     */
    clickUserJsyh(data) {
      this.cxtj = data.name;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: data.name
      };
      /**
       * 获取 角色用户 接口
       */
      findJsxyhlist(param).then((res) => {
        this.tableData = res.data.content;
        this.total = res.data.pageInfo.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    /**
     * 获取 角色下用户 接口(分页)
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: this.cxtj
      };
      /**
       * 获取 角色用户 接口
       */
      // findJsxyhlist(param).then((res) => {
      //   this.userTableData = res.data.content;
      // }).finally(() => {
      //   this.loading = false;
      // });
    },
    /**
     * currentPage change event
     * @param val
     */
    handleCurrentChange(val) {
      this.page = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: this.cxtj
      };
      /**
       * 获取 角色用户 接口
       */
      // findJsxyhlist(param).then((res) => {
      //   this.userTableData = res.data.content;
      // }).finally(() => {
      //   this.loading = false;
      // });
    },
    /**
     * 获取 角色用户 接口
     */
    resetUserJsyh() {
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: ''
      };
      /**
       * 获取 角色用户 接口
       */
      // findJsxyhlist(param).then((res) => {
      //   this.userTableData = res.data.content;
      // }).finally(() => {
      //   this.loading = false;
      // });
    }
  }
};
</script>
<style lang="scss" scoped>
  .span-active{
    color: #3a8ee6;
  }
  .button-tab {
    margin-bottom: $page-content-padding;
  }
  .department-user {
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }
    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
