import { createAPI } from '@/utils/request';

const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/xtjkyyj_01_01/yjpzgl`;
// 查询预警配置列表
export const findYjpzList = (data) => createAPI(`${APP_PRE}/findYjpzList`, 'get', data);
// 删除预警配置
export const delYjpz = (data) => createAPI(`${APP_PRE}/delete`, 'post', data);
/* 查询单条预警配置 */
export const findOneByYjbs = (data) => createAPI(`${APP_PRE }/findOne`, 'get', data);
// 新增预警配置
export const addYjpz = (data) => createAPI(`${APP_PRE}/add`, 'post', data);
// 更新预警配置
export const updateYjpz = (data) => createAPI(`${APP_PRE}/update`, 'post', data);
// 查询提醒人员列表
export const findTxryList = (data) => createAPI(`${APP_PRE}/findTxryList`, 'get', data);
// 查询待选提醒人员列表
export const findDxtxryList = (data) => createAPI(`${APP_PRE}/findDxtxryList`, 'get', data);
// 新增提醒人员信息
export const addTxryxx = (data) => createAPI(`${APP_PRE}/addTxryxx`, 'post', data);
// 删除提醒人员信息
export const deleteTxryxx = (data) => createAPI(`${APP_PRE}/deleteTxryxx`, 'post', data);
// 查询预警类别列表
export const findYjlb = (data) => createAPI(`${APP_PRE}/findYjlb`, 'get', data);
// 查询预警分类列表
export const findYjfl = (data) => createAPI(`${APP_PRE}/findYjfl`, 'get', data);
// 查询预警级别列表
export const findYjjb = (data) => createAPI(`${APP_PRE}/findYjjb`, 'get', data);
// 修改预警开关
export const changeKg = (data) => createAPI(`${APP_PRE}/changeKg`, 'post', data);
