import Vue from 'vue';
import Vuex from 'vuex';

import state from './state';
import mutations from './mutations';
import actions from './actions';

Vue.use(Vuex);

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('@/app/', true, /(.*?)\/store\/index\.js$/);

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((module, modulePath) => {
  // set './app.js' => 'app'
  const namesArr = modulePath.split('/');
  const moduleName = namesArr[namesArr.length - 3];
  // const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
  const value = modulesFiles(modulePath);
  module[moduleName] = value.default;
  return module;
}, {});
const store = new Vuex.Store({
  strict: process.env.NODE_ENV !== 'production',
  namespaced: true,
  modules,
  state: {
    ...state
  },
  mutations: {
    ...mutations
  },
  actions: {
    ...actions
  },
  getters: {
  }
  // getters
});

export default store;
