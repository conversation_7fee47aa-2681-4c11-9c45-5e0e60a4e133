<template>
  <div ref="navbar-item" class="navbar">
    <div class="navbar-left">
      <!--      权限管理系统logo-->
      <div ref="navbar-logo" class="navbar-left-logo" @click="$router.push('/')" :style="logoWidth">
        <img src="@/assets/logo.png" class="navbar-left-img" alt="权限管理系统">
        <div :style="`width:${!collapse? '110px':'0'}`"><p>权限管理系统</p></div>
      </div>
      <!--      展开/收起 icon-->
      <div :title="collapse?'展开':'收起'" ref="navbar-collapse" class="navbar-left-collapse navbar-icon-botton"
           @click="changeCollapse">
        <span :class="collapse?'el-icon-s-fold':'el-icon-s-unfold'"></span>
      </div>
      <div title="功能地图" ref="function-map" class="navbar-left-collapse navbar-icon-botton"
           @click="openFunctionMap">
        <span class="el-icon-search"></span>
      </div>
      <!--      一级菜单展开情况-->
      <div v-if="showSmall" ref="navbar-tab" class="navbar-left-list">
        <v-tab
          :key="tabIndex"
          :navbar-list="navbarList"
          :active-name.sync="activeTabName"
          @changeTab="handleClick">
        </v-tab>
      </div>
      <!--      一级菜单收起情况-->
      <div v-else>
        <el-dropdown @command="changeTab" trigger="click" class="navbar-icon-botton">
          <span class="el-dropdown-link">
            <span class="el-icon-s-grid"></span>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in navbarList" :command="item.id" :key="item.id">
              <div :class="item.id === activeTabName ? 'tabHasClick' : ''">
                {{ item.name }}
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <div ref="navbar-right" class="navbar-right">
      <!--      全屏 icon-->
      <div title="全屏" class="navbar-icon-botton" @click="toggleFullScreen">
        <span class="el-icon-full-screen"></span>
      </div>
      <!--提示待办 icon-->
      <!--      <el-popover
              @show.once="showTodo"
              placement="bottom"
              trigger="click">
              <todo-tip ref="todo"/>
              <div title="待办" slot="reference" class="navbar-icon-botton">
                <el-badge :value="3" :max="99" style="line-height: 20px">
                  <span class="el-icon-bell"></span>
                </el-badge>
              </div>
            </el-popover>-->
      <!--用户功能信息-->
      <div style="display: flex">
        <el-dropdown @visible-change="systemChange" trigger="click" :hide-on-click="false" @command="userAction">
          <div class="el-dropdown-link navbar-icon-botton user-info-button">
            <div class="user-info-avatar">
              <i class="el-icon-s-custom"></i>
              <!--              <img src="../../../../../assets/logo.png" alt="">-->
            </div>
            <span :title="userName" class="user-info-name">{{ userName }}</span>
          </div>
          <el-dropdown-menu slot="dropdown">
            <!--            <el-dropdown-item>-->
            <!--              <span class="el-icon-user user-info-button-icon"></span>-->
            <!--              个人信息-->
            <!--            </el-dropdown-item>-->
            <!--            <el-dropdown-item><span class="el-icon-setting user-info-button-icon"></span>修改密码-->
            <!--            </el-dropdown-item>-->
            <el-dropdown-item command="role">
              <div style="position: relative" @click="()=>{roleVisible = true}">
                <div v-show="roleVisible" class="role-list-ul">
                  <el-radio-group @change="roleChange" v-model="roleRadio">
                    <el-radio v-for="item in roleList" :key="item.jsid" style="display: block;padding: 5px 0"
                              :label="item.jsid">{{ item.jsmc }}
                    </el-radio>
                  </el-radio-group>
                </div>
                <span class="el-icon-info user-info-button-icon"></span>角色({{ roleName }})
              </div>
            </el-dropdown-item>
            <el-dropdown-item divided>
              <div @click="logout">
                <span class="user-info-button-icon"></span>
                退出登录
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <el-drawer
      size="calc(100% - 75px)"
      :withHeader="false"
      custom-class="function-map"
      :visible.sync="mapDrawer"
      direction="ttb">
      <div class="function-map-content">
        <div class="function-map-content-left">
          <v-title name="最近访问"></v-title>
          <ul>
            <li v-for="item in noteRecently" :key="item.name">
              <el-button type="text" @click="handleNodeClick(item)"> {{ item.title }}</el-button>
            </li>
          </ul>
        </div>
        <div class="function-map-content-right">
          <el-input
            size="small"
            placeholder="请输入搜索内容"
            suffix-icon="el-icon-search"
            v-model="mapSearch">
          </el-input>
          <div class="tree-content">
            <el-tree
              v-if="mapDrawer"
              empty-text=""
              ref="mapTree"
              class="function-map-tree"
              :default-expand-all="true"
              :data="treeData"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick">
              <div class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ data.name }}</span>
              </div>
            </el-tree>
          </div>

        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex';
import { clearToken } from '@/utils/auth-utils';
import { logoutApi, authLogoutApi } from '@/app/layout_03_01/api/login/login-api.js';
import { cloneDeep } from 'lodash';
import VTitle from '@/components/title/VTitle';
import { sessionData } from '../../../../../utils/local-utils';
import VTab from '../../components/VTab';
import FullScreen from '../../../utils/fullScreen';
import { CONSTANTS } from '../../../../../../constant';
// import TodoTip from './TodoTip';

export default {
  components: {
    VTab,
    // TodoTip,
    VTitle
  },
  props: {
    collapse: {
      type: Boolean,
      default: () => false
    },
    activeName: {
      type: String,
      default: () => ''
    },
    navbarList: {
      type: Array,
      default: () => []
    }

  },
  data() {
    return {
      // 功能地图展示数据
      treeData: [],
      // 功能地图搜索input内容
      mapSearch: '',
      // 功能地图visible
      mapDrawer: false,
      // v-tab 显示更新
      tabIndex: 1,
      // 角色资源切换弹窗
      roleVisible: false,
      // 人员radio
      roleRadio: '',
      // navbar 宽度
      navbarWidth: 0,
      // 一级菜单 是否缩略
      showSmall: true,
      // 全屏实例
      FullScreenClass: ''
    };
  },
  computed: {
    ...mapState('layout_03_01/user', ['userInfo', 'roleId', 'menuList']),
    ...mapState('layout_03_01/tagView', ['historyList']),
    // logo 宽度
    logoWidth() {
      return { width: `${this.collapse ? '64px' : '220px'}` };
    },
    // active 选中状态
    activeTabName: {
      get() {
        return this.activeName;
      },
      set(val) {
        this.$emit('update:activeName', val);
      }
    },
    // 角色列表
    roleList() {
      return this.userInfo?.list || [];
    },
    // 已选择角色名
    roleName() {
      // eslint-disable-next-line array-callback-return
      const arr = this.userInfo?.list || [];
      const temp = arr.filter((x) => x.jsid === this.roleRadio);
      if (temp.length > 0) {
        return temp[0].jsmc;
      }
      return '';
    },
    // 用户名称
    userName() {
      return this.userInfo?.info?.userName || '';
    },
    // 历史记录取前十个
    noteRecently() {
      return this.historyList.slice(0, 10);
    }
  },
  watch: {
    // 检测一级菜单收缩展开情况 update组件
    showSmall() {
      this.tabIndex++;
    },
    // 获取menuList
    menuList: {
      handler(newVal) {
        this.treeData = cloneDeep(newVal);
      },
      immediate: true
    },
    // 功能地图通过mapSearch 过滤 treeData
    mapSearch(val) {
      this.$refs.mapTree.filter(val);
    }
  },
  mounted() {
    // 全屏实例
    this.FullScreenClass = new FullScreen();
    this.roleRadio = this.roleId;
    // 全屏实例初始化
    this.FullScreenClass.fullInit();
  },
  beforeDestroy() {
    // 销毁 handleResize
    window.removeEventListener('resize', this.handleResize);
    // 销毁 FullScreenClass
    this.FullScreenClass = null;
  },
  methods: {
    ...mapMutations('layout_03_01/tagView', ['changeHistoryList']),
    ...mapMutations('layout_03_01/user', ['setResourceList', 'setMenuList']),
    systemChange() {
      this.roleVisible = false;
    },
    /**
     * 角色切换
     */
    roleChange() {
      this.$emit('roleChange', this.roleRadio);
    },
    /**
     * 展展示todo tip
     */
    showTodo() {
      this.$nextTick(() => {
        // proper 默认不显示tab下划线
        this.$refs.todo.activeName = 'notice';
      });
    },
    /**
     * 全屏切换
     */
    toggleFullScreen() {
      this.FullScreenClass.toggleFullScreen();
    },
    /**
     * 初始化navbar
     */
    handleMounted() {
      this.navbarTab = this.$refs['navbar-tab'].offsetWidth;
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    },
    /**
     * 检测屏幕宽度
     */
    handleResize() {
      this.navbarWidth = this.$refs['navbar-item'].offsetWidth;
      // this.navbarLogo = this.$refs['navbar-logo'].offsetWidth;
      this.navbarLogo = this.collapse ? 64 : 220;
      this.navbarCollapse = this.$refs['navbar-collapse'].offsetWidth + this.$refs['function-map'].offsetWidth;
      this.navbarRight = this.$refs['navbar-right'].offsetWidth;
      const val = this.navbarWidth - this.navbarLogo - this.navbarCollapse - this.navbarRight - this.navbarTab - 30;
      // this.$emit('change-tab-width', val);
      if (val > 0) {
        this.showSmall = true;
      } else {
        this.showSmall = false;
      }
    },
    /**
     * changeCollapse
     */
    changeCollapse() {
      const isCollapse = !this.collapse;
      this.$emit('update:collapse', isCollapse);
      setTimeout(() => {
        this.handleResize();
      }, 100);
    },
    /**
     *  tab menu 切换
     * @param tab
     */
    handleClick(tab) {
      this.$emit('changeTab', tab);
    },
    /**
     *  tab menu(缩略情况下) 切换
     * @param tab
     */
    changeTab(tab) {
      this.$emit('changeTab', { name: tab });
      this.activeTabName = tab;
    },
    /**
     * 登出
     */
    logout() {
      // 清空tagview vuex 路由列表
      const clearLocation = () => {
        this.changeHistoryList([]);
        // 清空资源list
        this.setResourceList([]);
        // 清空菜单list
        this.setMenuList([]);
        sessionData('clean', 'roleList');
        sessionData('clean', 'menuList');
        sessionData('clean', 'layout_03_01/tagView');
        sessionData('clean', 'userInfo');
        sessionData('clean', 'roleId');
      };

      // loginMode:1 统一身份认证
      if (this.userInfo && this.userInfo.info && this.userInfo.info.loginMode && this.userInfo.info.loginMode === 1) {
        authLogoutApi()
          .finally(() => {
            clearToken();
            window.location.href = `${CONSTANTS.TYSFRZ_URL}/signout`;
          });
        clearLocation();
        // window.location.href = 'http://*************:6888/tysfrz/login';
        // window.location.href = 'http://localhost:8080/spring_oauth_server_war_exploded';
        // this.$router.push({ path: '/login' });
      } else {
        logoutApi()
          .finally(() => {
            clearToken();
          });
        clearLocation();
        this.$router.push({ path: '/login_common' });
      }
    },
    /**
     * 右侧用户操作
     * @param command
     */
    userAction(command) {
      if (command !== 'role') {
        this.roleVisible = false;
      }
    },
    /**
     * 打开功能地图
     */
    openFunctionMap() {
      this.mapDrawer = true;
      this.mapSearch = '';
    },
    /**
     * 功能地图点击
     * @param route
     */
    handleNodeClick(route) {
      if (!route.path) {
        return;
      }
      this.mapDrawer = false;
      // 新标签页打开
      if (route.isOut === '1') {
        this.$emit('rollback');
        window.open(route.path, '_blank');
        return;
      }
      // 外部链接打开
      if (route.openType === '2') {
        this.$emit('rollback');
        window.open(`${window.location.origin}/#${route.path}`, '_blank');
      } else {
        this.$router.push(route.path);
      }
    },
    /**
     * 功能地图搜索过滤
     * @param value
     * @param data
     * @returns {boolean}
     */
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    }
  }
};
</script>
<style lang="scss" scoped>
.tabHasClick {
  color: #66b1ff;
}

.user-info-button-icon {
  margin-right: 10px !important;
}

.navbar {
  height: 60px;
  display: flex;
  justify-content: space-between;

  &-icon-botton {
    font-size: 20px;
    color: #FFFFFF;
    padding: 0 10px;
    cursor: pointer;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }
  }

  &-left {
    display: flex;

    & > div:not(.navbar-left-list) {
      cursor: pointer;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }

    &-logo {
      padding-left: 20px;
      overflow: hidden;
      transition: all .5s;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;

      & > div {
        font-size: 16px;
        font-weight: 600;
        margin-left: 10px;
        color: #FFFFFF;
        overflow: hidden;
        transition: all 0.2s;

        p {
          white-space: nowrap;
        }
      }

    }

    &-img {
      flex-shrink: 0;
      width: 26px;
      height: 26px;
      vertical-align: middle;
    }
  }

  .el-tabs__header {
    margin: 0;
  }

  &-right {
    display: flex;
    width: 240px;
    flex-shrink: 0;
    position: absolute;
    right: 0;
    height: 60px;
    justify-content: flex-end;

    .user-info-name {
      display: inline-block;
      max-width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-info-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 6px;
      background-color: #ffffff;

      i {
        color: #2d8cf0;
        font-size: 24px;
        vertical-align: top;
      }

      img {
        vertical-align: top;
        width: 100%;
      }
    }

    .user-info-button {
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-logout {
      color: #FFFFFF;
      cursor: pointer;
    }
  }
}

.function-map-content {
  background: $page-bg-color;
  line-height: normal;
  min-width: 800px;
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;

  & > div {
    padding: $page-content-padding;
  }

  &-left {
    width: 220px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;

    ul {
      flex: 1;
      overflow: auto;
    }
  }

  &-right {
    flex: 1;
    overflow: hidden;

    .tree-content {
      height: 100%;
      overflow: hidden;
    }
  }

}

</style>

<style lang="scss">

.function-map-tree {
  background: $page-bg-color;
  width: 100%;
  height: calc(100% - 52px);
  overflow: auto;

  .el-tree__empty-block {
    height: auto !important;
  }

  .custom-tree-node {
    flex: 1;
    padding-left: 20px;

    &:hover {
      color: $page-font-hover-color;
    }
  }

  & > div {
    float: left;
    margin-top: $page-content-margin;
    width: 30%;
    margin-right: 3%;

    & > div:first-child {
      background: $page-label-color !important;
      color: #FFFFFF;

      .custom-tree-node:hover {
        color: #FFFFFF;
      }
    }

    .el-tree-node__label {
      flex: 1;
      text-align: left;
    }
  }

  .el-tree-node__content {
    border-radius: 5px;
  }
}

.role-list-ul {
  position: absolute;
  top: 0;
  transform: translateX(-100%);
  background: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0);
  padding: 10px;
  left: -25px;
}

.navbar {
  .navbar-left-list {
    .el-tabs__header {
      margin: 0;
    }

    .el-tabs__content {
      display: none;
    }

    .el-tabs__active-bar {
      bottom: 1px;
      background-color: #4fe3c1;
    }

    .el-tabs__nav-wrap::after {
      background: transparent;
    }

    .el-tabs__item {
      box-sizing: content-box;
      padding: 10px 10px !important;
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
      font-weight: bold;
      vertical-align: top;
    }

    .el-tabs__item:hover {
      color: rgba(255, 255, 255, 1);
      background-color: rgba(255, 255, 255, 0.05);
    }

    .el-tabs__item.is-active {
      color: #FFFFFF;
    }
  }
}

.function-map.el-drawer.ttb {
  top: 61px;
  overflow: auto;

  .el-drawer__body {
    overflow: auto;
  }
}

</style>
