<template>
  <el-table
    class="zhxy-table"
    :data="tableData"
    :height="scrollerHeight"
    stripe
    border
    @selection-change="handleSelectionChange">
    <el-table-column align="center" type="selection" width="50"></el-table-column>
    <el-table-column prop="zhid" label="账户ID" width="200"></el-table-column>
    <el-table-column prop="qz" label="群组" width="100" show-overflow-tooltip>
      <template slot-scope="scope">
        <span v-if="scope.row.qzmc" class="zhgl-group-list" @click="showGroup(scope.row)">{{ scope.row.qzmc }}</span>
        <span v-else class="zhgl-group-list" @click="showGroup(scope.row)">--</span>
      </template>
    </el-table-column>
    <el-table-column prop="sfzh" label="证件号" width="150" show-overflow-tooltip></el-table-column>
    <el-table-column prop="xm" label="姓名" width="150" show-overflow-tooltip></el-table-column>
    <el-table-column prop="hyzt" label="活跃状态" width="120">
      <template slot-scope="scope">
        <span>{{ scope.row.hyzt | getHyzt}}</span>
      </template>
    </el-table-column>
    <el-table-column prop="sjh" label="联系电话" show-overflow-tooltip width="150"></el-table-column>
    <el-table-column prop="" label="密码强度" width="80">
      <template slot-scope="scope">
        <span>{{ scope.row.mmqd | getMmqd}}</span>
      </template>
    </el-table-column>
    <el-table-column prop="gqsj" label="过期时间" width="180">
      <template slot-scope="scope">
        {{scope.row.gqsj | dateFilter}}
      </template>
    </el-table-column>
    <el-table-column prop="zhly" label="数据来源" width="110">
      <template slot-scope="scope">
        {{scope.row.zhly | getZhly}}
      </template>
    </el-table-column>
    <el-table-column prop="" label="状态" width="80">
      <template slot-scope="scope">
        <span class="zhgl-group-list" @click="editStatus(scope.row)">{{ scope.row.zhzt | getZhzt}}</span>
      </template>
    </el-table-column>
    <el-table-column fixed="right" prop="" label="操作" width="200">
      <template slot-scope="scope">
        <el-button size="small" @click="modifyData(scope.row)" type="text">修改</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" @click="modifyMm(scope.row)" type="text">重置密码</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" @click="formDetail(scope.row)" type="text">查看</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" type="text" @click="delTable(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';

export default {
  name: 'YhglTable',
  filters: {
    dateFilter,
    getQz(val) {
      let res = '';
      if (!val) {
        return res;
      }
      val.forEach((item) => {
        res = res ? `${res},${ item.name}` : `${ item.name}`;
      });
      return res;
    },
    getMmqd(val) {
      if (!val) {
        return '';
      }
      const list = {
        1: '低',
        2: '中',
        3: '高',
        4: '不符合密码'
      };
      return list[val];
    },
    // 活跃状态
    getHyzt(val) {
      if (!val) {
        return '';
      }
      const list = {
        1: '活跃',
        2: '休眠'
      };
      return list[val];
    },
    getZhzt(val) {
      if (!val) {
        return '';
      }
      const list = {
        1: '启用',
        2: '停用',
        3: '锁定'
      };
      return list[val];
    },
    getZhly(val) {
      if (!val) {
        return '';
      }
      const list = {
        1: '管理员创建',
        2: '同步程序'
      };
      return list[val];
    }
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {};
  },
  methods: {
    /**
     * 查看数据详情
     * @param val
     */
    formDetail(val) {
      this.$emit('formDetail', val);
    },
    /**
     * 修改数据
     * @param val
     */
    modifyData(val) {
      this.$emit('modifyData', val);
    },
    /**
     * 修改密码
     * @param val
     */
    modifyMm(val) {
      this.$emit('modifyMm', val);
    },
    /**
     * 删除数据
     * @param val
     */
    delTable(val) {
      this.$emit('delTable', val);
    },
    /**
     * 展示群组弹窗
     * @param val
     */
    showGroup(val) {
      this.$emit('showGroupDialog', val);
    },
    /**
     * table selection操作
     * @param val
     */
    handleSelectionChange(val) {
      this.$emit('handleSelectionChange', val);
    },
    /**
     * 编辑状态
     * @param val
     */
    editStatus(val) {
      this.$emit('editStatus', val);
    }
  }
};
</script>

<style lang="scss" scoped>
.zhgl-group-list{
  color: $page-font-hover-color;
  cursor: pointer;
}
</style>
