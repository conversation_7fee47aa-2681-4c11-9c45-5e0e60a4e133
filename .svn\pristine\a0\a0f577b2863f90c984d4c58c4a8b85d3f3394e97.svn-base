<template>
  <div class="department-user" ref="element">
    <el-button type="primary" size="small" @click="buttonAction"> {{isEdit ? '编辑' : '保存'}}</el-button>
    <el-form :disabled="isEdit" label-width="150px" :model="formData" :rules="rules" class="zhxy-form zhxy-form-search-part" :class="isEdit ? '' : 'form-status-edit'">
      <el-form-item prop="qzid">
        <span class="zhxy-form-label" slot="label">群组ID</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.qzid" placeholder="群组ID" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="qzmc">
        <span slot="label" class="zhxy-form-label">群组名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.qzmc" placeholder="群组名称" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="pxh">
        <span class="zhxy-form-label" slot="label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh" placeholder="排序号" size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjr" placeholder="创建人" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjsj" placeholder="创建时间" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgr" placeholder="变更人" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgsj" placeholder="变更时间" size="small"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { findJsxyhlist, update } from '@/app/xtgl_03_01/api/jsgl/jsgl.js';

export default {
  name: 'DepartmentDetail',
  props: {
    formData: {
      type: Object,
      default: () => ({
        qzid: '',
        qzmc: '',
        pxh: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      })
    }
  },
  data() {
    return {
      // 是否可编辑
      isEdit: true,
      rules: {
        qzid: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入群组ID'
          },
          {
            max: 32,
            message: '群组ID长度不能多于32位'
          }
        ],
        qzmc: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入群组名称'
          },
          {
            max: 32,
            message: '群组名称长度不能多于32位'
          }
        ]
      }
    };
  },
  methods: {
    // 切换编辑状态
    buttonAction() {
      if (this.isEdit === false) {
        this.$emit('saveForm');
      } else {
        this.isEdit = !this.isEdit;
      }
    }
  }
};
</script>
<style lang="scss">
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
      cursor: default;
    }
  }
</style>
<style lang="scss" scoped>
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
    }
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }
    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
