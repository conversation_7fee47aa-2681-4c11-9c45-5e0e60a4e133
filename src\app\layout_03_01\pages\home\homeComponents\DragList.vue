<template>
<div>
  <draggable
    v-bind="dragOptions"
    class="list-group"
    :list="componentList"
    :move="onMove"
    @end="onEnd"
    @start="onStart"
  >
    <transition-group name="list">
      <div class="list-group-item" v-for="element in componentList" :key="element.name">
        <div class="list-group-item-title">
          <div class="drag-handle" :style="`opacity: ${dragging  ? 1 : 0}`">{{ getComponentName(element.name) }}</div>
          <el-dropdown>
            <div class="drag-module-btn el-dropdown-link" :style="`display: ${dragging  ? 'none' : 'block'}`">
              <i class="el-icon-more"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item><div @click="hideBlock(element.name)">隐藏</div></el-dropdown-item>
              <el-dropdown-item><div @click="addBlock">添加区块</div></el-dropdown-item>
              <el-dropdown-item><div @click="revertBlock"></div>恢复系统默认配置</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

        </div>
        <div v-show="!dragging" class="component-box">
          <slot name="content" :content-name="element.name"></slot>
<!--          <component :is="element.name"/>-->
        </div>
      </div>

    </transition-group>
  </draggable>
</div>
</template>

<script>
import Draggable from 'vuedraggable';

export default {
  name: 'DragList',
  components: {
    Draggable
  },
  props: {
    // dragging
    dragging: {
      type: Boolean,
      default: false
    },
    // drag options
    dragOptions: {
      type: Object,
      default: () => ({})
    },
    // drag list
    componentList: {
      type: Array,
      default: () => []
    },
    // drag Map
    dragMap: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 当前是否在执行拖拽
      // dragging: false
    };
  },
  computed: {
    getComponentName() {
      return (name) => {
        const index = this.dragMap.findIndex((item) => item.name === name);
        if (index > -1) {
          return this.dragMap[index].title;
        }
        return '';
      };
    }
  },
  methods: {
    /**
     * drag 组件拖动到列表位置事件
     * @param e
     * @returns {boolean}
     */
    onMove(e) {
      const name = e.draggedContext.element.name;
      const position = e.relatedContext.component.$attrs.animation === 30 ? 'left' : 'right';
      const index = this.dragMap.findIndex((x) => x.name === name);
      if (index > -1) {
        const pos = this.dragMap[index].position;
        if (pos === 'both') {
          return true;
        }
        return pos === position;
      }
      return false;
    },
    /**
     * drag 组件 拖拽开始事件
     * @param e
     */
    onStart(e) {
      this.$emit('update:dragging', true);
    },
    /**
     * drag 组件拖拽结束事件
     * @param e
     */
    onEnd(e) {
      this.$emit('update:dragging', false);
      // 保存当前的配置
      this.$emit('saveConfig');
    },
    /**
     * 隐藏区块
     * @param name
     */
    hideBlock(name) {
      this.$emit('hideBlock', name);
    },
    /**
     * 添加区块
     */
    addBlock() {
      this.$emit('addBlock');
    },
    /**
     * 恢复默认区块
     */
    revertBlock() {
      this.$emit('revertBlock');
    }
  }
};
</script>

<style lang="scss" scoped>
$component-title-height:40px;
// 拖拽列表 transition 样式
.list-enter-active {
  transition: all .3s linear;
}

.list-enter,
.list-leave-to {
  opacity: .5;
}

// 默认 拖拽占位
.list-group {
  > span {
    display: block;
    min-height: $component-title-height;
  }
}

// 拖拽内容class
.component-box {
  padding: $page-content-padding;
  background-color: #FFFFFF;
}
.list-group-item{
  position: relative;
  min-height: 40px;
  margin-bottom: $page-content-margin;
}
.list-group-item-title{
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 1;
  display: flex;
  height: $component-title-height;
}
.drag-module-btn{
  flex-shrink: 0;
  padding: $page-content-padding;
  cursor: pointer;
  &:hover{
    color: $page-font-hover-color;
  }
}

// 可拖拽的元素 class
.drag-handle {
  flex: 1;
  cursor: move;
  opacity: 0;
  font-weight: bold;
  height: $component-title-height;
  line-height: $component-title-height;
  padding-left: 20px;
  border-radius: 5px;
  background-color: #efefef;
}

// 拖拽中 当前元素样式
.draggingClass {
  background-color: #FFFFFF;
  overflow: hidden !important;
  height: $component-title-height !important;
  border-radius: 5px;
  .component-box {
    display: none;
    height: 0;
  }
  .drag-module-btn{
    display: none !important;
  }

  .drag-handle {
    opacity: 1 !important;
  }
}

// 当前拖拽元素的占位 class
.ghost {
  .drag-handle {
    background: #a3a3a3;
    opacity: .5 !important;
  }
}

</style>
