<template>
  <div class="login-container" v-loading="loadStatus(loading,roleLoading)"></div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'; // 相关语法糖
import { sessionData } from '@/utils/local-utils.js';
import { authLoginApi } from '@/app/layout_03_01/api/login/login-api.js';
import { CONSTANTS } from '../../../../constant';

export default {
  name: 'Login',
  components: {},
  data() {
    return {
      // 登录loading
      loading: false,
      // 重定向信息
      redirect: '/',
      // url query
      otherQuery: {}
    };
  },
  computed: {
    ...mapState('layout_03_01/user', ['roleLoading']),
    loadStatus() {
      return (load, role) => {
        if (load) {
          return true;
        }
        if (!load && !role) {
          return false;
        }
        return false;
      };
    }
  },
  // watch: {
  //   // 监听路由信息 过去重定向信息
  //   $route: {
  //     handler(route) {
  //       this.setRoleLoading(false);
  //       const codeQuery = window.location.search.indexOf('code') > -1;
  //       debugger;
  //       if (codeQuery) {
  //         const val = this.getQueryString('code');
  //         if (val) {
  //           this.loading = true;
  //           const urlObj = window.location;
  //           authLoginApi({ code: val })
  //             .then((res) => {
  //               const result = res.data.content;
  //               // 存储信息到cookie
  //               this.saveUserInfo(result);
  //               // 进入app跳转首页
  //               window.location.href = `${urlObj.origin}/#/?section=${val}`;
  //               // this.$router.push({
  //               //   path: this.redirect || '/',
  //               //   query: this.otherQuery
  //               // });
  //             })
  //             .catch(() => {
  //               this.loading = false;
  //             });
  //         } else {
  //           this.loading = true;
  //           this.handleLoginAuth();
  //         }
  //       } else {
  //         this.loading = true;
  //         this.handleLoginAuth();
  //       }
  //       // const query = route.query;
  //       // if (query) {
  //       //   this.redirect = query.redirect;
  //       //   this.otherQuery = this.getOtherQuery(query);
  //       // }
  //     },
  //     immediate: true
  //   }
  // },
  mounted() {
    this.setRoleLoading(false);
    const codeQuery = window.location.search.indexOf('code') > -1;
    if (codeQuery) {
      const val = this.getQueryString('code');
      if (val) {
        this.loading = true;
        const urlObj = window.location;
        authLoginApi({ code: val })
          .then((res) => {
            const result = res.data.content;
            // 存储信息到cookie
            this.saveUserInfo(result);
            // 进入app跳转首页
            window.location.href = `${CONSTANTS.LOCALHOST}/#/login?section=${val}`;
            // this.$router.push({
            //   path: this.redirect || '/',
            //   query: this.otherQuery
            // });
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        this.loading = true;
        this.handleLoginAuth();
      }
    } else {
      if (this.$route.query && this.$route.query.section) {
        this.$router.push('/');
        return;
      }
      this.loading = true;
      this.handleLoginAuth();
    }
  },
  methods: {
    ...mapMutations('layout_03_01/user', ['setLogin', 'setLoginRole', 'setHasLogin', 'setRoleLoading']), // vuex setLogin

    getQueryString(name) {
      const reg = new RegExp(`(^|&)${ name }=([^&]*)(&|$)`, 'i');
      const r = window.location.search.substr(1).match(reg);
      if (r != null) return (r[2]); return null;
    },
    /**
     * 保存/重置用户信息
     * @param response
     */
    saveUserInfo(response) {
      this.setHasLogin(true);
      // // 判断复选框是否被勾选 勾选则调用配置cookie方法
      // if (this.loginForm.remember) {
      //   // 登陆成功设置 token
      //   const token = response.token || '';
      //   setToken(token, 7);
      // } else {
      //   // 登陆成功设置 token
      //   const token = response.token || '';
      //   setToken(token, 1);
      // }
      // 存储用户基础信息
      let jsid = '';
      if (response.roleList) {
        jsid = response.roleList[0] ? response.roleList[0].jsid : '';
      }
      const userInfo = {
        info: response.data,
        list: response.roleList
      };
      this.setLogin(userInfo || {});
      this.setLoginRole(jsid);
      // 清楚 权限列表
      sessionData('clear', 'roleList');
    },
    /**
     * router解析
     * @param query
     * @returns {{}}
     */
    getOtherQuery(query) {
      return Object.keys(query)
        .reduce((acc, cur) => {
          if (cur !== 'redirect') {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
    },
    /**
     * 统一身份认证登录
     */
    handleLoginAuth() {
      const urlAuth = `${CONSTANTS.TYSFRZ_URL}/oauth/authorize`;
      const urlPro = CONSTANTS.LOCALHOST;
      const clientId = CONSTANTS.CLIENTID;
      const time = new Date().getTime();
      window.location.replace(`${urlAuth}?response_type=code&scope=read&client_id=${clientId}&redirect_uri=${urlPro}&state=${time}`);
    }
  }
};
</script>
<style lang="scss" scoped>
.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
