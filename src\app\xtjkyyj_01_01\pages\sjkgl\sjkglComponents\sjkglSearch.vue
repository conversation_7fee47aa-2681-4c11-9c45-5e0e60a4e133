<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">

      <el-form-item>
        <span class="zhxy-form-label" slot="label">数据库名称</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.sjymc" placeholder="数据库名称" size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">数据库类型</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.sjylx" placeholder="用户状态" size="small">
          <el-option
            v-for="item in sjylxOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">数据库状态</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.sjyzt" size="small">
          <el-option
            v-for="item in sjyztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

</template>

<script>
export default {
  name: 'CjrwgzSearch',
  data() {
    return {
      // label 宽度
      lableWidth: '100px',
      // search params
      listQuery: {
        sjymc: null,
        sjylx: null,
        sjyzt: null
      },
      sjylxOptions: [{
        label: '全部',
        value: ''
      }, {
        label: '结构化',
        value: 1
      }, {
        label: '非结构化',
        value: 2
      }],

      sjyztOptions: [{
        label: '全部',
        value: ''
      }, {
        label: '启用',
        value: '1'
      }, {
        label: '停用',
        value: '0'
      }]

    };
  },
  mounted() {
    this.listQuery.sjylx = '';
    this.listQuery.sjyzt = '';
  },
  methods: {
    /**
       * 搜索查询
       */
    search() {
      const param = {
        sjymc: this.listQuery.sjymc,
        sjylx: this.listQuery.sjylx,
        sjyzt: this.listQuery.sjyzt
      };
      this.$emit('search', param);
    },
    /**
       * 重置搜索条件
       */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });
      this.search();
    }
  }
};
</script>

<style lang="scss" scoped>
  .title {
    display: flex;
    justify-content: space-between;
  }

  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
</style>
