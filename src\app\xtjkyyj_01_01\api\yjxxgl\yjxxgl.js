import { createAPI } from '@/utils/request';

const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/xtjkyyj_01_01/yjxxgl`;
// 查询预警信息列表
export const findYjxxList = (data) => createAPI(`${APP_PRE}/findYjxxList`, 'get', data);
// 修改是否忽略
export const updateSfhl = (data) => createAPI(`${APP_PRE}/updateSfhl`, 'post', data);
// 查询预警类别列表
export const findYjlb = (data) => createAPI(`${APP_PRE}/findYjlb`, 'get', data);
// 查询预警分类列表
export const findYjfl = (data) => createAPI(`${APP_PRE}/findYjfl`, 'get', data);
// 查询预警级别列表
export const findYjjb = (data) => createAPI(`${APP_PRE}/findYjjb`, 'get', data);
// 删除
export const del = (data) => createAPI(`${APP_PRE}/deleteYjxx`, 'post', data);
