<template>
<div class="zjjjc-content">
 <div class="zjjjc-content-main">
   <v-title name="中间件监测"></v-title>
   <div style="height: calc(100% - 80px)">
     <jjjjc-table v-loading="pageLoading" :table-data="tableData"></jjjjc-table>
   </div>
   <el-pagination
     @size-change="handleSizeChange"
     @current-change="handleCurrentChange"
     :current-page="currentPage"
     :page-sizes="[30, 50, 100, 200]"
     :page-size="pageSize"
     layout="total,sizes,  prev, pager, next, jumper"
     :total="pageTotal">
   </el-pagination>
 </div>
</div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import { findTableList } from '@/app/xtjkyyj_01_01/api/zjjjc';
import JjjjcTable from './zjjjcComponents/ZjjjcTable';

export default {
  components: {
    VTitle,
    JjjjcTable
  },
  data() {
    return {
      // 表格数据
      tableData: [],
      // 页面loading
      pageLoading: false,
      // 当前页码
      currentPage: 1,
      // 当前一页个数
      pageSize: 30,
      // 数据个数
      pageTotal: 0
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
    /**
     * table init
     */
    getTableData() {
      this.pageLoading = true;
      const params = {
        page: this.currentPage,
        pageSize: this.pageSize
      };
      findTableList(params).then((res) => {
        console.log('结果', res);
        if (res.code === 200) {
          this.tableData = res.data.content || [];
          this.pageTotal = res.data.pageInfo.total || 0;
        }
      }).finally(() => {
        this.pageLoading = false;
      });
    },
    /**
     * pageSize change
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.getTableData();
    },
    /**
     * page number change
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getTableData();
    }
  }

};
</script>

<style lang="scss" scoped>
.zjjjc-content-main{
  height: 100%;
}
</style>
