<template>
  <el-table class="zhxy-table" :data="tableData" stripe border
            @selection-change="handleSelectionChange"
            :height="scrollerHeight">
    <el-table-column align="center" type="selection" width="50"></el-table-column>
    <el-table-column prop="yhid" label="用户ID" width="100"></el-table-column>
    <el-table-column prop="xm" label="用户姓名" width="100"></el-table-column>
    <el-table-column prop="bmmc" label="部门" width="150" :show-overflow-tooltip="true">
      <template slot-scope="scope">
        <a v-if="scope.row.bmmc !== undefined" href="javascript:void(0)">{{scope.row.bmmc}}</a>
        <a v-else href="javascript:void(0)" @click="addYhbm(scope.row.yhid)">---</a>
      </template>
    </el-table-column>
    <el-table-column prop="jsmc" label="角色" width="150"
                     show-overflow-tooltip></el-table-column>
    <el-table-column prop="sjh" label="手机号" width="120"></el-table-column>
    <el-table-column prop="dzyx" label="电子邮箱"
                     :show-overflow-tooltip="true" width="150"></el-table-column>
    <el-table-column prop="sfms" label="身份描述" width="130"></el-table-column>
    <el-table-column prop="" label="是否自建" width="80">
      <template slot-scope="scope">
        <span v-if="scope.row.sfzj === '0'">否</span>
        <span v-else-if="scope.row.sfzj === '1'">是</span>
      </template>
    </el-table-column>
    <el-table-column prop="" label="可登录方式" width="110">
      <template slot-scope="scope">
        <span v-if="scope.row.kdlfs === '1100000000'">适用全部</span>
        <span v-if="scope.row.kdlfs === '1000000000'">统一身份认证</span>
        <span v-if="scope.row.kdlfs === '0100000000'">其他身份登录</span>
      </template>
    </el-table-column>
    <el-table-column prop="bz" label="备注" :show-overflow-tooltip="true" width="120"></el-table-column>
    <el-table-column prop="" label="用户状态" width="80">
      <template slot-scope="scope">
        <span v-if="scope.row.yhzt === '1'">启用</span>
        <span v-else-if="scope.row.yhzt === '0'">禁用</span>
      </template>
    </el-table-column>
    <el-table-column prop="cjr" label="创建人" width="120"></el-table-column>
    <el-table-column prop="cjsj" label="创建时间" width="180">
      <template slot-scope="scope">
        {{scope.row.cjsj | dateFilter}}
      </template>
    </el-table-column>
    <el-table-column prop="bgr" label="变更人" width="130"></el-table-column>
    <el-table-column prop="bgsj" label="变更时间" width="180">
      <template slot-scope="scope">
        {{scope.row.bgsj | dateFilter}}
      </template>
    </el-table-column>
    <el-table-column fixed="right" prop="" label="操作" width="100">
      <template slot-scope="scope">
        <el-button size="small" @click="modifyYh(scope.row)" type="text">修改</el-button>
        <i style="color: #e8eaec;"> | </i>
        <el-button size="small" type="text" @click="delYh(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';

export default {
  name: 'YhglTable',
  filters: {
    dateFilter
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {};
  },
  methods: {
    addYhbm(val) {
      this.$emit('addYhbm', val);
    },
    editYhbm(val) {
      this.$emit('editYhbm', val);
    },
    handleSelectionChange(val) {
      this.$emit('handleSelectionChange', val);
    },
    modifyYh(val) {
      this.$emit('modifyYh', val);
    },
    delYh(val) {
      this.$emit('delYh', val);
    }
  }
};
</script>

<style scoped>

</style>
