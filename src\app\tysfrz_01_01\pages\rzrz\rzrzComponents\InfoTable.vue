<template>
  <el-table
    class="zhxy-table"
    :data="tableData"
    stripe
    border
    :height="'100%'">
<!--    <el-table-column align="center" type="index" label="序号" width="52"></el-table-column>-->
    <el-table-column prop="zhid" label="账号名称"></el-table-column>
    <el-table-column prop="xm" label="姓名" width="130"></el-table-column>
    <el-table-column prop="rzsj" label="认证时间" width="180">
      <template slot-scope="scope">
        {{scope.row.rzsj | dateFilter}}
      </template>
    </el-table-column>

    <el-table-column prop="rzzt" label="认证结果" width="180">
      <template slot-scope="scope">
        {{scope.row.rzzt | getRzjg}}
      </template>
    </el-table-column>

    <el-table-column prop="rzsbyy" label="认证失败原因" show-overflow-tooltip></el-table-column>

    <el-table-column prop="rzip" label="认证IP" width="130" show-overflow-tooltip></el-table-column>

    <el-table-column prop="yymc" label="认证名称" width="130" show-overflow-tooltip></el-table-column>
    <el-table-column prop="rzfs" label="认证方式" width="130" show-overflow-tooltip></el-table-column>
  </el-table>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';

export default {
  name: 'YhglTable',
  filters: {
    dateFilter,
    // 认证结果
    getRzjg(val) {
      const list = {
        1: '认证成功',
        0: '认证失败'
      };
      if (!val && val !== 0) {
        return '';
      }
      return list[val];
    }
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  methods: {
  }
};
</script>

<style scoped>

</style>
