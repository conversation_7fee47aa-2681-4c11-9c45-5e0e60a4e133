<template>
  <div class="dialog-content">
    <el-form ref="yjpzForm" :model="formData" :rules="rules" label-width="120px" label-position="right">
      <el-form-item label="预警标识" prop="yjbs">
        <el-input :disabled="!yjbsVisible" v-model="formData.yjbs" placeholder="必填" size="small" style="width: 98%"/>
      </el-form-item>
      <el-form-item label="预警名称" prop="yjmc">
        <el-input v-model="formData.yjmc" placeholder="必填" size="small" style="width: 98%"/>
      </el-form-item>
      <el-form-item label="预警类别" prop="yjlb">
        <el-select class="zhxy-form-inline" v-model="formData.yjlb" placeholder="必填" size="small" style="width: 98%">
          <el-option
            v-for="item in yjlbOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="预警分类" prop="yjfl">
        <el-select class="zhxy-form-inline" v-model="formData.yjfl" placeholder="必填" size="small" style="width: 98%">
          <el-option
            v-for="item in yjflOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="预警说明" prop="yjsm">
        <el-input type="textarea" :rows="3" v-model="formData.yjsm" placeholder="选填" size="small" style="width: 98%"/>
      </el-form-item>
      <el-form-item label="预警级别" prop="yjjbm" >
        <el-select class="zhxy-form-inline" v-model="formData.yjjbm" placeholder="预警级别" size="small" style="width: 98%">
          <el-option
            v-for="item in yjjbOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="阈值01" prop="yzkzzd01">
        <el-input v-model="formData.yzkzzd01" placeholder="选填" size="small" style="width: 98%"/>
      </el-form-item>
      <el-form-item label="阈值02" prop="yzkzzd02">
        <el-input v-model="formData.yzkzzd02" placeholder="选填" size="small" style="width: 98%"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  findYjfl,
  findYjjb, findYjlb
} from '@/app/xtjkyyj_01_01/api/yjpzgl/yjpzgl';

export default {
  name: 'YjpzxxEdit',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    yjbsVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 预警名称校验
    // eslint-disable-next-line consistent-return
    const validateYjmc = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入预警名称'));
      }
      callback();
    };
    // 预警标识校验
    // eslint-disable-next-line consistent-return
    const validateYjbs = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('请输入预警标识'));
      }
      callback();
    };
    // 预警类别校验
    // eslint-disable-next-line consistent-return
    const validateYjlb = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入预警类别'));
      }
      callback();
    };
    // 预警分类校验
    // eslint-disable-next-line consistent-return
    const validateYjfl = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入预警分类'));
      }
      callback();
    };
    // 预警级别校验
    // eslint-disable-next-line consistent-return
    const validateYjjbm = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择预警级别'));
      }
      callback();
    };
    return {
      // label 宽度
      lableWidth: '75px',
      yjlbOptions: [{ label: '', value: '' }],
      yjflOptions: [{ label: '', value: '' }],
      yjjbOptions: [{ label: '', value: '' }],
      rules: {
        yjmc: [
          {
            required: true,
            trigger: 'blur',
            validator: validateYjmc
          },
          {
            max: 100,
            message: '预警名称长度不能多于100位'
          }
        ],
        yjbs: [
          {
            required: true,
            trigger: 'blur',
            validator: validateYjbs
          },
          {
            max: 50,
            message: '预警标识长度不能多于50位'
          }
        ],
        yjlb: [
          {
            required: true,
            trigger: 'blur',
            validator: validateYjlb
          },
          {
            max: 100,
            message: '预警类别长度不能多于100位'
          }
        ],
        yjfl: [
          {
            required: true,
            trigger: 'blur',
            validator: validateYjfl
          },
          {
            max: 100,
            message: '预警分类长度不能多于100位'
          }
        ],
        yjsm: [
          {
            required: false,
            trigger: 'blur'
          },
          {
            max: 500,
            message: '预警说明长度不能多于500位'
          }
        ],
        yjjbm: [
          {
            required: true,
            trigger: 'blur',
            validator: validateYjjbm
          }
        ],
        yzkzzd01: [
          {
            required: false,
            trigger: 'blur'
          },
          {
            max: 20,
            message: '阈值01长度不能多于20位'
          }
        ],
        yzkzzd02: [
          {
            required: false,
            trigger: 'blur'
          },
          {
            max: 20,
            message: '阈值02长度不能多于20位'
          }
        ]
      }
    };
  },
  mounted() {
    this.findYjlb();
    this.findYjfl();
    this.findYjjb();
  },
  methods: {
    findYjlb() {
      findYjlb().then((res) => {
        if (res.code === 200) {
          this.yjlbOptions = res.data.content;
        }
      });
    },
    findYjfl() {
      findYjfl().then((res) => {
        if (res.code === 200) {
          this.yjflOptions = res.data.content;
        }
      });
    },
    findYjjb() {
      findYjjb().then((res) => {
        if (res.code === 200) {
          this.yjjbOptions = res.data.content;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-content {
  overflow: auto;
  padding: 0 10px;
}
</style>
