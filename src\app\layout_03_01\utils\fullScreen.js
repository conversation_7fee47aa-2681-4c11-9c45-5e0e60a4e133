// 全屏设置
export default class FullScreen {
  constructor() {
    this.browserKernel = ''; // 浏览器类型
    this.canFullScreen = false; // 是否支持全屏
    this.isFullScreen = false; // 是否全屏
  }

  /**
   * 全屏点击触发
   */
  toggleFullScreen() {
    if (this.canFullScreen) {
      if (this.isFullScreen) {
        // 关闭全屏
        this.exitFullScreen();
        this.isFullScreen = false;
      } else {
        // 打开全屏
        this.requestFullScreen(document.body);
        this.isFullScreen = true;
      }
    } else {
      this.$message.warning({
        content: '当前浏览器暂不支持全屏模式，请切换浏览器后重新尝试！',
        duration: 3
      });
    }
  }

  /**
   * 判断浏览器 获取对应全屏事件
   * @param element
   */
  // eslint-disable-next-line class-methods-use-this
  requestFullScreen(element) {
    // 判断各种浏览器，找到正确的方法
    const requestMethod = element.requestFullScreen // W3C
      || element.webkitRequestFullScreen // Chrome, safari
      || element.mozRequestFullScreen // FireFox
      || element.msRequestFullscreen; // IE11
    if (requestMethod) {
      requestMethod.call(element);
    }
  }

  /**
   * 关闭全屏
   */
  // eslint-disable-next-line class-methods-use-this
  exitFullScreen() {
    const exitMethod = document.exitFullscreen // W3C
      || document.mozCancelFullScreen // FireFox
      || document.webkitExitFullscreen // Chrome等
      || document.msExitFullscreen; // IE11
    if (exitMethod) {
      exitMethod.call(document);
    }
  }

  /**
   * 全屏监控
   */
  addFullScreenListener() {
    const self = this;
    document.onkeydown = (e) => {
      if (e && e.keyCode === 122) { // 捕捉F11键盘动作
        e.preventDefault(); // 阻止F11默认动作
        self.toggleFullScreen();
      }
    };
    // 监听不同浏览器的全屏事件，并件执行相应的代码
    switch (self.browserKernel) {
      case 'webkit':
        document.onwebkitfullscreenchange = () => {
          if (document.webkitIsFullScreen) {
            self.isFullScreen = true;
          } else {
            self.isFullScreen = false;
          }
        };
        break;
      case 'gecko':
        document.onmozfullscreenchange = () => {
          if (document.mozFullScreen) {
            self.isFullScreen = true;
          } else {
            self.isFullScreen = false;
          }
        };
        break;
      case 'trident':
        document.onmsfullscreenchange = () => {
          if (document.msFullscreenElement) {
            self.isFullScreen = true;
          } else {
            self.isFullScreen = false;
          }
        };
        break;
      case 'others':
        document.onfullscreenchange = () => {
          if (document.fullscreen) {
            self.isFullScreen = true;
          } else {
            self.isFullScreen = false;
          }
        };
        break;
      default:
        break;
    }
  }

  /**
   * 全屏初始化
   */
  fullInit() {
    this.canFullScreen = document.fullscreenEnabled
      || document.webkitFullscreenEnabled
      || document.mozFullScreenEnabled
      || document.msFullscreenEnabled;
    if (document.webkitFullscreenEnabled) {
      this.browserKernel = 'webkit';
    } else if (document.mozFullScreenEnabled) {
      this.browserKernel = 'gecko';
    } else if (document.msFullscreenEnabled) {
      this.browserKernel = 'trident';
    } else if (document.fullscreenEnabled) {
      this.browserKernel = 'others';
    }
    if (this.canFullScreen) {
      this.addFullScreenListener();
    }
  }
}
