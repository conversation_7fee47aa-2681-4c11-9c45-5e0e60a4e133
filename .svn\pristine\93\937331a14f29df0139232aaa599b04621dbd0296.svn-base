<template>
  <section v-loading="homeLoading" class="zhxy-home-page">
    <div class="home-layout">
      <div class="home-layout-left">
        <dragList
          :component-list="componentListLeft"
          :drag-options="dragOptionLeft"
          :drag-map="dragMap"
          :dragging.sync="dragging"
          @saveConfig="saveConfig"
          @hideBlock="(name)=>hideBlock('componentListLeft',name)"
          @addBlock="()=>addBlock('componentListLeft')"
          @revertBlock="()=>revertBlock('componentListLeft')"
        >
          <template v-slot:content="slotProps">
            <component :is="slotProps.contentName"/>
          </template>
        </dragList>
      </div>
      <div class="home-layout-right">
        <dragList
          :dragging.sync="dragging"
          :component-list="componentListRight"
          :drag-options="dragOptionRight"
          :drag-map="dragMap"
          @saveConfig="saveConfig"
          @hideBlock="(name)=>hideBlock('componentListRight',name)"
          @addBlock="()=>addBlock('componentListRight')"
          @revertBlock="()=>revertBlock('componentListRight')"
        >
          <template v-slot:content="slotProps">
            <component :is="slotProps.contentName"/>
          </template>
        </dragList>
      </div>
    </div>

    <el-dialog
      title="添加区块"
      :visible.sync="addBlockVisible"
      width="80%"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <div>
        <div  class="zhxy-form block-add-content">
           <span class="zhxy-form-label select-label">
            区块内容
          </span>
          <el-select @change="selectChange" class="select-content" size="small" v-model="blockSelectValue" placeholder="请选择">
            <el-option
              v-for="item in selectOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="zhxy-form block-add-content">
          <span class="zhxy-form-label select-label">
            区块所在区域
          </span>
          <el-select class="select-content" size="small" v-model="blockPositionValue" placeholder="请选择">
            <el-option
              v-for="item in positionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>

      </div>
      <span slot="footer" class="dialog-footer">
    <el-button size="small" @click="addBlockVisible = false">取 消</el-button>
    <el-button size="small" type="primary" @click="certainBlock">确 定</el-button>
  </span>
    </el-dialog>
  </section>
</template>

<script>
import DragList from '@/app/layout_03_01/pages/home/<USER>/DragList';
import DataPart from './homeComponents/DataPart';
import HomeTodoCard from './homeComponents/TodoCard';
import HomeQuickAction from './homeComponents/QuickAction';

export default {
  name: 'home',
  components: {
    DragList,
    DataPart,
    HomeTodoCard,
    HomeQuickAction
  },
  data() {
    return {
      // homeLoading
      homeLoading: false,
      // addBlockVisible
      addBlockVisible: false,
      // 区块添加 select value
      blockSelectValue: '',
      // 区块 select options
      selectOptions: [],
      // 添加区块位置value
      blockPositionValue: '',
      // 添加区块 select选择 options
      positionOptions: [],
      // drag组件 拖动时候显示的title
      dragMap: [
        {
          name: 'DataPart',
          title: '基础数据',
          position: 'left'
        },
        {
          name: 'HomeTodoCard',
          title: '办理事项',
          position: 'both'
        },
        {
          name: 'HomeQuickAction',
          title: '快捷操作',
          position: 'both'
        }

      ],
      // 当前是否在执行拖拽
      dragging: false,
      // 左侧drag列表
      componentListLeft: [],
      // 右侧drag列表
      componentListRight: []
    };
  },
  computed: {
    dragOptionLeft() {
      return {
        animation: 30,
        handle: '.drag-handle',
        group: {
          name: 'menu',
          put: true
        },
        ghostClass: 'ghost',
        chosenClass: 'sortable',
        forceFallback: true,
        fallbackClass: 'draggingClass'
      };
    },
    dragOptionRight() {
      return {
        animation: 50,
        handle: '.drag-handle',
        group: {
          name: 'menu',
          put: true
        },
        ghostClass: 'ghost',
        chosenClass: 'sortable',
        forceFallback: true,
        fallbackClass: 'draggingClass'
      };
    }
  },
  created() {
    // TODO mock 加载 首页配置
    this.homeLoading = true;
    setTimeout(() => {
      this.homeLoading = false;
      const result = {
        left: [
          {
            name: 'DataPart'
          },
          {
            name: 'HomeTodoCard'
          },
          {
            name: 'HomeQuickAction'
          }
        ],
        right: []
      };
      this.componentListLeft = result.left || [];
      this.componentListRight = result.right || [];
    }, 1000);
  },
  mounted() {
  },

  methods: {
    /**
     * 隐藏drag 模块
     * @param type
     * @param name
     */
    hideBlock(type, name) {
      const index = this[type].findIndex((x) => x.name === name);
      if (index > -1) {
        this[type].splice(index, 1);
        this.saveConfig();
      }
    },
    /**
     * 添加区块
     * @param type
     */
    addBlock(type) {
      // 重置 弹窗
      this.blockSelectValue = '';
      this.selectOptions = [];
      this.blockPositionValue = '';
      this.positionOptions = [];
      // 显示添加区块弹窗
      this.addBlockVisible = true;
      const currentList = [...this.componentListLeft, ...this.componentListRight];
      const otherList = [];
      this.dragMap.forEach((item) => {
        const isNeed = currentList.some((x) => x.name === item.name);
        if (!isNeed) {
          const ite = {
            value: item.name,
            label: item.title
          };
          otherList.push(ite);
        }
      });
      this.selectOptions = otherList;
    },
    /**
     * 区块变更
     * @param val
     */
    selectChange(val) {
      this.blockPositionValue = '';
      const index = this.dragMap.findIndex((x) => x.name === val);
      if (index > -1) {
        const pos = this.dragMap[index].position;
        const options = [
          {
            value: 'componentListLeft',
            label: '左侧'
          },
          {
            value: 'componentListRight',
            label: '右侧'
          }
        ];
        const list = {
          both: ['componentListLeft', 'componentListRight'],
          left: ['componentListLeft'],
          right: ['componentListRight']
        };
        const needList = list[pos];
        const arr = options.filter((item) => needList.some((x) => x === item.value));

        this.positionOptions = arr;
      }
    },
    /**
     * 区块确定
     */
    certainBlock() {
      if (!this.blockPositionValue || !this.blockSelectValue) {
        this.$message.error('请选择区块及所在区域');
        return;
      }
      this[this.blockPositionValue].push({ name: this.blockSelectValue });

      this.addBlockVisible = false;
      this.saveConfig();
    },
    /**
     * 重置默认配置
     */
    revertBlock() {
      this.saveConfig();
    },
    /**
     * 配置保存
     */
    saveConfig() {
      const params = {
        left: this.componentListLeft || [],
        right: this.componentListRight || []
      };
      console.log('保存配置', params);
    }
  }
};
</script>
<style lang="scss" scoped>
.zhxy-home-page {
  .home-layout {
    display: flex;
    justify-content: space-between;

    &-left {
      width: 75%;
    }

    &-right {
      width: 20%;
    }
  }

  .header {
    width: 100%;
    display: flex;
  }

  .content {
    display: flex;
    padding-top: 20px;
    height: calc(100% - 110px);

    &-left {
      padding: 0 20px 20px;
      width: 60%;
      background-color: #FFFFFF;
      height: 100%;
    }

    &-right {
      width: 40%;
      padding-left: 20px;
    }
  }
}
.block-add-content{
  .select-label{
    width: 150px;
    text-align: right;
    margin-right: 10px;
    display: inline-block;
  }
  .select-content{
    width: 300px;
  }
}
</style>
