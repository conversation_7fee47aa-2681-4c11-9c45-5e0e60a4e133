<template>
  <section class="dybzx-page">
    <v-title name="应用管理"></v-title>
    <div class="zhxy-form zhxy-form-search-part" ref="element">
      <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
        <el-form-item>
          <span class="zhxy-form-label" slot="label">应用名称</span>
          <el-input class="zhxy-form-inline" v-model="listQuery.systemName" size="small"></el-input>
          <el-button type="primary" @click="search()" size="small">查询
          </el-button>
          <el-button type="" @click="reset()" size="small">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-loading="contentLoading" class="dybzx-page-content">
      <div class="button-list">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="showDialog">新增</el-button>
      </div>
      <template v-for="item in result">
        <div class="dybzx-page-content-box" :key="item.yyid">
          <div class="dybzx-page-content-card">
            <div class="card-top">
              <div class="card-tip">{{ item.flmc }}</div>
              <i class="card-top-icon"></i>
              <p class="card-top-title">{{ item.yymc }}</p>
            </div>
            <div class="card-middle">
              <div class="card-middle-item">
                <div class="card-middle-item-label">应用ID:</div>
                <div :title="item.yyid" class="card-middle-item-value">{{ item.yyid }}
                </div>
              </div>
              <div class="card-middle-item">
                <div class="card-middle-item-label">授权方式:</div>
                <div :title="item.sqfs" class="card-middle-item-value">{{ item.sqfs }}</div>
              </div>
            </div>
            <div class="card-bottom">
              <div class="card-bottom-left card-bottom-btn" @click="edit(item)">
                <i class="el-icon-edit"></i>
                <p>编辑</p>
              </div>
              <div class="card-bottom-right card-bottom-btn" @click="delForm(item)">
                <i class="el-icon-delete"></i>
                <p>删除</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!--新增修改弹窗-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="所属分类" :label-width="formLabelWidth" prop="flid">
          <el-select style="width: 100%;" v-model="form.flid" placeholder="请选择" size="small">
            <el-option v-for="item in options" :key="item.dmz" :label="item.dmmc" :value="item.dmz"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="系统名称" :label-width="formLabelWidth" prop="yymc">
          <el-input size="small" v-model="form.yymc" placeholder="必填"></el-input>
        </el-form-item>
        <el-form-item label="系统ID" :label-width="formLabelWidth" prop="yyid">
          <el-input size="small" v-model="form.yyid" placeholder="必填"></el-input>
          <!--          <el-input size="small" v-model="form.BMMC"  autocomplete="off"></el-input>-->
        </el-form-item>
        <el-form-item label="秘钥" :label-width="formLabelWidth" prop="yymy">
          <el-input  size="small" v-model="form.yymy" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="认证方式" :label-width="formLabelWidth" prop="checkList">
          <el-checkbox-group v-model="form.checkList" prop="checkList">
            <el-checkbox label="authorization_code">授权码</el-checkbox>
            <el-checkbox label="password">账号密码</el-checkbox>
          </el-checkbox-group>
          <!--          <el-input size="small" v-model="form.sqfs"  autocomplete="off"></el-input>-->
        </el-form-item>
        <el-form-item v-if="form.checkList.includes('authorization_code')" label="回调地址:" :label-width="formLabelWidth" prop="hddz">
          <el-input size="small" v-model="form.hddz" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
        <el-button :loading="formLoading" size="small" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import { cloneDeep } from 'lodash';
import VTitle from '@/components/title/VTitle.vue';
import { deletePrompt } from '@/utils/action-utils.js';
import { v4 as uuidv4 } from 'uuid';
import {
  getYyList, deleteYyList, addYyList, getYyDetailOptions
} from '@/app/tysfrz_01_01/api/yygl';

export default {
  components: {
    VTitle
  },
  data() {
    const validatetitle = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入系统ID'));
      }
      return callback();
    };
    return {
      // 新增dialog loading
      formLoading: false,
      // dialog title
      dialogTitle: '新增',
      // 列表内容loading
      contentLoading: false,
      dialogFormVisible: false,
      form: {
        yyid: '',
        yymc: '',
        flid: '',
        hddz: '',
        yymy: '',
        sqfs: '',
        checkList: []
      },
      options: [],
      rules: {
        yyid: [
          {
            required: true,
            trigger: 'blur',
            validator: validatetitle
          },
          {
            max: 200,
            message: '最多输入200个字符',
            trigger: 'blur'
          }
        ],
        yymc: [
          {
            required: true,
            message: '请填写系统名称',
            trigger: 'blur'
          }
        ],
        flid: [
          {
            required: true,
            message: '请填写所属分类',
            trigger: 'change'
          }
        ],
        hddz: [
          {
            required: true,
            message: '请填回调地址',
            trigger: 'blur'
          }
        ]
      },
      formLabelWidth: '120px',
      // label 宽度
      lableWidth: '120px',
      // search params
      listQuery: {
        systemName: ''
      },
      // mock 请求数据
      result: []
    };
  },
  created() {
    this.getYyDetailOptionsFn();
    this.getYyCardList();
  },
  mounted() {

  },
  methods: {
    // 获取应用分类数据
    getYyDetailOptionsFn() {
      getYyDetailOptions().then((res) => {
        this.options = res.data.content || [];
      }).finally(() => {
      });
    },
    // 获取应用列表数据
    getYyCardList() {
      this.contentLoading = true;
      const params = {
        yymc: this.listQuery.systemName
      };
      // 获取应用列表数据接口
      getYyList(params).then((res) => {
        this.result = res.data.content || [];
      }).finally(() => {
        this.contentLoading = false;
      });
    },
    /**
     * 搜索查询
     */
    search() {
      this.getYyCardList();
    },
    /**
     * 重置搜索条件
     */
    reset() {
      this.listQuery.systemName = '';
      this.getYyCardList();
    },
    // 删除数据
    delForm(item) {
      // 设置参数
      const config = {
        text: '是否删除?',
        btnText: '执行中...',
        title: '',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        // 删除应用列表数据接口
        const params = {
          yyid: item.yyid
        };
        deleteYyList(params).then(() => {
          const index = this.result.findIndex((x) => x.id === item.id);
          if (index > -1) {
            this.result.splice(index, 1);
            this.$message.success('删除成功');
          }
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    /**
     * 显示新增弹窗
     */
    showDialog() {
      this.dialogTitle = '新增';
      // 展示弹窗
      this.dialogFormVisible = true;
      const uuid = uuidv4();
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.yymy = uuid;
      });
    },
    /**
     * 新增确定按钮
     */
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = cloneDeep(this.form);
          params.sqfs = params.checkList.join(',');
          delete params.checkList;
          if (!params.sqfs.includes('authorization_code')) {
            params.hddz = '';
          }
          this.formLoading = true;
          addYyList(params).then(() => {
            this.$message.success('数据添加成功');
            this.getYyCardList();
            this.dialogFormVisible = false;
          }).finally(() => {
            this.formLoading = false;
          });
          // 调用接口 传递数据
          return true;
        }
        return false;
      });
    },
    /**
     * 编辑数据
     * @param val
     */
    edit(val) {
      this.$router.push({ path: '/tysfrz_01_01/Yyxq', query: { id: val.yyid } });
    }
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page {
  padding: $page-content-padding;
  background-color: #FFFFFF;

  .dybzx-page-content {
    min-height: 300px;
    display: flex;
    flex-wrap: wrap;

    .button-list {
      width: 100%;
      display: flex;
      flex-direction: row-reverse;
      padding-right: 2%;
      height: 32px;
    }

    .dybzx-page-content-box {
      width: 25%;
      padding: 0 2%;
    }

    .dybzx-page-content-card {
      background-color: #FFFFFF;
      width: 100%;
      height: 228px;
      border: 1px solid #EAEAEA;
      box-shadow: #ededed 0px 0px 5px 1px;
      position: relative;
      overflow: hidden;
      margin-top: 20px;

      .card-tip {
        position: absolute;
        top: 10px;
        right: -30px;
        width: 120px;
        background-color: #286fb7;
        color: #FFFFFF;
        font-size: 16px;
        padding: 7px 0;
        text-align: center;
        transform: rotate(45deg);
      }

      .card-top {
        display: flex;
        align-items: center;
        background-color: #F2F2F2;
        height: 52px;
        padding: 0 10px;

        .card-top-icon {
          width: 26px;
          height: 26px;
          background-size: cover;
        }

        .card-top-title {
          font-size: 16px;
          color: #2d8cf0;
          padding-left: 10px;
        }

      }

      .card-middle {
        padding: 20px 30px;

        .card-middle-item {
          display: flex;
          line-height: 30px;

          .card-middle-item-label {
            width: 80px;
            height: 45px;
            flex-shrink: 0;
            color: #90a4af;
          }

          .card-middle-item-value {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

      }

      .card-bottom {
        display: flex;
        height: 45px;

        .card-bottom-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 50%;
          border-top: 1px solid #EAEAEA;
          cursor: pointer;
        }

        .card-bottom-btn:hover {
          opacity: 0.8;
        }

        .card-bottom-left {
          border-right: 1px solid #EAEAEA;
          color: #286fb7;
        }

        .card-bottom-right {
          color: #90a4af;
        }
      }
    }
  }
}
</style>
