<template>
  <div class="xtcs-content">
    <div class="xtcs-content-main">
      <!--      标题-->
      <v-title name="系统参数"></v-title>
      <xtcs-search ref="searchElement" @search="search"></xtcs-search>
      <!--      table 表单部分 -->
      <!--      按钮-->
      <div class="button-list">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="addYh()">新增</el-button>
        <el-button type="danger" icon="el-icon-delete-solid" size="small" @click="plscYh()">批量删除
        </el-button>
      </div>
      <!--      table-->
      <div class="table">
        <div class="table-content">
          <xtcs-table :scroller-height="scrollerHeight" :table-data="tableData" @handleSelectionChange="handleSelectionChange" @delYh="delYh" @modifyYh="modifyYh"></xtcs-table>
        </div>
        <!--        table pageInation-->
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>

    <!--新增修改弹窗-->
    <el-dialog
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="addDialogVisible"
      :title="dialogType === 'edit'?'修改系统参数':'新增系统参数'"
      :close-on-click-modal="false" @close="closeYh('yhForm')">
      <xtcs-edit ref="dialogEditContent" :yhid-visible="yhidVisible" :FormData="yh"></xtcs-edit>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="confirm('yhForm')">确定</el-button>
        <el-button type="" size="small" @click="closeYh('yhForm')">取消</el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import {
  findListByPage, add, update, del, deleteBatchXtcs, findOneByID
} from '@/app/xtgl_03_01/api/xtcsgl/xtcsgl.js';
import XtcsSearch from './xtcsComponents/InfoSearch';
import XtcsTable from './xtcsComponents/InfoTable';
import XtcsEdit from './xtcsComponents/InfoEdit';

const defaultYh = {
  csbs: '',
  csbssm: '',
  csz: '',
  bz: ''
};

export default {
  name: 'xtgl_02_01-jcggXtcs',
  components: {
    XtcsSearch,
    XtcsTable,
    VTitle,
    XtcsEdit
  },
  data() {
    return {
      yh: { ...defaultYh },
      scrollerHeight: 0,
      currentPage: 1, // 初始页
      tableData: [], // 列表数据集合
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      yhSelection: [], // table多选框数据
      yhidList: [],
      selections: '',
      addDialogVisible: false,
      addYhbmDialogVisible: false,
      yhidVisible: true,
      dialogType: 'new',
      yhbmOptions: []
    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /**
       * 监听页面尺寸改变table size
       */
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight*0.8 - height - 265;
    },
    /**
       * search 搜索事件
       * @param params
       * @returns {Promise<void>}
       */
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        csbs: null,
        csbssm: null,
        csz: null,
        bz: null,
        cjr: null,
        bgr: null,
        cjsjkssj: null,
        cjsjjssj: null,
        bgsjkssj: null,
        bgsjjssj: null
      };
      if (params) {
        Object.assign(param, params);
      }

      findListByPage(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    /**
       * 查询条件重置
       */
    reset() {
      this.listQuery.csbs = null;
      this.listQuery.csbssm = null;
      this.listQuery.csz = null;
      this.listQuery.bz = null;
      this.listQuery.cjr = null;
      this.listQuery.bgr = null;
      this.search();
    },
    /**
       * 每页显示条数改变事件
       * @param val
       */
    handleSizeChange(val) {
      // this.pageSize = val;
      // this.search();// 根据用户获取的每页显示页面数量显示页面
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        csbs: this.$refs.searchElement.listQuery.csbs,
        csbssm: this.$refs.searchElement.listQuery.csbssm,
        csz: this.$refs.searchElement.listQuery.csz,
        bz: this.$refs.searchElement.listQuery.bz,
        cjr: this.$refs.searchElement.listQuery.cjr,
        bgr: this.$refs.searchElement.listQuery.bgr,
        cjsjkssj: this.$refs.searchElement.listQuery.cjsjkssj,
        cjsjjssj: this.$refs.searchElement.listQuery.cjsjjssj,
        bgsjkssj: this.$refs.searchElement.listQuery.bgsjkssj,
        bgsjjssj: this.$refs.searchElement.listQuery.bgsjjssj
      };
      this.search(param);
    },
    /**
       * 当前页数改变事件
       * @param val
       */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage, // 页码
        csbs: this.$refs.searchElement.listQuery.csbs,
        csbssm: this.$refs.searchElement.listQuery.csbssm,
        csz: this.$refs.searchElement.listQuery.csz,
        bz: this.$refs.searchElement.listQuery.bz,
        cjr: this.$refs.searchElement.listQuery.cjr,
        bgr: this.$refs.searchElement.listQuery.bgr,
        cjsjkssj: this.$refs.searchElement.listQuery.cjsjkssj,
        cjsjjssj: this.$refs.searchElement.listQuery.cjsjjssj,
        bgsjkssj: this.$refs.searchElement.listQuery.bgsjkssj,
        bgsjjssj: this.$refs.searchElement.listQuery.bgsjjssj
      };
      this.search(param);
    },
    /**
       * 多选框选中赋值
       * @param val
       */
    handleSelectionChange(val) {
      this.yhSelection = val;
    },
    /**
       * 新增系统参数弹窗事件
       */
    addYh() {
      this.yh = { ...defaultYh };
      this.dialogType = 'new';
      this.yhidVisible = true;
      this.addDialogVisible = true;

      // this.$nextTick(() => {
      //   this.$refs.dialogEditContent.rules.mm[0].required = true;
      // });
    },
    /**
       * 关闭用户窗口时处理
       * @param formName
       */
    closeYh(formName) {
      this.$refs.dialogEditContent.$refs[formName].clearValidate();
      this.addDialogVisible = false;
    },
    /**
       * 删除
       * @param row
       */
    delYh(row) {
      const id = row.csbs || ''; // 参数标识
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            del({ csbs: id }).then((res) => {
              if (res.code === 200) {
                const index = this.tableData.findIndex((item) => item.csbs === id);
                if (index > -1) {
                  this.tableData.splice(index, 1);
                }
                this.$message.success('删除成功');
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
       * 修改用户信息
       * @param row
       */
    modifyYh(row) {
      this.addDialogVisible = true;
      this.dialogType = 'edit';
      this.yhidVisible = false;
      Object.assign(this.yh, row);
    },
    /**
       * 新增/修改
       * @param formName
       */
    confirm(formName) {
      if (this.dialogType !== 'edit') {
        const param = {
          csbs: this.yh.csbs
        };
        findOneByID(param).then((res) => {
          if (Object.keys(res.data).length === 0) {
            add(this.yh).then((result) => {
              this.search();
              this.closeYh(formName);
            });
          } else {
            this.$message.error('该参数标识已存在！');
          }
        });
      } else {
        update(this.yh).then((result) => {
          this.search();
          this.closeYh(formName);
        });
      }
    },
    /**
       * 批量删除
       */
    plscYh() {
      console.log(this.yhSelection);
      if (this.yhSelection.length === 0) {
        this.$message.error('请勾选系统参数');
      } else {
        const arr = [];
        this.yhSelection.forEach((item) => {
          const itemparam = {
            csbs: item.csbs
          };
          arr.push(itemparam);
        });
        /**
         * 批量删除 接口
         */
        const param = {
          ids: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },
    /**
     * 批量删除 角色下用户 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      deleteBatchXtcs(param).then((res) => {
        this.$message.success('删除成功');
        this.search();
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    }
  }
};
</script>
<style lang="scss">
</style>
<style lang="scss" scoped>
  .xtcs-content {

    &-main{
      background-color: #ffffff;
      padding: $page-content-padding;
    }

    .table {
      &-content {
        margin-top: $page-content-margin;
      }

    }
    .dialog-footer{
      text-align: center;
    }
  }
</style>
