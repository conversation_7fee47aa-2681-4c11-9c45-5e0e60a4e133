<template>
  <div>
    <div class="dybzx-page-content">
      <el-table class="zhxy-table" :data="tableData" style="width: 100%" border>
        <el-table-column prop="ip" label="IP地址" width="500"></el-table-column>
        <el-table-column label="状态" width="300">
          <template slot-scope="scope">
            {{ scope.row.zt === 0 ? '停用' : '启用' }}
          </template>
        </el-table-column>
        <el-table-column prop="cjsj" label="创建时间" width="300">
          <template slot-scope="scope">
            {{dayjs(scope.row.cjsj).format('YYYY-MM-DD HH:mm:ss')}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="400">
          <template slot-scope="scope">
            <el-button @click="statusChange(scope.row)" type="text" size="small">
              {{ scope.row.zt === 1 ? '停用' : '启用' }}
            </el-button>
            <el-button @click.native.prevent="deleteRow(scope.row)" type="text" size="small">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { deletePrompt } from '@/utils/action-utils.js';
import dayjs from 'dayjs';

export default {
  name: 'YyxqIplb',
  props: {
    tableData: {
      type: Array,
      default: () => ([])
    },
    label: {
      type: Array,
      default: () => ([])
    }
  },
  methods: {
    dayjs,
    // 删除 ip
    deleteRow(row) {
      const params = {
        ...row
      };
      this.$emit('deleteIP', params);
    },
    /**
     * 状态改变
     * @param params
     */
    statusChange(params) {
      this.$emit('changeIPStatus', params);
    }
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    margin-top: 20px;
    padding: 0 10px;
    box-sizing: border-box;
  }
}
</style>
