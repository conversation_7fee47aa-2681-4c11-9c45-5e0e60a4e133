<template>
  <div ref="myChart" style="width: 100%;height: 100%;box-sizing: border-box;"></div>
</template>

<script>
// 引入基本模板
import { echarsMixins } from '@/app/ywxtgl_01_01/mixins/echars-resize';

const echarts = require('echarts/lib/echarts');

// 引入饼状图组件
require('echarts/lib/chart/pie');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');

export default {
  mixins: [echarsMixins],
  props: {
    data: {
      type: Array
    },
    colorlist: {
      type: Array,
      default() {
        return ['#49c7ad', '#ffa55b', '#4a90e2', '#CB5555'];
      }
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    data(newValue, oldValue) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);
      this.echarsDom = myChart;

      const bgColor = '#fff';
      const title = '总量';
      const color = ['#0E7CE2', '#FF8352', '#E271DE', '#F8456B', '#00FFFF', '#4AEAB0'];
      const echartData = [{
        name: '元数据类',
        value: '720'
      },
      {
        name: '信息资源类',
        value: '2920'
      },
      {
        name: '数据质量类',
        value: '2200'
      },
      {
        name: '其他类型',
        value: '1420'
      }
      ];

      const formatNumber = function (num) {
        const reg = /(?=(\B)(\d{3})+$)/g;
        return num.toString().replace(reg, ',');
      };
      const total = echartData.reduce((a, b) => a + b.value * 1, 0);

      const option = {
        color,
        series: [{
          type: 'pie',
          radius: ['45%', '60%'],
          center: ['50%', '50%'],
          data: echartData,
          hoverAnimation: false,
          itemStyle: {
            normal: {
              borderColor: bgColor,
              borderWidth: 2
            }
          },
          /* labelLine: {
               normal: {
                   length: 20,
                   length2: 20,
                   lineStyle: {
                       color: '#e6e6e6'
                   }
               }
           }, */
          label: {
            normal: {
              formatter: (params) => (
                `{icon|●}{name|${params.name}}{value|${
                  formatNumber(params.value)}}`
              ),
              rich: {
                icon: {
                  fontSize: 12
                },
                name: {
                  fontSize: 12,
                  padding: [0, 10, 0, 4],
                  color: '#666666'
                },
                value: {
                  fontSize: 12,
                  fontWeight: 'bold',
                  color: '#333333'
                }
              }
            }
          }
        }]
      };

      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
