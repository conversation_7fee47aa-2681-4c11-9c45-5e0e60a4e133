// echars resize
export const echarsMixins = {
  data() {
    return {
      echarsDom: null
    };
  },
  mounted() {
    // 注：此处要用addEventListener,如果用 window.onresize = function(){},如果别的组件也用了onresize事件，就容易覆盖掉此处的函数
    window.addEventListener('resize', this.chartResize);
  },
  destroyed() {
    window.removeEventListener('resize', this.chartResize);
  },
  methods: {
    /**
     * echars resize
     * @param eleArr
     */
    chartResize() {
      if (this.echarsDom) {
        this.echarsDom.resize();
      }
    }
  }
};
