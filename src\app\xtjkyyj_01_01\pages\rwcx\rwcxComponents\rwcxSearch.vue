<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">

<!--      <el-form-item>-->
<!--        <span class="zhxy-form-label" slot="label">任务类别</span>-->
<!--        <el-select class="zhxy-form-inline" v-model="listQuery.rwlb" placeholder="任务类别" size="small">-->
<!--          <el-option-->
<!--            v-for="item in rwlbOptions"-->
<!--            :key="item.value"-->
<!--            :label="item.label"-->
<!--            :value="item.value">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->

      <el-form-item>
        <span class="zhxy-form-label" slot="label">程序名称</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.cxmc" placeholder="程序名称" size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">程序状态</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.cxzt" placeholder="程序状态" size="small">
          <el-option
            v-for="item in cxztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

</template>

<script>
export default {
  name: 'CjrwgzSearch',
  data() {
    return {
      // label 宽度
      lableWidth: '100px',
      // search params
      listQuery: {
        rwlb: null,
        cxmc: null,
        cxzt: null
      },
      rwlbOptions: [{
        label: '全部',
        value: ''
      }, {
        label: '平台类',
        value: 0
      }, {
        label: '数据类',
        value: 1
      }],
      cxzt: this.$route.query.cxzt,
      cxztOptions: [{
        label: '全部',
        value: ''
      }, {
        label: '正常',
        value: 1
      }, {
        label: '异常',
        value: 2
      }]

    };
  },
  mounted() {
    this.listQuery.rwlb = '';
    this.listQuery.cxzt = '';

    if (this.cxzt === '1') {
      this.listQuery.cxzt = '1';
      this.search();
    } else if (this.cxzt === '2') {
      this.listQuery.cxzt = '2';
      this.search();
    } else {
      this.listQuery.cxzt = '';
      this.search();
    }
  },
  methods: {
    /**
       * 搜索查询
       */
    search() {
      const param = {
        rwlb: this.listQuery.rwlb,
        cxmc: this.listQuery.cxmc,
        cxzt: this.listQuery.cxzt
      };
      this.$emit('search', param);
    },
    /**
       * 重置搜索条件
       */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });
      this.search();
    }
  }
};
</script>

<style lang="scss" scoped>
  .title {
    display: flex;
    justify-content: space-between;
  }

  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
</style>
