<template>
  <div class="file-upload">
    <div class="file-upload-single">
      <p>
        文件上传single
      </p>

      <el-upload
        class="upload-demo"
        action="/"
        multiple
        :auto-upload="false"
        :on-change="handleChange"
        :show-file-list="false"
      >
        <el-button size="small" type="primary">选择文件</el-button>
      </el-upload>
      <!--    上传列表及进度-->
      <div class="file-upload-list">
        <div class="file-upload-list-item" v-for="item in fileList" :key="item.uid">
          <div class="file-upload-list-item-label">
            <i class="upload-icon el-icon-document" :class="item.type"></i>
            <div :title="item.name" class="upload-name">{{ item.name }}</div>
          </div>
          <div class="file-upload-list-item-value">
            <el-progress style="width: 100%" type="line" :percentage="item.percentage" class="progress"
                         :show-text="true"></el-progress>
            <div class="file-upload-list-item-value-btn">
              <el-button
                v-if="item.percentage !== 100"
                icon="el-icon-upload" size="small"
                type="primary"
                :loading="item.percentage>0 && item.percentage<100"
                @click="fileUploadAction(item)">
                上传
              </el-button>
              <el-button
                v-else size="small"
                icon="el-icon-delete"
                type="danger"
                @click="uploadfileDelete(item)">
                删除
              </el-button>
              <el-button
                v-if="item.percentage !== 100"
                size="small"
                icon="el-icon-delete"
                type="warning"
                @click="unUploadfileDelete(item)">
                取消
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5';
import { minioUpload, minioDelete } from '@/app/demo_03_01/api/demo/demo.js';
import axios from 'axios';
import { deletePrompt } from '@/utils/action-utils.js';
import { cloneDeep } from 'lodash';

const CANCEL_TOKEN = axios.CancelToken;

export default {
  name: 'FileUpload',
  data() {
    return {
      fileList: [] // 上传列表
    };
  },
  methods: {
    /**
     * 获取文件类型及icon
     * @param suffix
     * @returns {null}
     */
    getFileType(suffix) {
      const pdfList = {
        name: 'pdf',
        type: ['pdf']
      };
      const imgList = {
        name: 'img',
        type: ['png', 'jpg', 'jpeg', 'bmp', 'gif']
      };
      const pptList = {
        name: 'ppt',
        type: ['ppt', 'pptx']
      };
      const wordList = {
        name: 'word',
        type: ['doc', 'docx']
      };
      const excelList = {
        name: 'excel',
        type: ['xls', 'xlsx']
      };
      const fileList = [pdfList, imgList, pptList, wordList, excelList];
      let type = null;
      for (let i = 0; i < fileList.length; i++) {
        const item = fileList[i];
        if (item.type.some((x) => x === suffix)) {
          type = item.name;
          break;
        }
      }
      return type;
    },
    /**
     * 文件上传 添加到文件列表中
     * @param file
     * @param fileList
     */
    async handleChange(file, fileList) {
      const fileCurrent = file;
      const suffix = /\.([0-9a-zA-Z]+)$/i.exec(file.name)[1]; // 文件后缀
      fileCurrent.type = this.getFileType(suffix);
      // 解析上传文件
      const buffer = await this.fileParse(file.raw, 'buffer');
      // 创建上传文件的切片大小
      const chunkSize = 1024 * 600;
      const {
        partList,
        hash
      } = this.createFileChunk(chunkSize, buffer, fileCurrent);
      fileCurrent.partList = partList;
      fileCurrent.hash = hash;
      fileCurrent.split = partList.length > 1;
      if (!fileCurrent.split) {
        fileCurrent.partList[0] = fileCurrent.raw;
      }
      this.fileList.push(fileCurrent);
      console.log(fileList);
    },
    /**
     * 待上传文件删除
     * @param file
     */
    unUploadfileDelete(file) {
      const index = this.fileList.findIndex((x) => x.uid === file.uid);
      if (index > -1) {
        this.fileList.splice(index, 1);
      }
      file.partList.forEach((item) => {
        if (item.cancel) {
          item.cancel();
        }
      });
    },
    /**
     * 已上传文件删除
     * @param file
     */
    uploadfileDelete(file) {
      // 删除接口
      const config = {
        text: '是否删除',
        btnText: '执行中...'
      };
      const deleteItem = (successAction) => {
        setTimeout(() => {
          this.unUploadfileDelete(file);
          successAction();
        }, 2000);
      };
      deletePrompt(config, deleteItem);
    },
    /**
     * 文件上传到服务器
     * @param file
     */
    fileUploadAction(file) {
      const formData = new FormData();
      // for (let i = 0; i < this.fileList.length; i++) {
      //   formData.append('fileUpload', this.fileList[i].raw);
      // }
      const params = cloneDeep(file);
      const config = {
        onUploadProgress: (progressEvent) => {
          const complete = (progressEvent.loaded / progressEvent.total) * 100 || 0;
          file.percentage = complete;
        },
        cancelToken: new CANCEL_TOKEN((c) => { // 强行中断请求要用到的，记录请求信息
          file.partList[0].cancel = c;
        })
      };
      // 不是切片上传
      if (!file.split) {
        formData.append('fileUpload', params.partList[0]);
        minioUpload(formData, config)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success('附件上传成功!');
            }
          })
          .catch((err) => {
            file.percentage = 0;
            this.$message.error(err);
          })
          .finally(() => {
            // this.$message.success('成功后操作');
          });
      } else {
        // 是切片上传的文件
        const {
          lastModified,
          lastModifiedDate,
          name,
          type
        } = params.raw;
        console.log(lastModified, lastModifiedDate, name, type);
        formData.append('fileUpload', params.raw);
        this.sendRequest();
      }
    },
    /**
     * 文件读取解析
     * @param file
     * @param type
     * @returns {Promise<unknown>}
     */
    fileParse(file, type = 'base64') {
      return new Promise((resolve) => {
        const fileRead = new FileReader();
        if (type === 'base64') {
          fileRead.readAsDataURL(file);
        } else if (type === 'buffer') {
          fileRead.readAsArrayBuffer(file);
        }
        fileRead.onload = (ev) => {
          resolve(ev.target.result);
        };
      });
    },
    /**
     * 文件切片
     * @param size
     * @param buffer
     * @param file
     */
    createFileChunk(size = 1024 * 30, buffer, files) {
      const file = files.raw;
      const spark = new SparkMD5.ArrayBuffer();
      let hash = null;
      let suffix = null;
      spark.append(buffer);
      hash = spark.end(); // 文件的hash值
      suffix = /\.([0-9a-zA-Z]+)$/i.exec(file.name)[1]; // 文件后缀

      // 设置上传文件切片大小 size
      const partsize = size;
      const partList = [];
      const partLength = Math.ceil(file.size / partsize);
      let cur = 0;
      for (let i = 0; i < partLength; i++) {
        const item = {
          chunk: file.slice(cur, cur + partsize),
          filename: `${hash}_${i}.${suffix}`,
          percentage: 0,
          index: i,
          total: partLength
        };
        cur += partsize;
        partList.push(item);
      }
      console.log('-----------', partList);
      return {
        partList,
        hash
      };
    },
    /**
     * 文件上传
     * @param config
     * @returns {Promise<void>}
     */
    async changeFile(config) {
      const file = config.file;
      console.log(config);
      if (!file) return;
      // file = file.raw;

      // 解析为BUFFER数据
      const buffer = await this.fileParse(file, 'buffer');
      const {
        partList,
        hash
      } = this.createFileChunk(1024 * 30, buffer, file);
      this.fileLists.push({
        file: partList,
        hash
      });
      console.log('fileLIst:', this.fileLists);
      this.sendRequest({
        file: partList,
        hash
      });
    },
    /**
     * 文件请求list
     * @param params
     * @returns {Promise<void>}
     */
    async sendRequest(params) {
      // 切片个数创建ajax请求
      const partList = params.partList; // 切片file list
      const requestList = []; // init 请求list
      partList.forEach((item, index) => {
        // 每一个函数都是发送一个切片的请求
        const fn = () => {
          const formData = new FormData();
          formData.append('chunk', item.chunk);
          formData.append('filename', item.filename);
          const multipleConfig = {
            headers: { 'Content-Type': 'multipart/form-data' },
            cancelToken: new CANCEL_TOKEN((c) => { // 强行中断请求要用到的，记录请求信息
              item.cancel = c;
            }),
            onUploadProgress: (progressEvent) => {
              const complete = progressEvent.loaded || 0;
              item.percentage = complete;
              let currentPercentage = 0;
              partList.forEach((x) => {
                currentPercentage += x.percentage;
              });
              params.percentage = (currentPercentage / item.total) * 100 || 0;
            }
          };
          return axios
            .post('/single', formData, multipleConfig)
            .then((result) => {
              if (result.code === 200) {
                // 操作
              }
            });
        };
        requestList.push(fn);
      });
      // 传递：并行(ajax.abort())/串行(基于标志控制不发送)
      let i = 0;
      // 切片全部上传成功后 调用合并接口
      const complete = async () => {
        const result = await axios.get('/merge', {
          params: {
            hash: params.hash
          }
        });
        if (result.code === 0) {
          // TODO 上传成功操作
        }
      };
      const send = async () => {
        // 已经中断则不再上传
        if (params.abort) return;
        if (i >= requestList.length) {
          // 上传结束 调用合并接口
          complete();
          return;
        }
        await requestList[i]();
        i++;
        send();
      };
      send();
    }
  }
};
</script>

<style lang="scss" scoped>
.upload-demo {
  display: inline-block;
}

.file-upload {
  &-single {
    background-color: #ffffff;
  }

  .file-upload-list {
    margin-top: $page-content-margin;

    &-item {
      display: flex;
      margin-bottom: 10px;

      &-label {
        display: flex;
        width: 200px;
        flex-shrink: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        align-items: center;
        white-space: nowrap;

        .upload-icon {
          display: inline-block;
          width: 23px;
          height: 28px;
          font-size: 30px;
          margin-right: 10px;
          padding: 0 12px;
          position: relative;
          box-sizing: content-box;
          color: #9c9c9c;
        }

        .upload-name {
          text-overflow: ellipsis;
          flex: 1;
          overflow: hidden;
          font-weight: bold;
        }
      }

      &-value {
        flex: 1;
        display: flex;
        align-items: center;

        &-btn {
          width: 220px;
        }
      }
    }
  }
}

.ppt {
  &:after {
    font-size: 14px;
    content: "PPT";
    display: block;
    font-weight: bold;
    color: #a3a3a3;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-shadow: 1px 1px #9c9c9c;
  }
}

.word {

  &:after {
    font-size: 14px;
    content: "DOC";
    display: block;
    font-weight: bold;
    color: #6f6969;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-shadow: 1px 1px #9c9c9c;
  }
}

.pdf {

  &:after {
    font-size: 14px;
    content: "PDF";
    display: block;
    font-weight: bold;
    color: #6f6969;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-shadow: 1px 1px #9c9c9c;
  }
}

.excel {

  &:after {
    font-size: 14px;
    content: "XLS";
    display: block;
    font-weight: bold;
    color: #6f6969;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-shadow: 1px 1px #9c9c9c;
  }
}

.img {

  &:after {
    font-size: 14px;
    content: "IMAGE";
    display: block;
    font-weight: bold;
    color: #6f6969;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-shadow: 1px 1px #9c9c9c;
  }
}
</style>
