<template>
  <div class="zygl-content">
    <!--新增一级资源-->
    <el-dialog
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="yjzyaddDialogVisible"
      :title="yjzydialogType === 'edit'?'新建子资源':'新建根资源'"
      :close-on-click-modal="false" @close="closeYjzy('yjzyForm')">
      <yjzy-add ref="yjzyEdit" :yhid-visible="yjzyyhidVisible" :FormData="yjzyyh"></yjzy-add>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="confirmYjzy('yjzyForm')">确定</el-button>
        <el-button type="" size="small" @click="closeYjzy('yjzyForm')">取消</el-button>
      </div>
    </el-dialog>
    <right-menu ref="rightMenuPart" :options="rightMenuOptions"></right-menu>
    <div class="title">
      <v-title name="资源管理"></v-title>
    </div>
    <div class="content">
      <div class="zygl-content-left">
        <div class="zygl-content-left-content">
          <div class="tree-top-part">
            <div style="display: flex;margin-bottom: 10px">
              <el-select size="small" style="flex: 1;margin-right: 10px" v-model="sourceType" placeholder="请选择">
                <el-option
                  v-for="item in sourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  @click.native="changeSzhjEvent(item.label)">
                </el-option>
              </el-select>
              <download-excel
                class="export-excel-wrapper"
                :data="treeExport"
                :fields="bmtree_fields"
                name="filename.xls">
                <el-button size="small" type="primary" @click="exportExcel">导出</el-button>
              </download-excel>
            </div>
            <el-input
              placeholder="请输入关键字"
              type="text"
              class="input-search"
              size="small"
              prefix-icon="el-icon-search"
              v-model="treeInput" clearable>
            </el-input>
            <div style="padding-left: 25px">
              <el-button @click="addYjzy()" style="font-weight: bold" size="default" icon="el-icon-plus" type="text">
                新建根资源
              </el-button>
            </div>
          </div>
          <div class="zygl-content-left-tree">
            <el-tree
              ref="treeLeft"
              :props="defaultProps"
              highlight-current
              :data="zytree"
              node-key="gnzyid"
              :filter-node-method="filterNode"
              @node-contextmenu="openMenu"
              @node-click="handleNodeClick">
              <span slot-scope="{ node, data }">
                <i v-if="!data.isSource" :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{data.gnzymc}}
              </span>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="zygl-content-right" ref="right">
        <el-tabs type="card" v-model="activeName" style="height: 100%">
          <el-tab-pane label="资源详情" name="roleDetail" class="content-tab-pane">
            <department-detail ref="detail"></department-detail>
          </el-tab-pane>
          <el-tab-pane label="资源明细" name="roleUser" class="content-tab-pane">
            <div class="button-tab">
              <el-button type="primary" icon="el-icon-plus" size="small" @click="showMxDiolag">新增子资源</el-button>
              <el-button type="danger" icon="el-icon-delete-solid" size="small" @click="()=>{$refs.userDetail.deleteJsyh()}">批量删除</el-button>
            </div>
            <department-sub ref="userDetail" @modifyUser="modifyUser"></department-sub>
          </el-tab-pane>
          <el-tab-pane label="所属角色" name="roleSource" class="content-tab-pane">
            <div class="button-tab">
              <el-button type="danger" icon="el-icon-delete-solid" size="small" @click="()=>{$refs.userDetailJs.deleteJsyh()}">批量删除</el-button>
            </div>
            <DepartmentUser ref="userDetailJs"></DepartmentUser>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
/* import {
  findBmList,
  findBmListByPageFbmm,
  findYhListByPageBmm,
  updateBm,
  deleteBmBatch,
  findYhListByBmm,
  drBmBatch,
  deleteYhBm
} from '../../api/bmgl/bmgl'; */
import { cloneDeep } from 'lodash';
import { dateFilter } from '@/utils/date-utils';
import VTitle from '../../../../components/title/VTitle';
import DepartmentDetail from './zyglComponents/DepartmentDetail';
import DepartmentUser from './zyglComponents/DepartmentUser';
import YjzyAdd from './zyglComponents/YjzyAdd';
import DepartmentSub from './zyglComponents/DepartmentSub';
import RightMenu from '../../components/RightMenu/index';
import {
  findList, add, findOne, del, findListByPage
} from '../../api/zygl/zygl';

const defaultYjzy = {
  gnzyid: '',
  gnzymc: '',
  zymchz: '',
  qqlj: '',
  fcdid: '',
  pxh: '',
  sfsqxxz: '',
  szhj: '',
  dbssxyybs: '',
  dblxbs: '',
  gzlgwtbfs: ''
};
export default {
  components: { // 注册组件
    VTitle,
    DepartmentDetail,
    DepartmentUser,
    DepartmentSub,
    RightMenu,
    YjzyAdd
  },
  data() {
    return {
      // 是否显示父菜单
      sfxsfcd: '',
      // 父菜单id
      Fcdid: '',
      id: '',
      // 文件名称编辑弹窗
      fileEdit: {
        visible: false,
        name: ''
      },
      // 资源详情
      zyxq: {
        gnzyid: '',
        gnzymc: '',
        zymchz: '',
        qqlj: '',
        fcdid: '',
        pxh: '',
        sfsqxxz: '',
        szhj: '',
        dbssxyybs: '',
        dblxbs: '',
        gzlgwtbfs: ''
      },
      // 一级资源
      yjzyyh: { ...defaultYjzy },
      yjzyaddDialogVisible: false,
      yjzyyhidVisible: true,
      yjzydialogType: 'new',
      // 资源tree结构 select
      sourceType: '1',
      sourceOptions: [
        {
          value: '1',
          label: 'PC'
        },
        {
          value: '2',
          label: 'Mobile'
        }
      ],
      // 右键菜单menu list
      rightMenuOptions: {
        list: [],
        top: 0,
        left: 0,
        event: {},
        ref: {}
      },
      // 搜索过滤input value
      treeInput: '',
      // tab标签
      activeName: 'roleDetail',
      // table 选中Array
      multipleSelection: [],
      // 资源详情表单数据
      formData: {
        gnzyid: '',
        gnzymc: '',
        zymchz: '',
        qqlj: '',
        fcdid: '',
        pxh: '',
        sfsqxxz: '',
        szhj: '',
        dbssxyybs: '',
        dblxbs: '',
        gzlgwtbfs: ''
      },
      bmtree_fields: {
        功能资源id: 'gnzyid',
        功能资源名称: 'gnzymc',
        资源名称后缀: 'zymchz',
        请求路径: 'qqlj',
        父菜单id: 'fcdid',
        排序号: 'pxh',
        是否受权限限制: 'sfsqxxz',
        宿主环境: 'szhj',
        待办所属小应用标识: 'dbssxyybs',
        待办类型标识: 'dblxbs'
      },
      zytree: [],
      // 资源管理树结构
      bmtree: [],
      // 资源树结构数据格式
      defaultProps: {
        label: 'gnzymc',
        id: 'gnzyid',
        children: 'children'
      },
      // 资源树结构默认展开数组
      defaultTree: [],
      // 当前部门
      currentBm: '',
      treeExport: []
    };
  },
  watch: {
    treeInput(val) {
      // tree 结构搜索过滤
      this.$refs.treeLeft.filter(val);
    },
    activeName(val) {
      this.editDialogName = val === 'roleUser' ? '资源明细' : '资源明细';
    }
  },
  mounted() { // 页面初始化加载(只在页面初始时加载一次)
    setTimeout(() => {
      this.treeExport = [];
      const handleTree = (data) => {
        data.forEach((item) => {
          this.treeExport.push({
            gnzyid: item.gnzyid,
            gnzymc: item.gnzymc,
            zymchz: item.zymchz,
            qqlj: item.qqlj,
            fcdid: item.fcdid,
            pxh: item.pxh,
            sfsqxxz: item.sfsqxxz,
            szhj: item.szhj,
            dbssxyybs: item.dbssxyybs,
            dblxbs: item.dblxbs
          });
          if (item.children && item.children.length > 0) {
            handleTree(item.children);
          }
        });
      };
      handleTree(this.zytree);
    }, 1000);
    this.loadfirstnode();
    // 默认展开几级
    setTimeout(() => {
      // this.expendDefaultLevel(this.bmtree, 1, 2);
    }, 2000);
  },
  methods: {
    exportExcel() {
    },
    // 切换宿主环境
    changeSzhjEvent(szhjid) {
      this.loadfirstnode();
    },
    // 加载部门树
    loadfirstnode(resolve) {
      const param = {
        szhj: this.sourceType
      };
      /**
       * 获取 全部资源树 接口
       */
      findList(param).then((res) => {
        this.zytree = res.data.content;
        this.handleNodeClick(res.data.content[0]);
      }).finally(() => {
        this.loading = false;
      });
    },
    // 资源树点击后执行
    handleNodeClick(data) {
      const param = {
        gnzyid: data.gnzyid
      };
      /**
       * 获取 资源详情 接口
       */
      findOne(param).then((res) => {
        const originOld = cloneDeep(this.zyxq);
        const newData = res.data.content;
        if (newData.pxh || newData.pxh === 0) {
          newData.pxh += '';
        }
        newData.cjsj = dateFilter(newData.cjsj);
        newData.bgsj = dateFilter(newData.bgsj);
        this.$refs.detail.formData = originOld;
        Object.assign(this.$refs.detail.formData, newData);
        this.NodeClickJsyh(data);
      }).finally(() => {
        this.loading = false;
      });
    },
    // 查询资源明细
    NodeClickJsyh(data) {
      this.$refs.userDetail.xzgnzyid = data.gnzyid;
      this.$refs.userDetail.getGmzymx();
      this.$refs.userDetailJs.xzgnzyid = data.gnzyid;
      this.$refs.userDetailJs.getGmjs();
    },
    // 弹出新增资源明细
    showMxDiolag() {
      this.$refs.userDetail.addfileEdit.qqlj = '';
      this.$refs.userDetail.addfileEdit.gnzymxmc = '';
      this.$refs.userDetail.addfileEdit.visible = true;
      this.$refs.userDetail.mxdialogType = 'add';
    },
    /**
     * 一级资源新增/修改
     * @param formName
     */
    confirmYjzy(formName) {
      const param = {
        bgr: '',
        bgsj: '',
        children: [
          {}
        ],
        cjr: '',
        cjsj: '',
        fcdid: this.fjsidEdit,
        gnzymc: this.$refs.yjzyEdit.$refs[formName].model.gnzymc,
        zymchz: this.$refs.yjzyEdit.$refs[formName].model.zymchz,
        qqlj: this.$refs.yjzyEdit.$refs[formName].model.qqlj,
        pxh: this.$refs.yjzyEdit.$refs[formName].model.pxh,
        sfsqxxz: this.$refs.yjzyEdit.$refs[formName].model.sfsqxxz,
        szhj: this.$refs.yjzyEdit.$refs[formName].model.szhj,
        dbssxyybs: this.$refs.yjzyEdit.$refs[formName].model.dbssxyybs,
        dblxbs: this.$refs.yjzyEdit.$refs[formName].model.dblxbs
      };
      /**
       * 获取 新增 接口
       */
      // eslint-disable-next-line consistent-return
      this.$refs.yjzyEdit.$refs.yjzyForm.validate((valid) => {
        if (valid) {
          add(param).then((res) => {
            this.loadfirstnode();
          }).finally(() => {
            this.loading = false;
            this.$message.success('新增成功');
          });
          this.$refs.yjzyEdit.$refs[formName].clearValidate();
          this.yjzyaddDialogVisible = false;
          console.log('chengg');
        } else {
          this.$message.error('请确认格式');
          return false;
        }
      });
    },
    closeYjzy(formName) {
      this.$refs.yjzyEdit.$refs[formName].clearValidate();
      this.yjzyaddDialogVisible = false;
    },
    /**
       * tree 默认打开层级
       * @param data
       * @param startLevel
       * @param stopLevel
       */
    expendDefaultLevel(data, startLevel, stopLevel) {
      this.defaultTree = [];
      const handleTree = (dataTree, level, needLevel) => {
        dataTree.forEach((item) => {
          // this.$set(item, 'privateLevel', level);
          item.privateLevel = level;
          if (item.privateLevel <= needLevel) {
            this.defaultTree.push(item.bmm);
          }
          if (item.privateLevel <= needLevel && item.children && item.children.length > 0) {
            const index = item.privateLevel + 1;
            handleTree(item.children, index, needLevel);
          }
        });
      };
      handleTree(data, startLevel, stopLevel);
    },
    /**
       * tree node 过滤
       * @param value
       * @param data
       * @returns {boolean}
       */
    filterNode(value, data) {
      if (!value) return true;
      return data.gnzymc.indexOf(value) !== -1;
    },
    // 添加根目录文件弹窗
    FileNameEdit() {
      this.sfxsfcd = false;
      this.fileEdit.visible = true;
    },
    /**
     * 一级资源新增弹窗事件
     */
    addYjzy() {
      this.yjzyyh = { ...defaultYjzy };
      this.yjzydialogType = 'new';
      this.yjzyyhidVisible = true;
      this.yjzyaddDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.yjzyEdit.yjzyVisible = false;
      });
    },
    // 右键事件
    openMenu(event, item) {
      this.fjsidEdit = item.gnzyid;
      this.rightMenuOptions.list = [
        {
          label: '查看详情',
          onClick: this.ckJsxq
        },
        {
          label: '新建子资源',
          onClick: this.addZjs
        },
        {
          label: '编辑',
          onClick: this.rightMenuEdit
        },
        {
          label: '删除',
          onClick: this.deleteYjjs,
          style: 'color:red'
        }
      ];
      this.$refs.rightMenuPart.showRightMenu();
      this.rightMenuOptions.event = event;
      this.rightMenuOptions.ref = this.$refs.rightMenuPart.$el;
    },
    // 查看资源
    ckJsxq() {
      this.activeName = 'roleDetail';
      this.$refs.detail.isEdit = true;
    },
    // 新增子资源
    addZjs() {
      this.yjzyyh = { ...defaultYjzy };
      this.yjzydialogType = 'edit';
      this.yjzyyhidVisible = true;
      this.yjzyaddDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.yjzyEdit.yjzyVisible = true;
        this.$refs.yjzyEdit.yjzyFjsid = this.fjsidEdit;
      });
    },
    // 编辑资源
    rightMenuEdit() {
      this.activeName = 'roleDetail';
      this.$refs.detail.isEdit = false;
    },
    // 删除资源
    deleteYjjs() {
      const gnzyid = this.fjsidEdit || ''; // 角色ID
      this.$confirm('该资源下可能存在子资源，确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteCurrent(gnzyid, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 流程配置删除资源接口事件
     * @param id
     */
    deleteCurrent(id, instance, done) {
      const param = {
        gnzyid: id
      };
      del(param).then((res) => {
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
        this.$message.success('删除成功');
        this.loadfirstnode();
      });
    },
    // 获取部门下用户
    findYhListByPageBmm(bmm) {
    },
    yhhandleSizeChange(val) { // 分页

    },
    yhhandleCurrentChange(val) { // 分页

    },
    // 获取下属部门
    findBmListByPageFbmm(bmm) {
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    bmhandleSizeChange(val) { // 分页
    },
    bmhandleCurrentChange(val) { // 分页
    },
    // 新增下属部门
    addBm() {
    },
    // 用户调入
    openZzjg(title) {
    },
    // 获取部门下所有用户（子组件调用）
    findYhListByBmm(bmm) {
    },
    // 提交调入
    submitZzjg(data) {
    },
    // 用户调入
    drBmBatch(yhlist, sfzzdr) {
    },
    // 调出
    infoDcyh(row) {
    },
    // 更新部门信息
    updateBm() {
    },
    // 删除部门信息
    delBmBatch() {
    },
    /**
       * 编辑角色信息弹窗 展示
       * @param val
       */
    modifyUser(val) {
      this.$refs.roleTree.showDialog();
    }

  }
};

</script>

<style lang="scss" scoped>
  .zygl-content {
    display: flex;
    flex-direction: column;

    .content {
      display: flex;
      flex: 1;
      overflow: auto
    }

    .title {
      background-color: #FFFFFF;
      padding: 10px 10px 0;
    }

    &-left {
      width: 300px;
      height: 100%;
      padding: $page-content-padding;
      flex-shrink: 0;
      margin-right: $page-content-padding;
      background-color: #ffffff;

      &-content {
        position: relative;
        padding-top: 110px;
        height: 100%;

        .tree-top-part {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
        }
      }

      &-tree {
        height: 100%;
        overflow: auto;
      }
    }

    &-right {
      padding: $page-content-padding;
      flex: 1;
      background-color: #ffffff;
      overflow: auto;

      .button-tab {
        margin-bottom: $page-content-padding;
      }

      .content-tab-pane {
        height: 100%;
        overflow: auto;
      }
    }
  }
</style>
<style lang="scss">
  .zygl-content {
    .el-tabs__content {
      height: calc(100% - 42px)
    }

    .custom-tree-node {
      width: 100%;
    }
  }

</style>
