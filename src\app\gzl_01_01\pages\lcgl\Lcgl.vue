<template>
  <div class="lcgl-content">
    <div class="lcgl-content-main">
      <v-title name="流程管理"></v-title>
      <div class="content">
        <info-search @search="search" :fwfl-options="fwflOptions"></info-search>
        <div class="table">
          <!--      按钮-->
          <div class="button-list">
              <el-button type="primary" icon="el-icon-plus" size="small" @click="addTable">新建
              </el-button>
          </div>
          <info-table
            ref="infoTable"
            :loading="loading"
            :zt-options="ztOptions"
            :fwfl-options="fwflOptions"
            :table-data="tableData"
            :total="total"
            @tableChangePage="tableChangePage"
            @tableChangeSize="tableChangeSize"
            @modify="modify"
            @detail="detail"
            @deleteItem="deleteItem">
          </info-table>
        </div>
      </div>
    </div>
<!--    新建/编辑弹窗-->
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="500px">
      <div>
        <info-edit ref="editForm" :fwfl-options="fwflOptions" :zt-options="ztOptions" :form-data="editForm"></info-edit>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" size="small" type="primary" @click="updateCertain">确 定</el-button>
        <el-button size="small" @click="updateClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  findPageList, findFwflList, fwlistAdd, fwlistDelete, fwlistUpdate
} from '../../api/lcgl/lcgl';

// import RoleTreeDialog from './jsglComponents/RoleTreeDialog';
import VTitle from '../../../../components/title/VTitle';
import InfoSearch from './lcglComponents/InfoSearch';
import InfoTable from './lcglComponents/InfoTable';
import InfoEdit from './lcglComponents/InfoEdit';

export default {
  components: { // 注册组件
    VTitle,
    InfoSearch,
    InfoTable,
    InfoEdit
  },
  data() {
    return {
      // 服务分类
      fwflOptions: [],
      // table loading
      loading: false,
      // 确定 button loading
      buttonLoading: false,
      // table pageSize
      pageSize: 30,
      // table page
      page: 1,
      // table 数据总数
      total: 0,
      // 流程新建/编辑 title
      title: '新建流程',
      // 新建/编辑form内容
      editForm: {
        fwdyid: '', // 服务ID
        pxh: '', // 排序号
        fwmc: '', // 服务名称
        fwsm: '', // 服务说明
        fwfl: '', // 服务分类
        cjr: '', // 创建人
        cjsj: '', // 创建时间
        bgr: '', // 变更人
        bgsj: '', // 变更时间
        fwzt: 1 // 服务状态
      },
      // 服务状态 options
      ztOptions: [
        {
          label: '不可用',
          value: 0
        },
        {
          label: '草稿',
          value: 1
        },
        {
          label: '发布',
          value: 2
        }
      ],
      // 新建/编辑弹窗 visible
      visible: false,
      // 流程基本信息
      tableData: []
    };
  },
  created() {
    // 获取数据
    this.getTableData();
  },
  methods: {
    /**
     * 查询 按钮点击事件
     * @param val
     */
    search(val) {
      // search table data and render
      this.getTableData(val);
    },
    /**
     * table 页码更改 调用事件
     * @param val
     */
    tableChangePage(val) {
      this.page = val;
      const params = this.$refs.infoTable.listQuery;
      this.getTableData(params);
    },
    /**
     * table 页码数更改 调用事件
     * @param val
     */
    tableChangeSize(val) {
      this.pageSize = val;
      const params = this.$refs.infoTable.listQuery;
      this.getTableData(params);
    },
    /**
     * table 数据列表获取
     * @params 搜索条件
     */
    async getTableData(params) {
      this.loading = true;
      // 调用 服务分类接口 获取 服务分类
      if (!this.fwflOptions.length) {
        try {
          const fwflOptions = await findFwflList();
          this.fwflOptions = fwflOptions.data.content;
        } catch {
          this.loading = false;
        }
      }
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        fwdyid: '', // 服务ID
        pxh: '', // 排序号
        fwmc: '', // 服务名称
        fwsm: '', // 服务说明
        fwfl: '', // 服务分类
        cjr: '', // 创建人
        // cjsj: '', // 创建时间
        bgr: '', // 变更人
        // bgsj: '', // 变更时间
        fwzt: '' // 服务状态
      };
      if (params) {
        Object.assign(param, params);
      }
      /**
       * 获取 流程管理 接口
       */
      findPageList(param).then((res) => {
        this.tableData = res.data.content;
        this.total = res.data.pageInfo.total || 0;
      }).finally(() => {
        this.loading = false;
      });
      /* this.tableData = [
        {
          fwdyid: '1', // 服务ID
          pxh: '1', // 排序号
          fwmc: '111', // 服务名称
          fwsm: '', // 服务说明
          fwfl: '', // 服务分类
          cjr: '', // 创建人
          cjsj: '', // 创建时间
          bgr: '', // 变更人
          bgsj: '', // 变更时间
          fwzt: 1 // 服务状态
        }
      ]; */
    },
    /**
     * 新建按钮事件
     */
    addTable() {
      this.visible = true;
      this.title = '新建流程';
      this.editForm = {
        fwdyid: '', // 服务ID
        pxh: '', // 排序号
        fwmc: '', // 服务名称
        fwsm: '', // 服务说明
        fwfl: '', // 服务分类
        cjr: '', // 创建人
        cjsj: '', // 创建时间
        bgr: '', // 变更人
        bgsj: '', // 变更时间
        fwzt: 1 // 服务状态
      };
    },
    /**
     *  table 编辑操作按钮 点击事件
     * @param val
     */
    modify(val) {
      this.visible = true;
      this.title = '编辑流程';
      this.editForm = {
        fwdyid: '', // 服务ID
        pxh: '', // 排序号
        fwmc: '', // 服务名称
        fwsm: '', // 服务说明
        fwfl: '', // 服务分类
        cjr: '', // 创建人
        cjsj: '', // 创建时间
        bgr: '', // 变更人
        bgsj: '', // 变更时间
        fwzt: 1 // 服务状态
      };
      Object.assign(this.editForm, val);
    },
    /**
     * 新建 / 编辑 确定事件
     */
    updateCertain() {
      // 调用 ajax
      // 表单校验
      // eslint-disable-next-line consistent-return
      this.$refs.editForm.$refs.form.validate((valid) => {
        if (valid) {
          this.submitForm();
        } else {
          return false;
        }
      });
    },
    submitForm() {
      this.buttonLoading = true;
      if (this.title === '新建流程') {
        fwlistAdd(this.editForm).then((res) => {
          if (res.code === 200) {
            this.$message.success('新建流程成功');
          }
        }).finally(() => {
          this.buttonLoading = false;
          this.visible = false;
          // 刷新数据
          this.getTableData();
        });
      } else {
        fwlistUpdate(this.editForm).then((res) => {
          if (res.code === 200) {
            this.$message.success('流程编辑成功');
          }
        }).finally(() => {
          this.buttonLoading = false;
          this.visible = false;
          // 刷新数据
          this.getTableData();
        });
      }
    },
    /**
     * 流程配置按钮 点击事件
     * @param val
     */
    detail(val) {
      const id = val.fwdyid || ''; // 服务ID
      this.$router.push({ path: '/gzl_01_01/gzlLchj', query: { id } });
    },
    /**
     * 流程配置删除接口事件
     * @param id
     */
    deleteCurrent(id, instance, done) {
      fwlistDelete({ fwdyid: id }).then((res) => {
        if (res.code === 200) {
          const index = this.tableData.findIndex((item) => item.fwdyid === id);
          if (index > -1) {
            this.tableData.splice(index, 1);
          }
          this.$message.success('删除成功');
        }
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    /**
     * 删除当前流程
     * @param val
     */
    deleteItem(val) {
      const id = val.fwdyid || ''; // 服务ID
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteCurrent(id, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 新建/编辑弹窗 取消 按钮点击事件
     */
    updateClose() {
      this.visible = false;
    }
  }
};

</script>

<style lang="scss" scoped>
  .lcgl-content {

    &-main {
      height: 100%;
      background-color: #ffffff;
      padding: $page-content-padding;
      display: flex;
      flex-direction: column;

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .table {
          display: flex;
          flex-direction: column;
          flex: 1;
          overflow: hidden;
        }
      }

      .button-list {
        margin-bottom: $page-content-margin;
      }
    }
  }
</style>
<style lang="scss">
  .lcgl-content {

  }

</style>
