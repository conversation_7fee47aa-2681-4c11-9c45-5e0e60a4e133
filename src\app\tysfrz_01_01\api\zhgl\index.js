import { createAPI } from '@/utils/request.js';

const BASE_URL = '';
// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/tysfrz_01_01/tysfrzZhxx`;
/**
 * 账号信息获取 用户分页数据
 * @param data
 * @returns {AxiosPromise}
 */
export const getPageTabel = (data) => createAPI(`${APP_PRE}/findPageList`, 'get', data);
/**
 * 账号信息 新增
 * @param data
 * @returns {AxiosPromise}
 */
export const addPageTabel = (data) => createAPI(`${APP_PRE}/add`, 'post', data);
/**
 * 删除账号信息
 * @param data
 * @returns {AxiosPromise}
 */
export const deletePageTabel = (data) => createAPI(`${APP_PRE}/delete`, 'post', data);
/**
 * 批量删除
 * @param data
 * @returns {AxiosPromise}
 */
export const batchDeletePageTabel = (data) => createAPI(`${APP_PRE}/deleteBatchYh`, 'post', data);
/**
 * 获取正则校验规则
 * @param data
 * @returns {AxiosPromise}
 */
export const getMmRegular = (data) => createAPI(`${APP_PRE}/findJyfs`, 'get', data);

/**
 * 查询群组列表数据
 * @param data
 * @returns {AxiosPromise}
 */
export const getGroupList = (data) => createAPI(`${APP_PRE}/findQzList`, 'get', data);
/**
 * 保存群组信息
 * @param data
 * @returns {AxiosPromise}
 */
export const saveGroupList = (data) => createAPI(`${APP_PRE}/updateZhqz`, 'post', data);

/**
 * 编辑修改列表数据
 * @param data
 * @returns {AxiosPromise}
 */
export const updatePageTabel = (data) => createAPI(`${APP_PRE}/update`, 'post', data);

/**
 * 修改重置密码
 * @param data
 * @returns {AxiosPromise}
 */
export const updatePassword = (data) => createAPI(`${APP_PRE}/updateSecret`, 'post', data);
/**
 * 批量过期设置
 * @param data
 * @returns {AxiosPromise}
 */
export const updateBatchDelay = (data) => createAPI(`${APP_PRE}/updateBatchGq`, 'post', data);
/**
 * 批量解锁
 * @param data
 * @returns {AxiosPromise}
 */
export const updateBatchUnclock = (data) => createAPI(`${APP_PRE}/updateBatchJs`, 'post', data);
/**
 * 批量锁定
 * @param data
 * @returns {AxiosPromise}
 */
export const updateBatchClock = (data) => createAPI(`${APP_PRE}/updateBatchSd`, 'post', data);

/**
 * 状态编辑
 * @param data
 * @returns {AxiosPromise}
 */
export const updateStatus = (data) => createAPI(`${APP_PRE}/updateYhzt`, 'post', data);
