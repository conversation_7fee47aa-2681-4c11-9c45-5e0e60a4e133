import { createAPI } from '@/utils/request.js';

const BASE_URL = '';

const APP_PRE = `${BASE_URL}/xtgl_02_01`;

/**
 * 获取角色列表
 * @param data
 * @returns {AxiosPromise}
 */
export const findRoleList = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/findGjslb`, 'get', data);
/**
 * 获取对应角色菜单列表
 * @param data
 * @returns {AxiosPromise}
 */
export const findMenuList = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/findCdTreeByJsid`, 'get', data);
/**
 * 获取对应菜单详情信息
 * @param data
 * @returns {AxiosPromise}
 */
export const findMenuDetail = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/findCdxxByCdid`, 'get', data);
/**
 * 菜单详情保存 update
 * @param data
 * @returns {AxiosPromise}
 */
export const menuDetailSave = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/update`, 'post', data);
/**
 * 菜单删除
 * * @param data
 * @returns {AxiosPromise}
 */
export const menuDelete = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/delete`, 'post', data);
/**
 * 内置功能资源 tree获取
 * @param data
 * @returns {AxiosPromise}
 */
export const findResourcesList = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/findGnzyxx`, 'get', data);
/**
 * 文件新增接口
 * @param data
 * @returns {AxiosPromise}
 */
export const resourceAdd = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/add`, 'post', data);
/**
 * 批量添加 资源
 * @param data
 * @returns {AxiosPromise}
 */
export const resourceAddMore = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/addNzgnList`, 'post', data);
/**
 * 获取系统标识
 * @param data
 * @returns {AxiosPromise}
 */
export const getXtbsApi = (data) => createAPI(`${APP_PRE}/tygzt3Tycdpz/findXtbs`, 'get', data);
