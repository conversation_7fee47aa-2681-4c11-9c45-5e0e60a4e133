<template>
  <div ref="myChart" :style="{width: '100%', height: '100%'}"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/line');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');

export default {
  props: {
    lineData: {
      type: Object
    },
    color: {
      type: Array,
      default() {
        return ['#1a9bfc', '#99da69', '#e32f46', '#7049f0', '#fa704d', '#01babc'];
      }
    }
  },
  watch: {},
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      console.log(1);
      const { lineData, color } = this;

      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);

      const legendData = [];
      const xAxisData = lineData.xData;
      const seriesName1 = lineData.seriesName1;
      const seriesName2 = lineData.seriesName2;
      const seriesData1 = lineData.yData1;
      const seriesData2 = lineData.yData2;

      const option = {
        tooltip: { // 提示框组件
          trigger: 'axis',
          formatter: '{a0}: {c0}次<br />{a1}: {c1}次',
          axisPointer: {
            type: 'shadow',
            label: {
              backgroundColor: '#4c647c'
            }
          },
          textStyle: {
            color: '#fff',
            fontStyle: 'normal',
            fontFamily: '微软雅黑',
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        legend: {
          right: 'center',
          top: '5%',
          textStyle: {
            color: '#4c647c',
            fontStyle: 'normal',
            fontFamily: '微软雅黑',
            fontSize: 12
          },
          // eslint-disable-next-line no-restricted-globals
          data: name
        },
        xAxis: [
          {
            type: 'category',

            boundaryGap: true, // 坐标轴两边留白
            data: xAxisData,
            axisLabel: { // 坐标轴刻度标签的相关设置。
              formatter: '{value}时',
              // rotate:30,
              interval: 0, // 设置为 1，表示『隔一个标签显示一个标签』
              margin: 15,
              textStyle: {
                color: '#4c647c',
                fontStyle: 'normal',
                fontFamily: '微软雅黑',
                fontSize: 12
              }
            },
            axisTick: { // 坐标轴刻度相关设置。
              show: false
            },
            axisLine: { // 坐标轴轴线相关设置
              lineStyle: {
                color: '#4c647c',
                opacity: 0.2
              }
            },
            splitLine: { // 坐标轴在 grid 区域中的分隔线。
              show: false
            }
          }
        ],
        yAxis: [

          {
            type: 'value',
            name: '资源数据访问量', // 坐标轴名称
            splitNumber: 5,
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#4c647c',
                fontStyle: '{value}',
                fontFamily: '微软雅黑',
                fontSize: 12
              }
            },
            axisLine: { // 坐标轴轴线相关设置
              lineStyle: {
                color: '#76DA91'
                // opacity:0.2
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: ['#000'],
                opacity: 0.06
              }
            }

          },
          {
            type: 'value',
            name: '资源目录访问量', // 坐标轴名称
            splitNumber: 5,
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#4c647c',
                fontStyle: '{value}',
                fontFamily: '微软雅黑',
                fontSize: 12
              }
            },
            axisLine: { // 坐标轴轴线相关设置
              lineStyle: {
                color: '#63B2EE'
                // opacity:0.2
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#000'],
                opacity: 0.06
              }
            }

          }
        ],
        series: [
          {
            name: seriesName1,
            type: 'line',
            data: seriesData1,
            symbolSize: 11,
            barWidth: 10,
            barGap: 0, // 柱间距离
            itemStyle: { // 图形样式
              normal: {
                lineStyle: {
                  width: 3
                  // type: 'dotted',
                },
                barBorderRadius: 20,
                color: '#76DA91'
              }
            }
          },
          {
            name: seriesName2,
            type: 'line',
            data: seriesData2,
            symbolSize: 11,
            yAxisIndex: 1,
            barWidth: 10,
            barGap: 1, // 柱间距离
            itemStyle: { // 图形样式
              normal: {
                lineStyle: {
                  width: 3
                  // type: 'dotted',
                },
                barBorderRadius: 20,
                color: '#63B2EE'
              }
            }
          }

        ]
      };

      // 绘制图表
      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
