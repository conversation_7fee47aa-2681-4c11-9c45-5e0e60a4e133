<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">账号名称</span>
        <el-input
          class="zhxy-form-inline"
          v-model="listQuery.zhid"
          placeholder="账号名称"
          size="small">
        </el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">认证结果</span>

        <el-select
          class="zhxy-form-inline"
          size="small"
          v-model="listQuery.rzzt"
          placeholder="请选择">
          <el-option
            v-for="item in rzztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">认证IP</span>
        <el-input
          class="zhxy-form-inline"
          v-model="listQuery.rzip"
          placeholder="认证IP"
          size="small">
        </el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">认证名称</span>
        <el-input
          class="zhxy-form-inline"
          v-model="listQuery.mbyyid"
          placeholder="认证名称"
          size="small">
        </el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">认证时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.rzsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'yhglSearch',
  data() {
    return {
      // label 宽度
      lableWidth: '120px',
      // labelWidth
      lableWidthSingle: '450px',
      // search params
      listQuery: {
        zhid: '',
        rzzt: '',
        rzip: '',
        mbyyid: '',
        rzsj: ''
      },
      // 认证结果 options
      rzztOptions: [
        {
          label: '认证成功',
          value: 1
        },
        {
          label: '认证失败',
          value: 2
        }
      ]
    };
  },
  methods: {
    /**
     * 搜索查询
     */
    search() {
      this.$emit('search');
    },
    /**
     * 重置搜索条件
     */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });
      this.$emit('reset');
    }
  }
};
</script>

<style lang="scss" scoped>
.search-fold {
  color: $page-font-hover-color;
  display: inline-block;
  margin-left: 10px;
  margin-right: 5px;
  cursor: pointer
}
</style>
