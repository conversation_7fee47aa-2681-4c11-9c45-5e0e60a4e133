<template>
  <div class="dmb-content">
    <choose-user-dialog :tree-data="bmtree" ref="chooseTree"></choose-user-dialog>
<!--    <el-dialog title="新增部门用户" :visible.sync="addDepartmentUser" width="500px" lock-scroll-->
<!--               close-on-press-escape>-->
<!--      <div class="">-->
<!--        <el-form :model="addUserForm" :rules="rules" ref="addUserForm" class="" label-width="120px">-->
<!--          <el-form-item label="部门名称">-->
<!--            <p>-->
<!--              {{addUserForm.departmentName}}-->
<!--            </p>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="用户名称" required prop="bmm">-->
<!--            <el-input style="width: 80%" size="small" v-model="addUserForm.userName" placeholder="">-->
<!--              <el-button slot="append" @click="()=>{$refs.chooseTree.showDialog()}">选择</el-button>-->
<!--            </el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="是否主职" required prop="bmm">-->
<!--            <el-radio-group v-model="addUserForm.isMainJob">-->
<!--              <el-radio :label="1">主职</el-radio>-->
<!--              <el-radio :label="0">兼职</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
<!--      </div>-->

<!--      <div slot="footer">-->
<!--        <el-button size="small" type="primary" @click="addBm()">确 定</el-button>-->
<!--        <el-button size="small" @click="dialogVisible = false">取 消</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->

    <div class="title">
      <v-title name="代码表"></v-title>
    </div>
    <div class="content">
      <div class="dmb-content-left">
        <div class="dmb-content-left-content">
          <div class="dmb-content-left-top">
            <el-input
              placeholder="请输入关键字"
              type="text"
              size="small"
              prefix-icon="el-icon-search"
              v-model="treeinput" clearable>
            </el-input>
            <div style="padding-left: 25px">
              <el-button style="font-weight: bold" size="default" icon="el-icon-plus" type="text" @click="addDmfl()">新建代码分类</el-button>
            </div>
          </div>
          <div class="dmb-content-left-tree">
            <el-tree
              ref="tree"
              :props="defaultProps"
              highlight-current
              :data="bmtree"
              node-key="dmflbs"
              default-expand-all
              :filter-node-method="filterNode"
              @node-contextmenu="openMenu"
              @node-click="handleNodeClick">
              <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{data.dmflmc}}
              </span>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="dmb-content-right" ref="right">
        <el-tabs type="card" v-model="activeName" style="height: 100%">
          <el-tab-pane label="分类详情" name="roleDetail" class="content-tab-pane">
            <department-detail ref="detail"></department-detail>
          </el-tab-pane>
          <el-tab-pane label="下级代码项" name="first" class="content-tab-pane content-tab-pane-sub">
            <div class="zhxy-form zhxy-form-search-part">
              <el-form label-width="120px" inline :model="departmentSearch" ref="searchForm">
                <el-form-item>
                  <span class="zhxy-form-label" slot="label">代码名称</span>
                  <el-input class="zhxy-form-inline" v-model="departmentSearch.dmmc"
                            placeholder="代码名称"
                            size="small"></el-input>
                </el-form-item>
                <el-form-item>
                  <span class="zhxy-form-label" slot="label">代码值</span>
                  <el-input class="zhxy-form-inline" v-model="departmentSearch.dmz"
                            placeholder="代码值"
                            size="small"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="loaddmxnode()" size="small">查询
                  </el-button>
                  <el-button type="" @click="reset()" size="small">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="button-tab">
              <el-button type="primary" icon="el-icon-plus" size="small"
                         @click="addZjs()">新增代码项
              </el-button>
              <el-button type="danger" icon="el-icon-delete-solid" size="small" @click="plscYh()">批量删除
              </el-button>
            </div>
            <department-sub ref="userDetail" @updateDmx="updateDmx" @delDmx="delDmx" @handleSelectionChange="handleSelectionChange" @addxjdm="addxjdm"></department-sub>
          </el-tab-pane>
<!--          <el-tab-pane label="下级部门" name="second" class="content-tab-pane">-->
<!--            <department-sub-search></department-sub-search>-->
<!--            <div class="button-tab">-->
<!--              <el-button type="" size="small" @click="addBm()">新增下属部门</el-button>-->
<!--              <el-button type="danger" size="small" @click="delBmBatch()">删除</el-button>-->
<!--            </div>-->
<!--            <department-sub :table-data="tableDataBm"></department-sub>-->
<!--          </el-tab-pane>-->
        </el-tabs>
      </div>
    </div>
    <right-menu ref="rightMenuPart" :options="rightMenuOptions"></right-menu>
    <!--新增代码分类-->
    <el-dialog
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="yjjsaddDialogVisible"
      :title="yjjsdialogType === 'edit'?'编辑代码分类':'新建代码分类'"
      :close-on-click-modal="false" @close="closeYjjs('yjjsForm')">
      <yjjs-add ref="yjjsEdit" :yhid-visible="yjjsyhidVisible" :FormData="yjjsyh"></yjjs-add>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="confirmYjjs('yjjsForm')">确定</el-button>
        <el-button type="" size="small" @click="closeYjjs('yjjsForm')">取消</el-button>
      </div>
    </el-dialog>

    <!--新增代码项-->
    <el-dialog :visible.sync="dmxDialogVisible" customClass="zhxy-dialog-view" :title="dmxdialogType === 'edit'?'修改代码项':'新建代码项'"
               :close-on-click-modal="false" @close="closeDmx('dmxForm')">
      <dmx-add ref="dmxEdit" :yhid-visible="dmxVisible" :FormInline="dmx"></dmx-add>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="confirmDmx('dmxForm')">确定</el-button>
        <el-button type="" size="small" @click="closeDmx('dmxForm')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';
import { cloneDeep } from 'lodash';
import {
  findList, findOne, add, adddmx, updatedmx, findListByPageDmx, deldmx, deleteBatchDmx, del, findOneByID, findDmxList
} from '../../api/dmb/dmb';
import VTitle from '../../../../components/title/VTitle';
import DepartmentDetail from './dmbComponents/DepartmentDetail';
import ChooseUserDialog from '../components/userDialog/ChooseUserDialog';
import RightMenu from '../../components/RightMenu/index';
import YjjsAdd from './dmbComponents/DmflAdd';
import DmxAdd from './dmbComponents/AddBmDialog';
import DepartmentSub from './dmbComponents/DepartmentSub';

const defaultYjjs = {
  dmflbs: '',
  dmflmc: '',
  xscj: '',
  pxh: '',
  sfky: ''
};

const defaultdmx = {
  dmflbs: '',
  sjdmid: '',
  dmid: '',
  dmmc: '',
  dmz: '',
  kzzd1: '',
  kzzd2: '',
  kzzd3: '',
  pxh: '',
  sfky: ''
};

export default {
  components: { // 注册组件
    VTitle,
    DepartmentDetail,
    ChooseUserDialog,
    DepartmentSub,
    RightMenu,
    YjjsAdd,
    DmxAdd
  },
  data() {
    return {
      // 右键菜单menu list
      rightMenuOptions: {
        list: [],
        top: 0,
        left: 0,
        event: {},
        ref: {}
      },
      jsxq: {
        dmflbs: '',
        dmflmc: '',
        xscj: '',
        pxh: '',
        sfky: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      },
      // 一级角色
      yjjsyh: { ...defaultYjjs },
      yjjsaddDialogVisible: false,
      yjjsyhidVisible: true,
      yjjsdialogType: 'new',
      // 代码项
      dmx: { ...defaultdmx },
      dmxDialogVisible: false,
      dmxVisible: true,
      dmxdialogType: 'new',
      // 部门详情------------------
      departmentSearch: {
        userName: ''
      },
      // 界面定义属性
      scrollerHeight: 'calc(100% - 110px)',
      treeinput: '',
      // tab标签
      activeName: 'roleDetail',
      // 下级部门table
      tableDataBm: [],
      // table 选中Array
      multipleSelection: [],

      // 部门详情表单数据
      FormInline: {
        dmflbs: '',
        dmflmc: '',
        xscj: '',
        pxh: '',
        sfky: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      },
      // 部门详情表单数据
      formData: {
        dmflbs: '',
        dmflmc: '',
        xscj: '',
        pxh: '',
        sfky: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      },
      // 部门管理树结构
      bmtree: [],
      // form rules
      rules: {
        bmm: [
          {
            required: true,
            message: '部门码不可为空',
            trigger: 'blur'
          }
        ],
        sfejbm: [
          {
            required: true,
            message: '请选择是否微页面',
            trigger: 'change'
          }
        ]
      },
      // 部门管理树结构数据格式
      defaultProps: {
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      },
      // 部门管理树结构默认展开数组
      defaultTree: [],
      // 当前部门
      currentBm: ''
    };
  },
  watch: {
    treeinput(val) {
      // tree 结构搜索过滤
      this.$refs.tree.filter(val);
    }
  },
  mounted() { // 页面初始化加载(只在页面初始时加载一次)
    // this.findListByPage()
    this.loadfirstnode();
    // 默认展开几级
    setTimeout(() => {
      // this.expendDefaultLevel(this.bmtree, 1, 2);
    }, 2000);
  },
  methods: {
    /**
     * 代码分类新增弹窗事件
     */
    addDmfl() {
      this.yjjsyh = { ...defaultYjjs };
      this.yjjsdialogType = 'new';
      this.yjjsyhidVisible = true;
      this.yjjsaddDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.yjjsEdit.yjjsVisible = false;
      });
    },
    // 修改资源明细
    updateDmx(data) {
      this.dmx = { ...defaultdmx };
      this.dmxdialogType = 'edit';
      this.dmxVisible = false;
      this.dmxDialogVisible = true;
      this.$nextTick(() => {
        const originOld = cloneDeep(defaultdmx);
        const newData = data;
        if (newData.pxh || newData.pxh === 0) {
          newData.pxh += '';
        }
        this.$refs.dmxEdit.FormInline = originOld;
        Object.assign(this.$refs.dmxEdit.FormInline, newData);
      });
    },
    /**
     * 代码项新增/修改
     * @param formName
     */
    confirmDmx(formName) {
      if (this.dmxdialogType !== 'edit') {
        const param = {
          dmflbs: this.fjsidEdit,
          dmmc: this.$refs.dmxEdit.FormInline.dmmc,
          dmz: this.$refs.dmxEdit.FormInline.dmz,
          pxh: this.$refs.dmxEdit.FormInline.pxh,
          kzzd1: this.$refs.dmxEdit.FormInline.kzzd1,
          kzzd2: this.$refs.dmxEdit.FormInline.kzzd2,
          kzzd3: this.$refs.dmxEdit.FormInline.kzzd3,
          sfky: this.$refs.dmxEdit.FormInline.sfky,
          sjdmid: this.$refs.dmxEdit.FormInline.sjdmid
        };
        adddmx(param).then((result) => {
          this.$message.success('新增成功');
          this.loaddmxnode();
        }).finally(() => {
          this.dmxDialogVisible = false;
        });
      } else {
        updatedmx(this.$refs.dmxEdit.FormInline).then((result) => {
          this.loaddmxnode();
        }).finally(() => {
          this.dmxDialogVisible = false;
        });
      }
    },
    delDmx(row) {
      const id = row.dmid || ''; // dmid
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            deldmx({ dmid: id }).then((res) => {
              if (res.code === 200) {
                this.$message.success('删除成功');
                this.loaddmxnode();
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 关闭一级角色窗口时处理
     * @param formName
     */
    closeYjjs(formName) {
      this.$refs.yjjsEdit.$refs[formName].clearValidate();
      this.yjjsaddDialogVisible = false;
    },
    /**
     * 一级角色新增/修改
     * @param formName
     */
    confirmYjjs(formName) {
      const param = {
        bgr: '',
        bgsj: '',
        cjr: '',
        cjsj: '',
        xscj: this.$refs.yjjsEdit.$refs[formName].model.xscj,
        dmflbs: this.$refs.yjjsEdit.$refs[formName].model.dmflbs,
        dmflmc: this.$refs.yjjsEdit.$refs[formName].model.dmflmc,
        sfky: this.$refs.yjjsEdit.$refs[formName].model.sfky,
        pxh: this.$refs.yjjsEdit.$refs[formName].model.pxh
      };
      const id = {
        dmflbs: this.$refs.yjjsEdit.$refs[formName].model.dmflbs
      };
      findOneByID(id).then((res) => {
        if (Object.keys(res.data).length === 0) {
          add(param).then((result) => {
          }).finally(() => {
            this.loading = false;
            this.$message.success('新增成功');
            this.loadfirstnode();
          });
        } else {
          this.$message.error('该代码分类标识已存在！');
        }
      });
      this.$refs.yjjsEdit.$refs[formName].clearValidate();
      this.yjjsaddDialogVisible = false;
      // this.loadfirstnode();
    },
    // 右键事件
    openMenu(event, item) {
      this.fjsidEdit = item.dmflbs;
      this.rightMenuOptions.list = [
        {
          label: '查看详情',
          onClick: this.ckJsxq
        },
        {
          label: '新建代码项',
          onClick: this.addZjs
        },
        {
          label: '编辑',
          onClick: this.rightMenuEdit
        },
        {
          label: '删除',
          onClick: this.deleteYjjs,
          style: 'color:red'
        }
      ];
      this.$refs.rightMenuPart.showRightMenu();
      this.rightMenuOptions.event = event;
      this.rightMenuOptions.ref = this.$refs.rightMenuPart.$el;
    },
    // 弹窗内事件
    rightMenuEdit() {
      this.activeName = 'roleDetail';
      this.$refs.detail.isEdit = false;
    },
    ckJsxq() {
      this.activeName = 'roleDetail';
      this.$refs.detail.isEdit = true;
    },
    // 新增代码项
    addZjs() {
      if (this.fjsidEdit == null || this.fjsidEdit === '') {
        this.$message.warning('请选择代码分类后新增 ！');
      } else {
        this.dmx = { ...defaultdmx };
        this.dmxdialogType = 'new';
        this.dmxVisible = true;
        this.dmxDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.dmxEdit.dmflbs = this.fjsidEdit;
        });
      }
    },
    // 新增下级代码项
    addxjdm(data) {
      this.dmx = { ...defaultdmx };
      this.dmxdialogType = 'new';
      this.dmxVisible = true;
      this.dmxDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.dmxEdit.dmflbs = this.fjsidEdit;
        this.$refs.dmxEdit.sjdmid = data.dmid;
        this.$refs.dmxEdit.FormInline.sjdmid = data.dmid;
      });
    },
    /**
     * 关闭一级角色窗口时处理
     * @param formName
     */
    closeDmx(formName) {
      this.$refs.dmxEdit.$refs[formName].clearValidate();
      this.dmxDialogVisible = false;
    },
    // 删除角色
    deleteYjjs() {
      const dmflbs = this.fjsidEdit || ''; // 角色ID
      this.$confirm('该代码分类下可能存在代码项，确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            del({ dmflbs }).then((res) => {
              if (res.code === 200) {
                // const index = this.tableData.findIndex((item) => item.dmflbs === dmflbs);
                // if (index > -1) {
                //   this.tableData.splice(index, 1);
                // }
                this.loadfirstnode();
                this.$message.success('删除成功');
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
       * tree 默认代开层级
       * @param data
       * @param startLevel
       * @param stopLevel
       */
    expendDefaultLevel(data, startLevel, stopLevel) {
      this.defaultTree = [];
      const handleTree = (dataTree, level, needLevel) => {
        dataTree.forEach((item) => {
          // this.$set(item, 'privateLevel', level);
          item.privateLevel = level;
          if (item.privateLevel <= needLevel) {
            this.defaultTree.push(item.bmm);
          }
          if (item.privateLevel <= needLevel && item.children && item.children.length > 0) {
            const index = item.privateLevel + 1;
            handleTree(item.children, index, needLevel);
          }
        });
      };
      handleTree(data, startLevel, stopLevel);
    },
    /**
       * tree node 过滤
       * @param value
       * @param data
       * @returns {boolean}
       */
    filterNode(value, data) {
      if (!value) return true;
      return data.dmflmc.indexOf(value) !== -1;
    },
    // 部门树点击后执行
    handleNodeClick(data) {
      this.fjsidEdit = data.dmflbs;
      const param = {
        dmflbs: data.dmflbs
      };
      /**
       * 获取 分类详情 接口
       */
      findOne(param).then((res) => {
        const origin = this.$refs.detail.formData;
        const newData = res.data.content;
        if (newData.pxh || newData.pxh === 0) {
          newData.pxh += '';
        }
        if (newData.xscj || newData.xscj === 0) {
          newData.xscj += '';
        }
        newData.cjsj = dateFilter(newData.cjsj);
        newData.bgsj = dateFilter(newData.bgsj);
        this.jsxq = newData;
        this.$refs.detail.formData = newData;
        // this.$refs.roleTree.jsidZy = data.jsid;
        this.dmflbs = data.dmflbs;
        this.loaddmxnode();
      }).finally(() => {
        this.loading = false;
      });
    },
    /**
     * 获取 代码项 接口
     */
    // search(params) {
    //   const param = {
    //     pageSize: this.$refs.userDetail.pageSize,
    //     page: this.$refs.userDetail.page,
    //     dmmc: this.departmentSearch.dmmc,
    //     dmz: this.departmentSearch.dmz,
    //     dmflbs: this.dmflbs
    //   };
    //   if (params) {
    //     Object.assign(param, params);
    //   }
    //   findListByPageDmx(param).then((res) => {
    //     this.$refs.userDetail.tableData = res.data.content;
    //     this.$refs.userDetail.total = res.data.pageInfo.total;
    //   }).finally(() => {
    //     this.loading = false;
    //   });
    // },
    reset() {
      const param = {
        dmmc: '',
        dmz: '',
        dmflbs: this.dmflbs
      };
      this.departmentSearch.dmmc = '';
      this.departmentSearch.dmz = '';
      this.loaddmxnode(param);
    },
    // 加载部门树
    loadfirstnode(resolve) {
      const param = {};
      /**
       * 获取 全部角色树 接口
       */
      findList(param).then((res) => {
        this.bmtree = res.data.content;
        this.handleNodeClick(res.data.content[0]);
        // this.NodeClickJsyh(res.data.content[0]);
        // this.NodeClickJszy(res.data.content[0]);
        // this.$refs.roleTree.jsidZy = res.data.content[0].jsid;
        this.dmflbs = res.data.content[0].dmflbs;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 加载代码项树
    loaddmxnode(resolve) {
      const param = {
        dmmc: this.departmentSearch.dmmc,
        dmz: this.departmentSearch.dmz,
        dmflbs: this.dmflbs
      };

      findDmxList(param).then((res) => {
        this.$refs.userDetail.tableData = res.data.content;
        // this.handleNodeClick(res.data.content[0]);
      }).finally(() => {
        this.loading = false;
      });
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 新增下属部门
    // eslint-disable-next-line consistent-return
    addBm() {
      if (this.currentBm == null || this.currentBm === '') {
        this.$message.warning('请选择上级部门后新增 ！');
        return false;
      }
      this.$refs.addBmDialog.fbmm = this.$refs.bmtree.getCurrentNode().bmm;
      this.$refs.addBmDialog.show();
    },
    /**
     * 批量删除
     */
    plscYh() {
      if (this.multipleSelection.length === 0) {
        this.$message.error('请勾选代码项');
      } else {
        const arr = [];
        this.multipleSelection.forEach((item) => {
          const itemparam = {
            dmid: item.dmid
          };
          arr.push(itemparam);
        });
        /**
         * 批量删除 接口
         */
        const param = {
          ids: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },
    /**
     * 批量删除 角色下用户 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      deleteBatchDmx(param).then((res) => {
        this.$message.success('删除成功');
        this.loaddmxnode();
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    }
  }
};

</script>

<style lang="scss" scoped>
  .dmb-content {
    display: flex;
    flex-direction: column;

    .content {
      display: flex;
      flex: 1;
      overflow: auto
    }

    .title {
      background-color: #FFFFFF;
      padding: 10px 10px 0;
    }

    .rightMenu {
      background-color: #FFFFFF;
      border-radius: 4px;
      box-shadow: 0 1px 6px rgba(0, 0, 0, .2);

      ul {
        padding: $page-content-padding;

        li {
          padding: 5px;
          border-bottom: 1px solid $page-bg-color;
          cursor: pointer;

          &:hover {
            color: $page-font-hover-color;
          }
        }
      }
    }

    &-left {
      width: 300px;
      height: 100%;
      padding: $page-content-padding;
      flex-shrink: 0;
      margin-right: $page-content-padding;
      background-color: #ffffff;

      &-content {
        position: relative;
        padding-top: 72px;
        height: 100%;

        .dmb-content-left-top {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
        }
      }

      &-tree {
        height: 100%;
        overflow: auto;
      }
    }

    &-right {
      padding: $page-content-padding;
      flex: 1;
      background-color: #ffffff;
      overflow: auto;

      .button-tab {
        margin-bottom: $page-content-padding;
      }
      .content-tab-pane{
        height: 100%;
        overflow: auto;
        &-sub{
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
</style>
<style lang="scss">
  .dmb-content {
    .el-tabs__content {
      height: calc(100% - 42px)
    }

    .custom-tree-node {
      width: 100%;
    }
  }

</style>
