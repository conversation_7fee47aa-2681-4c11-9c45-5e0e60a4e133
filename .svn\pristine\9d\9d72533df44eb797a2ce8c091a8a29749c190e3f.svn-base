<template>
  <div class="yjlsgl-content">
    <div class="yjlsgl-content-main">
      <v-title name="预警历史管理"></v-title>
      <yjlsgl-search ref="searchElement" @search="search"></yjlsgl-search>
      <div class="table">
        <div class="table-content">
          <yjlsgl-table :scroller-height="scrollerHeight" :table-data="tableData"></yjlsgl-table>
        </div>
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import {
  findYjlsList
} from '@/app/xtjkyyj_01_01/api/yjlsgl/yjlsgl.js';
import YjlsglSearch from './yjlsglComponents/YjlsglSearch';
import YjlsglTable from './yjlsglComponents/YjlsglTable';

const defaultYjls = {
  yjbs: '',
  yjlb: '',
  yjfl: '',
  yjmc: '',
  yjms: '',
  yjjbm: '',
  yjjbmc: '',
  yjfs: '',
  scsj: '',
  gzfscjsj: '',
  gzhfcjsj: ''
};

export default {
  name: 'yjlsgl',
  components: {
    VTitle,
    YjlsglSearch,
    YjlsglTable
  },
  data() {
    return {
      scrollerHeight: 0,
      currentPage: 1, // 初始页
      yjls: { ...defaultYjls },
      tableData: [
        {
          yjbs: '',
          yjlb: '',
          yjfl: '',
          yjmc: '',
          yjms: '',
          yjjbm: '',
          yjjbmc: '',
          yjfs: '',
          scsj: '',
          gzfscjsj: '',
          gzhfcjsj: ''
        }
      ], // 列表数据集合
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      options: {
        type: Array,
        default: () => []
      }
    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 监听页面尺寸改变table size
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight - height - 220;
    },
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        yjlb: null,
        yjfl: null,
        yjjbm: null,
        sfhl: null,
        scsjq: null,
        scsjz: null,
        yjmc: null,
        yjbs: null,
        glywid: null
      };
      if (params) {
        Object.assign(param, params);
      }
      findYjlsList(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    reset() {
      this.listQuery.yjlb = null;
      this.listQuery.yjfl = null;
      this.listQuery.yjjbm = null;
      this.listQuery.sfhl = null;
      this.listQuery.scsjq = null;
      this.listQuery.scsjz = null;
      this.listQuery.yjmc = null;
      this.listQuery.yjbs = null;
      this.listQuery.glywid = null;
      this.search();
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        yjlb: this.$refs.searchElement.listQuery.yjlb,
        yjfl: this.$refs.searchElement.listQuery.yjfl,
        yjjbm: this.$refs.searchElement.listQuery.yjjbm,
        sfhl: this.$refs.searchElement.listQuery.sfhl,
        scsjq: this.$refs.searchElement.listQuery.scsjq,
        scsjz: this.$refs.searchElement.listQuery.scsjz,
        yjmc: this.$refs.searchElement.listQuery.yjmc,
        yjbs: this.$refs.searchElement.listQuery.yjbs,
        glywid: this.$refs.searchElement.listQuery.glywid
      };
      this.search(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        yjlb: this.$refs.searchElement.listQuery.yjlb,
        yjfl: this.$refs.searchElement.listQuery.yjfl,
        yjjbm: this.$refs.searchElement.listQuery.yjjbm,
        sfhl: this.$refs.searchElement.listQuery.sfhl,
        scsjq: this.$refs.searchElement.listQuery.scsjq,
        scsjz: this.$refs.searchElement.listQuery.scsjz,
        yjmc: this.$refs.searchElement.listQuery.yjmc,
        yjbs: this.$refs.searchElement.listQuery.yjbs,
        glywid: this.$refs.searchElement.listQuery.glywid
      };
      this.search(param);
    }
  }
};
</script>
<style lang="scss" scoped>
  .yjlsgl-content {
    &-main{
      background-color: #ffffff;
      padding: $page-content-padding;
    }
    .table {
      &-content {
        margin-top: $page-content-margin;
      }
    }
    .dialog-footer{
      text-align: center;
      display: flex;
      justify-content: flex-end;
    }
    .qran{
      margin-top: -3.1rem;
      margin-right: 1rem;
      float: right;
    }
  }
</style>
