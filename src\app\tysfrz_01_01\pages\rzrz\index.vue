<template>
  <div class="rzrz-content">
    <div class="rzrz-content-main">
      <!--      标题-->
      <v-title name="认证日志"></v-title>
      <rzrz-search
        ref="searchElement"
        @search="search"
        @reset="reset">
      </rzrz-search>
      <!--      table 表单部分 -->
      <div class="table" v-loading="tableLoading">
        <div class="table-content">
          <rzrz-table :table-data="tableData" ></rzrz-table>
        </div>
        <!--        table pageInation-->
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import { getRzrzPageList } from '@/app/tysfrz_01_01/api/rzrz';
import { cloneDeep } from 'lodash';
import RzrzSearch from './rzrzComponents/InfoSearch';
import RzrzTable from './rzrzComponents/InfoTable';

export default {
  name: 'rzrz',
  components: {
    RzrzSearch,
    RzrzTable,
    VTitle
  },
  data() {
    return {
      // 页面loading
      tableLoading: false,
      // 列表data
      tableData: [], // 列表数据集合
      // 页码总数
      pageTotal: 0,
      page: 1,
      // 每页个数
      pageSize: 30
    };
  },
  mounted() {
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /**
     * search 搜索事件
     * @param params
     * @returns {Promise<void>}
     */
    search() {
      const searchParams = this.$refs.searchElement.listQuery;
      const paramsTemp = cloneDeep(searchParams);
      if (paramsTemp.rzsj) {
        paramsTemp.rzsjkssj = paramsTemp.rzsj[0];
        paramsTemp.rzsjjssj = paramsTemp.rzsj[1];
      }
      delete paramsTemp.rzsj;

      const params = {
        pageSize: this.pageSize,
        page: this.page,
        ...paramsTemp
      };
      this.tableLoading = true;
      // 获取认证日志接口
      getRzrzPageList(params).then((res) => {
        this.tableData = res.data.content || [];
        this.pageTotal = res.data.pageInfo.total || 0;
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    /**
     * 查询条件重置
     */
    reset() {
      this.page = 1;
      this.search();
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.search();
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.page = val;
      this.search();
    }
  }
};
</script>
<style lang="scss" scoped>
.rzrz-content {

  &-main{
    height: 100%;
    background-color: #ffffff;
    padding: $page-content-padding;
    display: flex;
    flex-direction: column;
  }

  .table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    &-content {
      flex: 1;
      overflow: auto;
      margin-top: $page-content-margin;
    }

  }
  .dialog-footer{
    text-align: center;
  }
}
</style>
