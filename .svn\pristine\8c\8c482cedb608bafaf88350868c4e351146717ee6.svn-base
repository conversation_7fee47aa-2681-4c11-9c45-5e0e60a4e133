import { createAPI } from '@/utils/request.js';

// 接口地址映射
const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/gzl_01_01`;

/**
 * 查询工作流 流程管理table列表数据
 * @param data pageSize page
 * @returns {AxiosPromise}
 */
export const findPageList = (data) => createAPI(`${APP_PRE }/gzlJbxx/findPageList`, 'get', data);
/**
 * 获取服务类别
 * @param data
 * @returns {AxiosPromise}
 */
export const findFwflList = (data) => createAPI(`${APP_PRE}/gzlJbxx/findFwflList?dmflbs=GZL_FWFL`, 'get', data);

/**
 * 流程服务添加
 * @param data
 * @returns {AxiosPromise}
 */
export const fwlistAdd = (data) => createAPI(`${APP_PRE}/gzlJbxx/add`, 'post', data);
/**
 * 流程服务修改
 * @param data
 * @returns {AxiosPromise}
 */
export const fwlistUpdate = (data) => createAPI(`${APP_PRE}/gzlJbxx/update`, 'post', data);
/**
 * 流程服务删除
 * @param data
 * @returns {AxiosPromise}
 */
export const fwlistDelete = (data) => createAPI(`${APP_PRE}/gzlJbxx/delete`, 'post', data);

/**
 * 流程环节 列表查询
 * @param data
 * @returns {AxiosPromise}
 */
export const lclistFind = (data) => createAPI(`${APP_PRE}/gzlLchj/findList`, 'get', data);
/**
 * 流程环节 添加
 * @param data
 * @returns {AxiosPromise}
 */
export const lclistAdd = (data) => createAPI(`${APP_PRE}/gzlLchj/add`, 'post', data);
/**
 * 流程环节编辑
 * @param data
 * @returns {AxiosPromise}
 */
export const lclistUpdate = (data) => createAPI(`${APP_PRE}/gzlLchj/update`, 'post', data);
/**
 * 获取流程明细
 * @param data
 * @returns {AxiosPromise}
 */
export const lcmxFind = (data) => createAPI(`${APP_PRE}/gzlLchj/findMbhjList`, 'get', data);

/**
 * 流程明细弹窗 保存
 * @param data
 * @returns {AxiosPromise}
 */
export const lcmcSave = (data) => createAPI(`${APP_PRE}/gzlLchj/addMbhjxx`, 'post', data);

/**
 * 环节按钮权限
 * @param data
 * @returns {AxiosPromise}
 */
export const lcBtnConfigFind = (data) => createAPI(`${APP_PRE}/gzlLchj/findHjanList`, 'get', data);
/**
 * 保存环节按钮权限
 * @param data
 * @returns {AxiosPromise}
 */
export const lcBtnConfigSave = (data) => createAPI(`${APP_PRE}/gzlLchj/addHjan`, 'post', data);

/**
 * 环节流程删除
 * @param data
 * @returns {AxiosPromise}
 */
export const lclistDelete = (data) => createAPI(`${APP_PRE}/gzlLchj/delete`, 'post', data);

/**
 * 目标环节list
 * @param data
 * @returns {AxiosPromise}
 */
export const lcmblistFind = (data) => createAPI(`${APP_PRE}/gzlLchj/findMbhjxxList`, 'get', data);

/**
 * 目标环节删除
 * @param data
 * @returns {AxiosPromise}
 */
export const lcmblistDelete = (data) => createAPI(`${APP_PRE}/gzlLchj/deleteMbhj`, 'post', data);

/**
 * 流程类型保存
 * @param data
 * @returns {AxiosPromise}
 */
export const lclxSave = (data) => createAPI(`${APP_PRE}/gzlLchj/addBllx`, 'post', data);
/**
 * 获取处理目标
 * @param data
 * @returns {AxiosPromise}
 */
export const clmbFind = (data) => createAPI(`${APP_PRE}/gzlLchj/findClmb`, 'get', data);
/**
 * 选择岗位类型列表弹窗
 * @param data
 * @returns {AxiosPromise}
 */
export const lcmxPostListFind = (data) => createAPI(`${APP_PRE}/gzlLchj/findGwlx`, 'get', data);
/**
 * 组织结构获取 treeData
 * @param data
 * @returns {AxiosPromise}
 */
export const organizationFind = (data) => createAPI(`${APP_PRE}/gzlLchj/findBmList`, 'get', data);
/**
 * 组织结构详情获取
 * @param data
 * @returns {AxiosPromise}
 */
export const organizationDetail = (data) => createAPI(`${APP_PRE}/gzlLchj/findBmxgwlb`, 'get', data);

export const organizationPeopleDetail = (data) => createAPI(`${APP_PRE}/gzlLchj/findYhListByPageBmm`, 'get', data);
/**
 * 保存流程明细
 * @param data
 * @returns {AxiosPromise}
 */
export const lcmxSave = (data) => createAPI(`${APP_PRE}/gzlLchj/addClmbxx`, 'post', data);
