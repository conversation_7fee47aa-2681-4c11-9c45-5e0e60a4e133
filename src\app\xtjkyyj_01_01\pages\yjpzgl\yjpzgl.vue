<template>
  <div class="yjpzgl-content">
    <div class="yjpzgl-content-main">
      <v-title name="预警配置管理"></v-title>
      <yjpzgl-search ref="searchElement" @search="search"></yjpzgl-search>
      <div class="button-list qran">
        <el-button type="primary" size="small" @click="addYjpz()">新增</el-button>
      </div>
      <div class="table">
        <div class="table-content">
          <yjpzgl-table :scroller-height="scrollerHeight" :table-data="tableData" @modifyYjpz="modifyYjpz" @delYjpz="delYjpz" @changeKg="changeKg"></yjpzgl-table>
        </div>
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>

        <!--新增修改弹窗-->
        <el-dialog
          ref="dialogEdit"
          customClass="zhxy-dialog-view"
          :visible.sync="addDialogVisible"
          :title="dialogType === 'edit'?'修改预警配置':'新增预警配置'"
          :close-on-click-modal="false" @close="closeYjpz('yjpzForm')">
          <yjpzxx-edit ref="dialogEditContent" :form-data="yjpz" :yjbs-visible="yjbsVisible"></yjpzxx-edit>
          <div slot="footer" class="dialog-footer" style="margin-right: 20px;">
            <el-button type="primary" size="small" @click="confirmYjpz('yjpzForm')">确定</el-button>
            <el-button type="" size="small" @click="closeYjpz('yjpzForm')">取消</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import { cloneDeep } from 'lodash';
import {
  findYjpzList, delYjpz, findOneByYjbs, addYjpz, updateYjpz, changeKg
} from '@/app/xtjkyyj_01_01/api/yjpzgl/yjpzgl.js';
import YjpzglSearch from './yjpzglComponents/YjpzglSearch';
import YjpzglTable from './yjpzglComponents/YjpzglTable';
import YjpzxxEdit from './yjpzglComponents/YjpzxxEdit';

const defaultYjpz = {
  yjlb: '',
  yjlbmc: '',
  yjfl: '',
  yjflmc: '',
  yjbs: '',
  yjmc: '',
  yjsm: '',
  yjjbm: '',
  yjjbmc: '',
  yjkg: 1,
  txrynum: '',
  yzkzzd01: '',
  yzkzzd02: ''
};

export default {
  name: 'yjpzgl',
  components: {
    VTitle,
    YjpzglSearch,
    YjpzglTable,
    YjpzxxEdit
  },
  data() {
    return {
      scrollerHeight: 0,
      scrollerHeight2: 300,
      currentPage: 1, // 初始页
      yjpz: { ...defaultYjpz },
      tableData: [
        {
          yjlb: '',
          yjfl: '',
          yjbs: '',
          yjmc: '',
          yjsm: '',
          yjjbm: '',
          yjjbmc: '',
          yjkg: 1,
          txrynum: 0,
          yzkzzd01: '',
          yzkzzd02: ''
        }
      ], // 列表数据集合
      yjbsVisible: true,
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      addDialogVisible: false, // 弹窗是否可见
      dialogType: 'new', // 弹窗名称控制
      options: {
        type: Array,
        default: () => []
      }
    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 监听页面尺寸改变table size
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight - height - 220;
    },
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        yjlb: null,
        yjfl: null,
        yjjbm: null,
        yjmc: null
      };
      if (params) {
        Object.assign(param, params);
      }
      findYjpzList(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    reset() {
      this.listQuery.yjlb = null;
      this.listQuery.yjfl = null;
      this.listQuery.yjjbm = null;
      this.listQuery.yjmc = null;
      this.search();
    },
    addYjpz() {
      this.yjpz = { ...defaultYjpz };
      this.dialogType = 'new';
      this.yjbsVisible = true;
      this.addDialogVisible = true;
    },
    closeYjpz(formName) {
      this.$refs.dialogEditContent.$refs[formName].clearValidate();
      this.addDialogVisible = false;
    },
    modifyYjpz(row) {
      this.addDialogVisible = true;
      this.dialogType = 'edit';
      this.yjbsVisible = false;
      Object.assign(this.yjpz, row);
    },
    changeKg(row) {
      const params = {
        yjbs: row.yjbs,
        yjkg: row.yjkg
      };
      this.$confirm('确认修改预警开关？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            changeKg(params).then((res) => {
              if (res.code === 200) {
                this.$message.success('修改成功');
                this.search();
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    delYjpz(row) {
      const yjbs = row.yjbs || ''; // yjbs
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            delYjpz({ yjbs }).then((res) => {
              if (res.code === 200) {
                const index = this.tableData.findIndex((item) => item.yjbs === yjbs);
                if (index > -1) {
                  this.tableData.splice(index, 1);
                }
                this.$message.success('删除成功');
                this.search();
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    confirmYjpz(formName) {
      // eslint-disable-next-line consistent-return
      this.$refs.dialogEditContent.$refs.yjpzForm.validate((valid) => {
        if (valid) {
          if (this.dialogType !== 'edit') {
            const param = {
              yjbs: this.yjpz.yjbs
            };
            findOneByYjbs(param).then((res) => {
              if (Object.keys(res.data).length === 0) {
                const params = cloneDeep(this.yjpz);
                addYjpz(params).then((result) => {
                  this.search();
                  this.$message.success('新增成功！');
                }).finally(() => {
                  this.closeYjpz(formName);
                });
              } else {
                this.$message.error('该预警标识已存在！');
              }
            });
          } else {
            const params = cloneDeep(this.yjpz);
            updateYjpz(params).then((result) => {
              this.search();
              this.$message.success('修改成功！');
            }).finally(() => {
              this.closeYjpz(formName);
            });
          }
        } else {
          this.$message.error('请重新填写信息！');
          return false;
        }
      });
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        yjlb: this.$refs.searchElement.listQuery.yjlb,
        yjfl: this.$refs.searchElement.listQuery.yjfl,
        yjjbm: this.$refs.searchElement.listQuery.yjjbm,
        yjmc: this.$refs.searchElement.listQuery.yjmc
      };
      this.search(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage, // 页码
        yjlb: this.$refs.searchElement.listQuery.yjlb,
        yjfl: this.$refs.searchElement.listQuery.yjfl,
        yjjbm: this.$refs.searchElement.listQuery.yjjbm,
        yjmc: this.$refs.searchElement.listQuery.yjmc
      };
      this.search(param);
    }
  }
};
</script>
<style lang="scss" scoped>
  .yjpzgl-content {
    &-main{
      background-color: #ffffff;
      padding: $page-content-padding;
    }
    .table {
      &-content {
        margin-top: $page-content-margin;
      }
    }
    .dialog-footer{
      text-align: center;
      display: flex;
      justify-content: flex-end;
    }
    .qran{
      margin-top: -3.1rem;
      margin-right: 1rem;
      float: right;
    }
  }
</style>
