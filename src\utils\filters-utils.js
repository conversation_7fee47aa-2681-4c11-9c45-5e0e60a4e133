/**
 * url params解析
 * @param {string} url
 * @returns {Object}
 */
export const getQueryObject = (url) => {
  const urlReal = url == null ? window.location.href : url;
  const search = urlReal.substring(url.lastIndexOf('?') + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
};
/**
 * 格式化处理字符串
 * @param str
 * @param size
 * @param delimiter
 * @returns {*}
 * ecDo.formatText('1234asda567asd890',4,' ')
 * result："1 234a sda5 67as d890"
 */
const formatText = (str, size, delimiter) => {
  const _size = size || 3; const
    _delimiter = delimiter || ',';
  const regText = `\\B(?=(\\w{${_size }})+(?!\\w))`;
  const reg = new RegExp(regText, 'g');
  return str.replace(reg, _delimiter);
};
/**
 * 现金额大写转换函数
 * @param n
 * @returns {string}
 * ecDo.upDigit(168752632)
 *  result："人民币壹亿陆仟捌佰柒拾伍万贰仟陆佰叁拾贰元整"
 */
export const upDigit = (n) => {
  const fraction = ['角', '分', '厘'];
  const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const unit = [
    ['元', '万', '亿'],
    ['', '拾', '佰', '仟']
  ];
  n = Math.abs(n);
  let s = '';
  for (let i = 0; i < fraction.length; i++) {
    // eslint-disable-next-line no-restricted-properties
    s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
  }
  s = s || '整';
  n = Math.floor(n);
  for (let i = 0; i < unit[0].length && n > 0; i++) {
    let p = '';
    for (let j = 0; j < unit[1].length && n > 0; j++) {
      p = digit[n % 10] + unit[1][j] + p;
      n = Math.floor(n / 10);
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    // s = p + unit[0][i] + s;
  }
  return s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
};
/**
 * 字母大小写切换
 * @param str
 * @param type 1:首字母大写  2：首页母小写 3：大小写转换  4：全部大写 5：全部小写
 * @returns {string|*}
 */
export const changeCase = (str, type) => {
  const ToggleCase = (strs) => {
    let itemText = '';
    strs.split('').forEach(
      (item) => {
        if (/^([a-z]+)/.test(item)) {
          itemText += item.toUpperCase();
        } else if (/^([A-Z]+)/.test(item)) {
          itemText += item.toLowerCase();
        } else {
          itemText += item;
        }
      }
    );
    return itemText;
  };
  switch (type) {
    case 1:
      return str.replace(/\b\w+\b/g, (word) => word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase());
    case 2:
      return str.replace(/\b\w+\b/g, (word) => word.substring(0, 1).toLowerCase() + word.substring(1).toUpperCase());
    case 3:
      return ToggleCase(str);
    case 4:
      return str.toUpperCase();
    case 5:
      return str.toLowerCase();
    default:
      return str;
  }
};

/**
 * 保留2位小数
 * @param x
 * @returns {string|boolean}
 */
export const toDecimal2 = (x) => {
  let f = parseFloat(x);
  if (Number.isNaN(f)) {
    return false;
  }
  f = Math.round(x * 100) / 100;
  let s = f.toString();
  let rs = s.indexOf('.');
  if (rs < 0) {
    rs = s.length;
    s += '.';
  }
  while (s.length <= rs + 2) {
    s += '0';
  }
  return s;
};
/**
 * 字符串循环复制,count->次数
 * @param str
 * @param count
 * @returns {string}
 */
export const repeatStr = (str, count) => {
  let text = '';
  for (let i = 0; i < count; i++) {
    text += str;
  }
  return text;
};
/**
 * 字符串替换
 * @param str
 * @param AFindText
 * @param ARepText
 * @returns {*}
 */
const replaceAll = (str, AFindText, ARepText) => {
  const raRegExp = new RegExp(AFindText, 'g');
  return str.replace(raRegExp, ARepText);
};

//
// replaceStr(字符串,字符格式, 替换方式,替换的字符（默认*）)
// ecDo.replaceStr('18819322663',[3,5,3],0)
// result：188*****663
// ecDo.replaceStr('asdasdasdaa',[3,5,3],1)
// result：***asdas***
// ecDo.replaceStr('1asd88465asdwqe3',[5],0)
// result：*****8465asdwqe3
// ecDo.replaceStr('1asd88465asdwqe3',[5],1,'+')
// result："1asd88465as+++++"
/**
 *
 * @param str
 * @param regArr
 * @param type
 * @param ARepText
 * @returns {null|*}
 */
export const replaceStr = (str, regArr, type, ARepText) => {
  let regtext = '';
  let Reg = null;
  const replaceText = ARepText || '*';
  // repeatStr是在上面定义过的（字符串循环复制），大家注意哦
  if (regArr.length === 3 && type === 0) {
    regtext = `(\\w{${ regArr[0] }})\\w{${ regArr[1] }}(\\w{${ regArr[2] }})`;
    Reg = new RegExp(regtext);
    const replaceCount = this.repeatStr(replaceText, regArr[1]);
    return str.replace(Reg, `$1${ replaceCount }$2`);
  }
  if (regArr.length === 3 && type === 1) {
    regtext = `\\w{${ regArr[0] }}(\\w{${ regArr[1] }})\\w{${ regArr[2] }}`;
    Reg = new RegExp(regtext);
    const replaceCount1 = this.repeatStr(replaceText, regArr[0]);
    const replaceCount2 = this.repeatStr(replaceText, regArr[2]);
    return str.replace(Reg, `${replaceCount1 }$1${ replaceCount2}`);
  }
  if (regArr.length === 1 && type === 0) {
    regtext = `(^\\w{${ regArr[0] }})`;
    Reg = new RegExp(regtext);
    const replaceCount = this.repeatStr(replaceText, regArr[0]);
    return str.replace(Reg, replaceCount);
  }
  if (regArr.length === 1 && type === 1) {
    regtext = `(\\w{${ regArr[0] }}$)`;
    Reg = new RegExp(regtext);
    const replaceCount = this.repeatStr(replaceText, regArr[0]);
    return str.replace(Reg, replaceCount);
  }
  return null;
};
/**
 * 配合filter 递归查找 父级是否存在
 * @param node
 * @param _array
 * @param value
 */
const getReturnNode = (node, _array, value) => {
  const isPass = node.data && node.data.bmmc && node.data.bmmc.indexOf(value) !== -1;
  // eslint-disable-next-line no-unused-expressions
  isPass ? _array.push(isPass) : '';
  if (!isPass && node.level !== 1 && node.parent) {
    getReturnNode(node.parent, _array, value);
  }
};
/**
 * 过滤节点 elementui tree组件 filterNode
 * @param value
 * @param data
 * @param node
 * @returns {boolean}
 */
export const filterNode = (value, data, node) => {
  if (!value) {
    return true;
  }
  const _array = [];// 这里使用数组存储 只是为了存储值。
  getReturnNode(node, _array, value);
  let result = false;
  _array.forEach((item) => {
    result = result || item;
  });
  return result;
};
