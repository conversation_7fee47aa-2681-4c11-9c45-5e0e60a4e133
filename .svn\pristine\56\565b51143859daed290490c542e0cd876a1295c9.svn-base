<template>
  <div class="zhxy-drawer-icon">
    <el-drawer
      :title="title"
      :visible.sync="visible"
      :direction="'rtl'"
      :before-close="handleClose">
      <div class="content">
        <div class="content-button">
          <el-button @click="setIcon" type="primary" size="small">确定</el-button>
          <el-button type="" size="small" @click="()=>{visible=false}">取消</el-button>
        </div>
        <div class="content-main">
          <ul class="icon-list">
            <li :class="checkIconName === item.label ? 'checked':''" v-for="item in iconList" :key="item.label" @click="checkIcon(item)">
              <i :class="item.label"></i>
              <span>{{item.label}}</span>
              <span v-show="item.checked" class="checked-icon"></span>
            </li>
          </ul>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'IconTreeDialog',
  props: {
    title: {
      type: String,
      default: '图标集合'
    },
    checkedName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // dialog 显隐
      visible: false,
      checkIconName: '',
      iconList: [
        {
          label: 'el-icon-user-solid'
        },
        {
          label: 'el-icon-phone'
        },
        {
          label: 'el-icon-phone-outline'
        },
        {
          label: 'el-icon-more'
        },
        {
          label: 'el-icon-s-tools'
        },
        {
          label: 'el-icon-star-on'
        },
        {
          label: 'el-icon-s-goods'
        },
        {
          label: 'el-icon-warning'
        },
        {
          label: 'el-icon-question'
        },
        {
          label: 'el-icon-info'
        },
        {
          label: 'el-icon-s-help'
        },
        {
          label: 'el-icon-picture'
        },
        {
          label: 'el-icon-upload'
        },
        {
          label: 'el-icon-camera-solid'
        },
        {
          label: 'el-icon-video-camera-solid'
        },
        {
          label: 'el-icon-message-solid'
        },
        {
          label: 'el-icon-s-cooperation'
        },
        {
          label: 'el-icon-s-order'
        },
        {
          label: 'el-icon-s-platform'
        },
        {
          label: 'el-icon-s-operation'
        },
        {
          label: 'el-icon-s-promotion'
        },
        {
          label: 'el-icon-s-home'
        },
        {
          label: 'el-icon-s-release'
        },
        {
          label: 'el-icon-s-ticket'
        },
        {
          label: 'el-icon-s-management'
        },
        {
          label: 'el-icon-s-open'
        },
        {
          label: 'el-icon-s-shop'
        },
        {
          label: 'el-icon-s-marketing'
        },
        {
          label: 'el-icon-s-flag'
        },
        {
          label: 'el-icon-s-comment'
        },
        {
          label: 'el-icon-s-finance'
        },
        {
          label: 'el-icon-s-claim'
        },
        {
          label: 'el-icon-s-opportunity'
        },
        {
          label: 'el-icon-s-data'
        },
        {
          label: 'el-icon-s-check'
        },
        {
          label: 'el-icon-s-grid'
        },
        {
          label: 'el-icon-menu'
        },
        {
          label: 'el-icon-share'
        },
        {
          label: 'el-icon-location'
        },
        {
          label: 'el-icon-date'
        },
        {
          label: 'el-icon-edit-outline'
        },
        {
          label: 'el-icon-tickets'
        },
        {
          label: 'el-icon-document-copy'
        },
        {
          label: 'el-icon-document-checked'
        },
        {
          label: 'el-icon-printer'
        },
        {
          label: 'el-icon-reading'
        },
        {
          label: 'el-icon-collection-tag'
        },
        {
          label: 'el-icon-collection'
        },
        {
          label: 'el-icon-files'
        },
        {
          label: 'el-icon-notebook-1'
        },
        {
          label: 'el-icon-notebook-2'
        },
        {
          label: 'el-icon-office-building'
        }, {
          label: 'el-icon-school'
        },
        {
          label: 'el-icon-shopping-cart-1'
        },
        {
          label: 'el-icon-guide'
        },
        {
          label: 'el-icon-coin'
        },
        {
          label: 'el-icon-link'
        },
        {
          label: 'el-icon-set-up'
        },
        {
          label: 'el-icon-medal'
        },
        {
          label: 'el-icon-trophy'
        },
        {
          label: 'el-icon-alarm-clock'
        },
        {
          label: 'el-icon-lock'
        },
        {
          label: 'el-icon-unlock'
        },
        {
          label: 'el-icon-fork-spoon'
        },
        {
          label: 'el-icon-orange'
        },
        {
          label: 'el-icon-odometer'
        },
        {
          label: 'el-icon-pie-chart'
        },
        {
          label: 'el-icon-mobile'
        },
        {
          label: 'el-icon-search'
        }
      ]
    };
  },
  // watch: {
  //   checkedName: {
  //     handler(val) {
  //       this.checkIconName = val;
  //     },
  //     immediate: true
  //   }
  // },
  methods: {
    showDialog() {
      this.visible = true;
      this.$nextTick(() => {
        this.checkIconName = this.checkedName;
      });
    },
    hideDialog() {
      this.visible = false;
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done();
        })
        .catch((_) => {
        });
    },
    checkIcon(val) {
      this.checkIconName = val.label;
    },
    setIcon() {
      this.$emit('setIcon', this.checkIconName);
    }
  }
};
</script>

<style lang="scss" scoped>
.zhxy-drawer-icon{
  .content {
    padding: $page-content-padding;
    height: 100%;

    &-button {
      padding-bottom: 2*$page-content-padding;
    }

    &-main {
      height: calc(100% - 52px);
      overflow: auto;
      .icon-list{
        li {
          position: relative;
          float: left;
          width: 78px;
          text-align: center;
          height: 120px;
          color: #000000;
          font-size: $page-font-size;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-right: 1px solid #eee;
          border-bottom: 1px solid #eee;
          margin-right: -1px;
          margin-bottom: -1px;
          cursor: pointer;
          overflow: hidden;
          &.checked{
            color: $page-font-hover-color;
            &:after{
              content: '';
              display: block;
              position: absolute;
              background-color: #4fe3c1;
              width: 20px;
              height: 20px;
              top: -10px;
              right: -10px;
              transform: rotateZ(45deg);
            }
          }
          &:hover{
            color: $page-font-hover-color;
          }
          i{
            margin: 15px 0;
            font-size: $title-font-size;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.zhxy-drawer-icon{
  .el-drawer__body{
    overflow: hidden;
  }
}
</style>
