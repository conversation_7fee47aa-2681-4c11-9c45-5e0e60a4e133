<template>
  <div ref="myChart" style="width:100%; height:100%"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/bar');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  props: {
    list: {
      type: Object
    },
    msg: {
      type: String
    },
    title: {
      type: String
    }
  },
  data() {
    return {
      myChart: null,
      myChart64: null
    };
  },
  watch: {
    list(val) {
      this.drawLine();
    },
    title(val) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      const { list, title } = this;
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(this.$refs.myChart);

      const color = '#189cbb';

      const scale = 1;

      const option = {
        animation: false,
        title: {
          text: title,
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
            fontFamily: '仿宋_GB2312',
            color: '#333'
          },
          left: 'center',
          top: '5%'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        /* legend: {
          data:['传输次数'],
          textStyle: {
            color: '#333'
          },
          y:'92%',
        }, */
        grid: {
          // left:'10%',
          top: '20%',
          bottom: '25%',
          left: '15%'
        },
        xAxis: {
          data: list.name,
          axisLabel: {
            type: 'category',
            interval: 0,
            rotate: 35,
            show: true,
            textStyle: {
              fontSize: 11,
              color: '#333'
            }
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333'
            }
          }
        },
        yAxis: {
          splitLine: {
            show: true,
            lineStyle: {
              color: '#224d6f',
              width: 0.5
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#ddd'
            },
            textStyle: {
              fontSize: 11,
              color: '#ddd'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 11,
              color: '#333'
            }
          }
        },
        series: [{
          name: '传输次数',
          barWidth: 15, // 固定柱子宽度
          type: 'bar',
          data: list.value,
          itemStyle: { // 上方显示数值
            normal: {
              label: {
                show: true, // 开启显示
                position: 'top', // 在上方显示
                textStyle: { // 数值样式
                  color: '#333'
                }
              }
            }
          }
        }]
      };
      this.myChart.setOption(option);
      setTimeout(() => {
        this.$emit('sendMychart', this.msg);
        // alert(1)
      }, 3000);
    },
    // 生成64位图片
    get64Bata() {
      if (typeof (this.myChart) === 'undefined') {
        this.myChart64 = '';
      } else {
        this.myChart64 = this.myChart.getDataURL({ type: 'png' }).split(',')[1];
      }
      return this.myChart64;
    }
  }

};
</script>

<style>
</style>
