<template>
  <div class="department-user" ref="element">
    <el-button type="primary" size="small" @click="buttonAction"> {{isEdit ? '编辑' :'保存'}}
    </el-button>
    <el-form :disabled="isEdit" label-width="150px" :model="formData" :rules="rules" class="zhxy-form zhxy-form-search-part" :class="isEdit ? '' : 'form-status-edit'">
      <el-form-item style="display: none">
        <span class="zhxy-form-label" slot="label">功能资源ID</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.gnzyid" placeholder="功能资源ID" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="gnzymc">
        <span class="zhxy-form-label" slot="label">功能资源名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.gnzymc" placeholder="功能资源名称" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="zymchz">
        <span class="zhxy-form-label" slot="label">资源名称后缀</span>
        <el-input class="zhxy-form-inline" v-model="formData.zymchz" placeholder="资源名称后缀" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="qqlj">
        <span class="zhxy-form-label" slot="label">请求路径</span>
        <el-input class="zhxy-form-inline" v-model="formData.qqlj" placeholder="请求路径" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="pxh">
        <span slot="label" class="zhxy-form-label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh" placeholder="排序号" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="sfsqxxz">
        <span class="zhxy-form-label" slot="label">是否受权限限制</span>
        <el-radio-group v-model="formData.sfsqxxz">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="szhj">
        <span class="zhxy-form-label" slot="label">宿主环境</span>
        <el-radio-group v-model="formData.szhj">
          <el-radio :label="1">PC</el-radio>
          <el-radio :label="2">移动</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="dbssxyybs">
        <span class="zhxy-form-label" slot="label">待办所属小应用标识</span>
        <el-input class="zhxy-form-inline" v-model="formData.dbssxyybs" placeholder="待办所属小应用标识" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="dblxbs">
        <span class="zhxy-form-label" slot="label">待办类型标识</span>
        <el-input class="zhxy-form-inline" v-model="formData.dblxbs" placeholder="待办类型标识" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjr" placeholder="创建人" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjsj" placeholder="创建时间" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgr" placeholder="变更人" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgsj" placeholder="变更时间" size="small"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { update } from '../../../api/zygl/zygl';

export default {
  name: 'DepartmentDetail',
  data() {
    return {
      // 是否可编辑
      isEdit: true,
      // 部门详情表单数据
      formData: {
        gnzyid: '',
        gnzymc: '',
        zymchz: '',
        qqlj: '',
        fcdid: '',
        pxh: '',
        sfsqxxz: '',
        szhj: '',
        dbssxyybs: '',
        dblxbs: '',
        gzlgwtbfs: ''
      },
      // 规则
      rules: {
        gnzymc: [{
          required: true,
          trigger: 'blur',
          message: '请输入功能资源名称'
        },
        {
          max: 100,
          message: '功能资源名称长度不能多于100位'
        }
        ],
        sfsqxxz: [{
          required: true,
          trigger: 'blur',
          message: '请选择是否受权限限制'
        }
        ],
        szhj: [{
          required: true,
          trigger: 'blur',
          message: '请选择宿主环境'
        }
        ],
        zymchz: [
          {
            max: 50,
            message: '资源名称后缀长度不能多于50位,加（）显示'
          }
        ],
        qqlj: [
          {
            max: 100,
            message: '请求路径长度不能多于100位'
          }
        ],
        pxh: [
          {
            max: 5,
            message: '排序号长度不能多于5位'
          }
        ],
        dbssxyybs: [
          {
            max: 100,
            message: '待办所属小应用标识长度不能多于100位'
          }
        ],
        dblxbs: [
          {
            max: 100,
            message: '待办类型标识长度不能多于100位'
          }
        ]
      }
    };
  },
  methods: {
    buttonAction() {
      // 切换编辑状态
      this.isEdit = !this.isEdit;
      if (this.isEdit === true) {
        const param = {
          gnzyid: this.formData.gnzyid,
          gnzymc: this.formData.gnzymc,
          qqlj: this.formData.qqlj,
          zymchz: this.formData.zymchz,
          pxh: this.formData.pxh,
          sfsqxxz: this.formData.sfsqxxz,
          szhj: this.formData.szhj,
          dbssxyybs: this.formData.dbssxyybs,
          dblxbs: this.formData.dblxbs,
          gzlgwtbfs: this.formData.gzlgwtbfs
        };
        /**
         * 修改 资源明细 接口
         */
        update(param).then((res) => {
          this.$message.success('修改成功');
        }).finally(() => {
          this.loading = false;
        });
      }
    }
  }
};
</script>
<style lang="scss">
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
      cursor: default;
    }
  }
</style>
<style lang="scss" scoped>
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
    }
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }

    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
