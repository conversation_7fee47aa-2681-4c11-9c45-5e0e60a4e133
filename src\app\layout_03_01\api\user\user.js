import { createAPI } from '@/utils/request.js';

const BASE_URL = '';

/**
 * 获取tok
 * @param data
 * @returns {AxiosPromise}
 */
export const getTokens = (data) => createAPI(`${BASE_URL}/permiss/getToken`, 'post', data);
/**
 * 获取资源列表
 * @param data
 * @returns {AxiosPromise}
 */
// export const getRoleList = (data) => createAPI(`${BASE_URL}/roleList`, 'get', data);
export const getRoleList = (data) => createAPI(`${BASE_URL}/layout_03_01/home/<USER>'get', data);

/**
 * 获取菜单列表
 * @param data
 * @returns {AxiosPromise}
 */
// export const getMenuList = (data) => createAPI(`${BASE_URL}/menuList`, 'get', data);
export const getMenuList = (data) => createAPI(`${BASE_URL}/layout_03_01/home/<USER>'get', data);
/**
 * 获取用户基础信息
 * @param data
 * @returns {AxiosPromise}
 */
export const getUserBaseInfo = (data) => createAPI(`${BASE_URL}/layout_03_01/home/<USER>'get', data);

/**
 * 到业务应用系统获取挑战数的地址
 * @param data
 * @returns {AxiosPromise}
 */
export const challengeUrl = (data) =>
  createAPI(`${BASE_URL}/sys/xtgl/sso/getRandom`, 'post', data);

/**
 * 到业务应用系统获取挑战数的地址
 * @param data
 * @returns {AxiosPromise}
 */
export const getIdauthUrl = (data) =>
  createAPI(`/sys/xtgl/sso/getUserInfo`, 'post', data);
