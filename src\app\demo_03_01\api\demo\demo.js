import { createAPI } from '@/utils/request.js';

// 接口地址映射
const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/demo_03_01`;

/**
 * 上传文件
 * @param data
 * @returns {AxiosPromise}
 */
export const minioUpload = (data, config) => createAPI(`${APP_PRE }/demo007Upload/minio-upload`, 'post', data, config);
/**
 * 下载文件
 * @param data
 * @returns {AxiosPromise}
 */
export const minioDownload = () => createAPI(`${APP_PRE }/demo007Upload/minio-download`, 'get');

export const minioDelete = () => createAPI(`${APP_PRE }/demo007Upload/minio-delete`, 'post');
