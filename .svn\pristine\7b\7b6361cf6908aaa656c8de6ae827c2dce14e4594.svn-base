<template>
  <div>
    <div class="dybzx-page-content">
      <div class="jksqgl-pageform-header">
        <span class="zhxy-form-label" slot="label">接口列表</span>
      </div>
    </div>
    <el-table :data="tablejklist" border style="width: 100%">
      <el-table-column fixed="left" label="接口名称" width="400">
        <template>
          <el-button type="text" size="small">
            待办事项获取
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tyqx" label="调用权限" width="180"></el-table-column>
      <el-table-column prop="cjsj" label="创建时间"></el-table-column>
      <el-table-column fixed="right" label="操作" width="400">
        <template>
          <el-button type="text" size="small">
            禁止
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'jksqgljklb',
  props: {
    tablejklist: {
      type: Array,
      default: () => ([])
    }
  }
};
</script>

<style lang="scss" scoped>
.dybzx-page-content {
  background-color: #FFFFFF;
  display: flex;
  flex-wrap: wrap;

  .button-list {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
    padding-right: 2%;
    height: 32px;
  }

  .dybzx-page-content-box {
    width: 25%;
    padding: 0 2%;
  }

  .dybzx-page-content-card {
    background-color: #FFFFFF;
    width: 100%;
    height: 228px;
    border: 1px solid #EAEAEA;
    box-shadow: #ededed 0 0 5px 1px;
    position: relative;
    overflow: hidden;
    margin-top: 20px;

    .card-tip {
      position: absolute;
      top: 10px;
      right: -30px;
      width: 120px;
      background-color: #286fb7;
      color: #FFFFFF;
      font-size: 16px;
      padding: 7px 0;
      text-align: center;
      transform: rotate(45deg);
    }

    .card-top {
      display: flex;
      align-items: center;
      background-color: #F2F2F2;
      height: 52px;
      padding: 0 10px;

      .card-top-icon {
        width: 26px;
        height: 26px;
        background: url("../../../assets/icon-xitong.png") no-repeat;
        background-size: cover;
      }

      .card-top-title {
        font-size: 16px;
        color: #2d8cf0;
        padding-left: 10px;
      }

    }

    .card-middle {
      padding: 20px 30px;

      .card-middle-item {
        display: flex;
        line-height: 30px;

        .card-middle-item-label {
          width: 80px;
          flex-shrink: 0;
          color: #90a4af;
        }

        .card-middle-item-value {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

    }

    .card-bottom {
      display: flex;
      height: 45px;

      .card-bottom-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 50%;
        border-top: 1px solid #EAEAEA;
        cursor: pointer;
      }

      .card-bottom-btn:hover {
        opacity: 0.8;
      }

      .card-bottom-left {
        border-right: 1px solid #EAEAEA;
        color: #286fb7;
      }

      .card-bottom-right {
        color: #90a4af;
      }
    }
  }
}

.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    margin-top: 20px;
    padding: 0 10px;
    box-sizing: border-box;
  }
}
</style>
