<template>
  <div class="dialog-content">
    <el-form
      ref="editForm"
      :model="formData"
      :rules="rules"
      :disabled="formDisabled"
      label-width="100px"
      label-position="right">
      <el-form-item label="账号" prop="zhid">
        <el-input :disabled="zhDisabled" v-model="formData.zhid" placeholder="账号" size="small"/>
      </el-form-item>
      <el-form-item label="姓名" prop="xm">
        <el-input v-model="formData.xm" placeholder="姓名" size="small"/>
      </el-form-item>
      <el-form-item label="电话号" prop="sjh">
        <el-input v-model="formData.sjh" placeholder="电话号" size="small"/>
      </el-form-item>
      <el-form-item label="身份证号" prop="sfzh">
        <el-input v-model="formData.sfzh" placeholder="身份证号" size="small"/>
      </el-form-item>
      <el-form-item label="电子邮箱" prop="yx">
        <el-input v-model="formData.yx" placeholder="邮箱" size="small"/>
      </el-form-item>
      <el-form-item label="有效截止时间" prop="gqsj">
        <el-date-picker
          :style="`width: 100%`"
          size="small"
          v-model="formData.gqsj"
          type="datetime"
          placeholder="选择日期时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="isAddForm" label="密码" prop="mm">
        <el-input v-model="formData.mm" placeholder="密码" size="small"/>
      </el-form-item>

      <el-form-item prop="zhzt" label="状态">
        <el-select
          style="width: 100%"
          v-model="formData.zhzt"
          placeholder="用户状态"
          size="small">
          <el-option
            v-for="item in zhztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => ({
        zhid: '111',
        xm: '222',
        mm: '333',
        sjh: '',
        yx: '',
        gqsj: '',
        sfzh: 1,
        zhzt: 1
      })
    },
    dialogType: {
      type: String,
      default: () => 'new'
    },
    mmRegular: {
      type: Object,
      default: () => ({
        pattern: /(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[\W])(?=.*[\S])^[0-9A-Za-z\S]{8,16}$/g,
        message: '请输入正确的密码格式（必须8-16位且包含大写字母,小写字母,数字,特殊符号）'
      })
    }
  },
  data() {
    // 用户ID校验
    // eslint-disable-next-line consistent-return
    const validateYhid = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入账号'));
      }
      callback();
    };

    // 姓名校验
    // eslint-disable-next-line consistent-return
    const validateXm = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入姓名'));
      }
      callback();
    };
    return {
      zhztOptions: [{
        label: '停用',
        value: 2
      }, {
        label: '启用',
        value: 1
      }, {
        label: '锁定',
        value: 3
      }],
      rules: {
        zhid: [{
          required: true,
          trigger: 'blur',
          validator: validateYhid
        }],
        zhzt: [{
          required: true,
          trigger: 'change',
          message: '请输入状态'
        }],
        xm: [{
          required: true,
          trigger: 'blur',
          validator: validateXm
        }],
        mm: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入密码'
          },
          {
            pattern: /(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[\W])(?=.*[\S])^[0-9A-Za-z\S]{8,16}$/g,
            message: '请输入正确的密码格式（必须8-16位且包含大写字母,小写字母,数字,特殊符号）'
          }
        ],
        sfzh: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入身份证号'
          }
        ]
      }
    };
  },
  computed: {
    // 表单的 查看状态
    formDisabled() {
      return this.dialogType === 'detail';
    },
    // 表单 账号编辑状态
    zhDisabled() {
      return this.dialogType === 'edit';
    },
    // 表单 新增状态
    isAddForm() {
      return this.dialogType === 'new';
    }
  },
  watch: {
    mmRegular: {
      handler(val) {
        if (val.message) {
          this.rules.mm[1].message = val.message;
          this.rules.mm[1].pattern = val.pattern;
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-content {
  max-height: 510px;
  overflow: auto;
  padding: 0 10px;
}
</style>
