import Vue from 'vue';
import JsonExcel from 'vue-json-excel'; // excel 导出
import App from './App.vue';
import router from './router';
import store from './store';
import './app/layout_03_01/utils/permission'; // 登陆权限配置
import './utils/elementImport'; // 按需加载elementUI
import './styles/reset.css'; // 按需加载elementUI
import './styles/zhxy-default.scss';
import 'default-passive-events';

// 判断是否是mock 环境 加载mockjs
if (process.env.VUE_APP_MODE === 'mock') {
  require('@/app/layout_03_01/mock/user/user.js');
}

Vue.component('downloadExcel', JsonExcel);

Vue.config.productionTip = false;

// vue 异常捕获
Vue.config.errorHandler = (err, vm, info) => {
  // 异常操作
  console.error('vue异常捕获:', err, vm, info);
};

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app');
