<template>
  <div ref="myChart" style="width: 100%;height: 100%;box-sizing: border-box;"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/bar');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');

export default {
  props: {
    data: {
      type: Object
    },
    color: {
      type: String,
      default() {
        return '#3398DB';
      }
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    list(val) {
      this.drawLine();
    },
    title(val) {
      this.drawLine();
    },
    data(val) {
      this.drawLine();
    }

  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);

      const option = {
        // title: {
        //         text: this.data.title,
        //     },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: (param, ticket, callback) => {
            let str = '';
            // eslint-disable-next-line no-restricted-syntax
            for (const item of param) {
              // eslint-disable-next-line no-unused-expressions
              item.seriesName && (str += `${item.marker} ${item.seriesName}：${item.value}<br>`);
            }

            return str;
          }
        },
        legend: {
          data: this.data.lengend,
          right: '20px',
          top: '20px'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          data: this.data.xData,
          axisLabel: {
            // inside: true,// 显示在内部
            textStyle: {
              // color: '#fff'
            },
            rotate: 40
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#999'
            }
          }
        },
        series: [{
          name: '新增',
          type: 'bar',
          color: '#76DA91',
          barWidth: '50%',
          stack: '总量',
          data: this.data.gw
        },
        {
          name: '变更',
          type: 'bar',
          stack: '总量',
          color: '#63B2EE',
          barWidth: '50%',

          data: this.data.zd
        },

        {
          name: '',
          type: 'bar',
          data: this.data.zl,
          stack: '总量',
          barWidth: '50%',
          label: {
            show: true,
            position: 'top',
            color: '#999'
          }
        }

        ]
      };
      const series = option.series;

      function getSum(params) {
        let datavalue = 0;
        for (let i = 0; i < series.length; i++) {
          datavalue += series[i].data[params.dataIndex];
        }
        return datavalue;
      }

      series[series.length - 1].label.formatter = getSum;
      myChart.setOption(option);
      myChart.on('legendselectchanged', (obj) => {
        // eslint-disable-next-line no-shadow
        function getSum(params) {
          let datavalue = 0;
          for (let i = 0; i < series.length; i++) {
            if (obj.selected[series[i].name]) {
              datavalue += series[i].data[params.dataIndex];
            }
          }
          return datavalue;
        }

        series[series.length - 1].label.formatter = getSum;
        myChart.setOption(option);
      });
    }
  }
};
</script>

<style>
</style>
