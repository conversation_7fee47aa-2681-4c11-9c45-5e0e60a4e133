<template>
  <div class="jsgl-content">
    <role-tree-dialog :title="editDialogName" :tree-data="jstree" ref="roleTree" @NodeClickJszy="NodeClickJszy" @NodeClickJsyh="NodeClickJsyh"></role-tree-dialog>
    <choose-user-dialog :tree-data="treeData" ref="chooseTree" @NodeClickJsyh="NodeClickJsyh"></choose-user-dialog>
    <div class="title">
      <v-title name="角色管理"></v-title>
    </div>
    <div class="content">
      <div class="jsgl-content-left">
        <div class="jsgl-content-left-content">
          <div class="tree-top-part">
            <el-input
              placeholder="请输入关键字"
              type="text"
              class="input-search"
              size="small"
              prefix-icon="el-icon-search"
              v-model="treeinput" clearable>
            </el-input>
            <div style="padding-left: 25px">
              <el-button style="font-weight: bold" size="default" icon="el-icon-plus" type="text" @click="addYjjs()">新建根角色</el-button>
            </div>
          </div>
          <div class="jsgl-content-left-tree" ref="jstree">
            <el-tree
              ref="jstreeLeft"
              :props="defaultJsProps"
              highlight-current
              :data="jstree"
              node-key="jsid"
              default-expand-all
              :filter-node-method="filterNode"
              @node-contextmenu="openMenu"
              @node-click="handleNodeClick">
              <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{data.jsmc}}
              </span>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="jsgl-content-right" ref="right">
        <el-tabs type="card" v-model="activeName" style="height: 100%">
          <el-tab-pane label="角色详情" name="roleDetail" class="content-tab-pane">
            <department-detail ref="detail"></department-detail>
          </el-tab-pane>
          <el-tab-pane label="角色用户" name="roleUser" class="content-tab-pane">
            <div class="zhxy-form zhxy-form-search-part">
              <el-form label-width="120px" inline :model="departmentSearch" ref="searchForm">
                <el-form-item>
                  <span class="zhxy-form-label" slot="label">用户ID/姓名</span>
                  <el-input class="zhxy-form-inline" v-model="departmentSearch.name" placeholder="用户ID/姓名" size="small"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="()=>{$refs.userDetail.clickUserJsyh(departmentSearch)}" size="small">查询</el-button>
                  <el-button type="" @click="resetYh()" size="small">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="button-tab">
              <el-button type="primary" icon="el-icon-plus" size="small" @click="showYhDialog">新增角色下用户
              </el-button>
              <el-button type="danger" icon="el-icon-delete-solid" size="small" @click="()=>{$refs.userDetail.deleteJsyh()}">批量删除</el-button>
            </div>
            <department-user :table-data="tableDataBmyh" ref="userDetail" :scrollerHeight="scrollerHeight" @modifyUsermx="modifyUsermx"></department-user>
          </el-tab-pane>
          <el-tab-pane label="角色资源" name="roleSource" class="content-tab-pane role-source">
            <el-button class="role-source-button" type="primary" size="small" @click="modifyUser(1)">修改授权</el-button>
            <div style="height: 100%;overflow: auto">
              <el-tree
                ref="tree"
                :props="defaultZyProps"
                highlight-current
                :data="jszytree"
                node-key="gnzyid"
                default-expand-all
                :filter-node-method="filterNode">
              <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{data.gnzymc}}
              </span>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <right-menu ref="rightMenuPart" :options="rightMenuOptions"></right-menu>
    <!--新增一级角色-->
    <el-dialog
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="yjjsaddDialogVisible"
      :title="yjjsdialogType === 'edit'?'新建子角色':'新建根角色'"
      :close-on-click-modal="false" @close="closeYjjs('yjjsForm')">
      <yjjs-add ref="yjjsEdit" :yhid-visible="yjjsyhidVisible" :FormData="yjjsyh"></yjjs-add>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="confirmYjjs('yjjsForm')">确定</el-button>
        <el-button type="" size="small" @click="closeYjjs('yjjsForm')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import { dateFilter } from '@/utils/date-utils';
import {
  findBmList,
  findBmListByPageFbmm,
  findYhListByPageBmm,
  updateBm,
  deleteBmBatch,
  findYhListByBmm,
  drBmBatch,
  deleteYhBm
} from '../../api/bmgl/bmgl';
import VTitle from '../../../../components/title/VTitle';
import DepartmentDetail from './jsglComponents/DepartmentDetail';
import DepartmentUser from './jsglComponents/DepartmentUser';
import ChooseUserDialog from './jsglComponents/ChooseUserDialog';
import RoleTreeDialog from './jsglComponents/RoleTreeDialog';
import RightMenu from '../../components/RightMenu/index';
import YjjsAdd from './jsglComponents/YjjsAdd';
import {
  findList, add, del, findOne, findJsxyhlist, findCkeckedYyzy, findBmlist, findBmYhlist, select
} from '../../api/jsgl/jsgl';

const defaultYjjs = {
  fjsid: '',
  jsid: '',
  jsmc: '',
  jslx: '',
  pxh: '',
  gzlgwtbfs: ''
};

export default {
  components: { // 注册组件
    VTitle,
    DepartmentDetail,
    DepartmentUser,
    ChooseUserDialog,
    RoleTreeDialog,
    RightMenu,
    YjjsAdd
  },
  provide() {
    return {
      reload: this.reload
    };
  },
  data() {
    return {
      jsxq: {
        fjsid: '',
        jsid: '',
        jsmc: '',
        jslx: '',
        pxh: '',
        gzlgwtbfs: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      },
      // table pageSize page
      pageSize: 30,
      page: 1,
      // 父角色id
      fjsidEdit: '',
      // 一级角色
      yjjsyh: { ...defaultYjjs },
      yjjsaddDialogVisible: false,
      yjjsyhidVisible: true,
      yjjsdialogType: 'new',
      // 角色弹窗名称
      editDialogName: '可授权的角色明细(部门)',
      // 部门详情------------------
      departmentSearch: {
        name: ''
      },
      // 新增部门用户 show
      addDepartmentUser: false,
      // 新增部门用户弹窗form
      addUserForm: {
        departmentName: '部门名称',
        userName: '',
        isMainJob: ''
      },
      // 右键菜单menu list
      rightMenuOptions: {
        list: [],
        top: 0,
        left: 0,
        event: {},
        ref: {}
      },
      // 角色id
      jsid: '',
      // 界面定义属性
      scrollerHeight: 'calc(100% - 110px)',
      treeinput: '',
      // 表单
      formInlineBm: {
        bmm: '',
        bmmc: '',
        pxh: ''
      },
      // tab标签
      activeName: 'roleDetail',
      treeData: [],
      // 用户table
      tableDataBmyh: [],
      // 部门table
      tableDataBm: [],
      // table 选中Array
      multipleSelection: [],
      // 资源树结构
      jszytree: [],
      // 角色树结构
      jstree: [],
      // 全部角色树
      defaultJsProps: {
        label: 'jsmc',
        id: 'jsid',
        children: 'children'
      },
      // 部门管理树结构数据格式
      defaultProps: {
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      },
      // 资源树结构数据格式
      defaultZyProps: {
        label: 'gnzymc',
        id: 'gnzyid',
        children: 'children'
      },
      // 部门管理树结构默认展开数组
      defaultTree: [],
      // 当前部门
      currentBm: ''
    };
  },
  watch: {
    treeinput(val) {
      // tree 结构搜索过滤
      this.$refs.jstreeLeft.filter(val);
    },
    activeName(val) {
      this.editDialogName = val === 'roleUser' ? '可授权的角色明细(部门)' : '可授权系统资源';
      if (this.activeName !== 'roleUser') {
        this.$refs.roleTree.isPc = true;
      } else {
        this.$refs.roleTree.isPc = false;
      }
    }
  },
  mounted() { // 页面初始化加载(只在页面初始时加载一次)
    // 角色树方法
    this.loadfirstnode();
    // 角色树默认展开几级
    setTimeout(() => {
      // this.expendDefaultLevel(this.jstree, 1, 2);
    }, 2000);
  },
  methods: {
    /**
     * 用户查询重置事件
     */
    resetYh() {
      this.departmentSearch.name = '';
      this.$refs.userDetail.resetUserJsyh();
    },
    /**
     * 一级角色新增弹窗事件
     */
    addYjjs() {
      this.yjjsyh = { ...defaultYjjs };
      this.yjjsdialogType = 'new';
      this.yjjsyhidVisible = true;
      this.yjjsaddDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.yjjsEdit.yjjsVisible = false;
      });
    },
    /**
     * 一级角色新增/修改
     * @param formName
     */
    confirmYjjs(formName) {
      const param = {
        bgr: '',
        bgsj: '',
        children: [
          {}
        ],
        cjr: '',
        cjsj: '',
        fjsid: this.fjsidEdit,
        gzlgwtbfs: this.$refs.yjjsEdit.$refs[formName].model.gzlgwtbfs,
        jsid: this.$refs.yjjsEdit.$refs[formName].model.jsid,
        jslx: this.$refs.yjjsEdit.$refs[formName].model.jslx,
        jsmc: this.$refs.yjjsEdit.$refs[formName].model.jsmc,
        pxh: this.$refs.yjjsEdit.$refs[formName].model.pxh
      };

      /**
       * 获取 新增 接口
       */
      // eslint-disable-next-line consistent-return
      this.$refs.yjjsEdit.$refs.yjjsForm.validate((valid) => {
        if (valid) {
          select(param).then((res) => {
            const i = res.data.content;
            if (i === 0) {
              add(param).then((rest) => {
              }).finally(() => {
                this.loading = false;
                this.$message.success('新增成功');
                this.$refs.yjjsEdit.$refs[formName].clearValidate();
                this.yjjsaddDialogVisible = false;
                this.loadfirstnode();
              });
            } else {
              this.$message.warning('角色id重复');
            }
          }).finally(() => {
          });
        } else {
          this.$message.error('请确认格式');
          return false;
        }
      });
    },
    /**
     * 关闭一级角色窗口时处理
     * @param formName
     */
    closeYjjs(formName) {
      this.$refs.yjjsEdit.$refs[formName].clearValidate();
      this.yjjsaddDialogVisible = false;
    },
    /**
     * tree 默认代开层级
     * @param data
     * @param startLevel
     * @param stopLevel
     */
    expendDefaultLevel(data, startLevel, stopLevel) {
      this.defaultTree = [];
      const handleTree = (dataTree, level, needLevel) => {
        dataTree.forEach((item) => {
          // this.$set(item, 'privateLevel', level);
          item.privateLevel = level;
          if (item.privateLevel <= needLevel) {
            this.defaultTree.push(item.bmm);
          }
          if (item.privateLevel <= needLevel && item.children && item.children.length > 0) {
            const index = item.privateLevel + 1;
            handleTree(item.children, index, needLevel);
          }
        });
      };
      handleTree(data, startLevel, stopLevel);
    },
    /**
     * tree node 过滤
     * @param value
     * @param data
     * @returns {boolean}
     */
    filterNode(value, data) {
      if (!value) return true;
      return data.jsmc.indexOf(value) !== -1;
    },
    // 右键事件
    openMenu(event, item) {
      this.fjsidEdit = item.jsid;
      this.rightMenuOptions.list = [
        {
          label: '查看详情',
          onClick: this.ckJsxq
        },
        {
          label: '新建子角色',
          onClick: this.addZjs
        },
        {
          label: '编辑',
          onClick: this.rightMenuEdit
        },
        {
          label: '删除',
          onClick: this.deleteYjjs,
          style: 'color:red'
        }
      ];
      this.$refs.rightMenuPart.showRightMenu();
      this.rightMenuOptions.event = event;
      this.rightMenuOptions.ref = this.$refs.rightMenuPart.$el;
    },
    // 编辑角色
    rightMenuEdit() {
      this.activeName = 'roleDetail';
      this.$refs.detail.isEdit = false;
    },
    // 查看角色
    ckJsxq() {
      this.activeName = 'roleDetail';
      this.$refs.detail.isEdit = true;
    },
    // 新增子角色
    addZjs() {
      this.yjjsyh = { ...defaultYjjs };
      this.yjjsdialogType = 'edit';
      this.yjjsyhidVisible = true;
      this.yjjsaddDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.yjjsEdit.yjjsVisible = true;
        this.$refs.yjjsEdit.yjjsFjsid = this.fjsidEdit;
      });
    },
    // 删除角色
    deleteYjjs() {
      const jsid = this.fjsidEdit || ''; // 角色ID
      this.$confirm('该角色下可能存在子角色，确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteCurrent(jsid, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 流程配置删除角色接口事件
     * @param id
     */
    deleteCurrent(id, instance, done) {
      const param = {
        jsid: id
      };
      del(param).then((res) => {
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
        this.$message.success('删除成功');
        this.loadfirstnode();
      });
    },
    // 角色树点击后执行
    handleNodeClick(data) {
      const param = {
        jsid: data.jsid
      };
      /**
       * 获取 角色详情 接口
       */
      findOne(param).then((res) => {
        const originOld = cloneDeep(this.jsxq);
        const newData = res.data.content;
        if (newData.pxh || newData.pxh === 0) {
          newData.pxh += '';
        }
        newData.cjsj = dateFilter(newData.cjsj);
        newData.bgsj = dateFilter(newData.bgsj);
        this.$refs.detail.formData = originOld;
        Object.assign(this.$refs.detail.formData, newData);
        this.$refs.roleTree.jsidZy = data.jsid;
        this.jsid = data.jsid;
        this.NodeClickJsyh(data);
        this.NodeClickJszy(data);
      }).finally(() => {
        this.loading = false;
      });
    },
    NodeClickJsyh(data) {
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid
      };
      /**
       * 获取 角色用户 接口
       */
      findJsxyhlist(param).then((res) => {
        this.tableDataBmyh = res.data.content;
        this.$refs.userDetail.tableDataBmyh = res.data.content;
        this.$refs.userDetail.userTableData = res.data.content;
        this.$refs.userDetail.jsid = this.jsid;
        this.$refs.userDetail.total = res.data.pageInfo.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 角色资源
    NodeClickJszy(data) {
      const param = {
        jsid: data.jsid
      };
      /**
       * 获取 角色资源 接口
       */
      findCkeckedYyzy(param).then((res) => {
        this.jszytree = res.data.content;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 加载角色树
    loadfirstnode(resolve) {
      const param = {};
      /**
       * 获取 全部角色树 接口
       */
      findList(param).then((res) => {
        this.jstree = res.data.content;
        this.handleNodeClick(res.data.content[0]);
        this.NodeClickJsyh(res.data.content[0]);
        this.NodeClickJszy(res.data.content[0]);
        this.$refs.roleTree.jsidZy = res.data.content[0].jsid;
        this.jsid = res.data.content[0].jsid;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 获取部门下用户
    findYhListByPageBmm(bmm) {
    },
    yhhandleSizeChange(val) { // 分页
    },
    yhhandleCurrentChange(val) { // 分页
    },
    // 获取下属部门
    findBmListByPageFbmm(bmm) {
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    bmhandleSizeChange(val) { // 分页

    },
    bmhandleCurrentChange(val) { // 分页

    },
    // 新增下属部门
    addBm() {
    },
    // 用户调入
    // eslint-disable-next-line consistent-return
    openZzjg(title) {
    },
    // 获取部门下所有用户（子组件调用）
    findYhListByBmm(bmm) {
    },
    // 提交调入
    submitZzjg(data) {
    },
    // 用户调入
    // eslint-disable-next-line consistent-return
    drBmBatch(yhlist, sfzzdr) {
    },
    // 调出
    infoDcyh(row) {
    },
    // 更新部门信息
    updateBm() {
    },
    // 删除部门信息
    // eslint-disable-next-line consistent-return
    delBmBatch() {
    },
    /**
     * 编辑角色信息弹窗 展示
     * @param val
     */
    modifyUser(val) {
      this.$refs.roleTree.showDialog();
    },
    modifyUsermx(val) {
      this.$refs.roleTree.showBmDialog(val);
    },
    showYhDialog(val) {
      this.$refs.chooseTree.xzjsid = this.jsid;
      this.$refs.chooseTree.showDialog();
    }
  }
};
</script>
<style lang="scss" scoped>
  .jsgl-content {
    display: flex;
    flex-direction: column;
    .content {
      display: flex;
      flex: 1;
      overflow: auto
    }
    .title {
      background-color: #FFFFFF;
      padding: 10px 10px 0;
    }
    .rightMenu {
      background-color: #FFFFFF;
      border-radius: 4px;
      box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
      ul {
        padding: $page-content-padding;
        li {
          padding: 5px;
          border-bottom: 1px solid $page-bg-color;
          cursor: pointer;
          &:hover {
            color: $page-font-hover-color;
          }
        }
      }
    }
    &-left {
      width: 300px;
      height: 100%;
      padding: $page-content-padding;
      flex-shrink: 0;
      margin-right: $page-content-padding;
      background-color: #ffffff;
      &-content {
        position: relative;
        padding-top: 70px;
        height: 100%;
        .tree-top-part {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
        }
      }
      &-tree {
        height: 100%;
        overflow: auto;
      }
    }
    &-right {
      padding: $page-content-padding;
      flex: 1;
      background-color: #ffffff;
      overflow: auto;
      .button-tab {
        margin-bottom: $page-content-padding;
      }
      .content-tab-pane {
        height: 100%;
        overflow: auto;
      }
      .role-source {
        position: relative;
        padding-top: 40px;
        &-button {
          position: absolute;
          top: 0;
          left: 0
        }
      }
    }
  }
</style>
<style lang="scss">
  .jsgl-content {
    .el-tabs__content {
      height: calc(100% - 42px)
    }
    .custom-tree-node {
      width: 100%;
    }
  }
</style>
