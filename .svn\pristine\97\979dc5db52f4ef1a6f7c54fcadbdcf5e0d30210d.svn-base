<template>
  <el-table class="zhxy-table" :data="tableData" stripe border
            :height="'100%'">
    <el-table-column align="center" type="selection" width="50"></el-table-column>
    <el-table-column prop="yhid" label="用户ID" width="100"></el-table-column>
    <el-table-column prop="xm" label="姓名" width="100"></el-table-column>
    <el-table-column prop="llqxx" label="浏览器信息" width="360"></el-table-column>
    <el-table-column prop="fwzdlx" label="访问终端类型" width="120">
      <template slot-scope="scope">
        <span v-if="scope.row.fwzdlx === 0">PC端</span>
        <span v-else-if="scope.row.fwzdlx === 1">移动端</span>
      </template>
    </el-table-column>
    <el-table-column prop="ip" label="IP" width="130"></el-table-column>
    <el-table-column prop="czsj" label="操作时间" width="180">
      <template slot-scope="scope">
        {{scope.row.czsj | dateFilter}}
      </template>
    </el-table-column>
    <el-table-column prop="" label="登录类型" width="80">
      <template slot-scope="scope">
        <span v-if="scope.row.dllx === '0'">登录</span>
        <span v-else-if="scope.row.dllx === '1'">登出</span>
      </template>
    </el-table-column>
    <el-table-column prop="sfms" label="身份描述"
                     show-overflow-tooltip></el-table-column>
    <el-table-column prop="cjr" label="创建人" width="100"></el-table-column>
    <el-table-column prop="cjsj" label="创建时间" width="180">
      <template slot-scope="scope">
        {{scope.row.cjsj | dateFilter}}
      </template>
    </el-table-column>
    <el-table-column prop="bgr" label="变更人" width="100"></el-table-column>
    <el-table-column prop="bgsj" label="变更时间" width="180">
      <template slot-scope="scope">
        {{scope.row.bgsj | dateFilter}}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { dateFilter } from '@/utils/date-utils';

export default {
  name: 'YhglTable',
  filters: {
    dateFilter
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  methods: {

  }
};
</script>

<style scoped>

</style>
