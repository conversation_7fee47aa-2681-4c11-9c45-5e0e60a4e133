<template>
  <div>
    <div class="dybzx-page-content">
<!--      <div class="jksqgl-pageform-header">
        <span class="zhxy-form-label" slot="label">IP列表</span>
        <el-button type="primary" @click="()=>{}" size="small">新增</el-button>
      </div>-->
      <el-table :data="tableData" style="width: 100%" border height="250" >
        <el-table-column prop="address" label="IP地址" width="500"></el-table-column>
        <el-table-column prop="zt" label="状态" width="300"></el-table-column>
        <el-table-column prop="cjsj" label="创建时间" width="300"></el-table-column>
        <el-table-column fixed="right" label="操作" width="400">
          <template slot-scope="scope">
            <el-button type="text" size="small">
              编辑
            </el-button>
            <el-button @click.native.prevent="deleteRow(scope.$index, tableData)" type="text" size="small">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'jksqgliplb',
  props: {
    tableData: {
      type: Array,
      default: () => ([])
    },
    label: {
      type: Array,
      default: () => ([])
    }
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    margin-top: 20px;
    padding: 0 10px;
    box-sizing: border-box;
  }
}
</style>
