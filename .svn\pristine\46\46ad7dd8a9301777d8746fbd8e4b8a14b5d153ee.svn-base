<template>
  <div class="app-container">
    <el-container style="height: 100%;" direction="vertical">
      <el-header>
        <p class="title">
          {{$route.meta.name || '哈工大软件'}}
        </p>
      </el-header>
      <el-container>
        <el-main class="app-container-main" height="100%">
            <router-view style="padding-top: 10px;height: 100%;overflow: auto"></router-view>
        </el-main>
      </el-container>
      <!--      <el-footer style="height: 30px" class="app-container-footer">Footer</el-footer>-->
    </el-container>
  </div>
</template>
<script>

export default {
  name: 'OutreachPage',
  components: {

  },
  created() {
    console.log('outreach');
  },
  methods: {}
};
</script>

<style lang="scss" scoped="scoped">
  .app-container {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: #FFFFFF;

    .title{
      padding-left: 20px;
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
    }
    .el-container {
      overflow: auto;
    }
    &-main {
      padding: 0 20px;
      position: relative;
      background-color: #f8f8f9;
      overflow: hidden;
    }
    .el-header {
      padding: 0;
      background-image: linear-gradient(90deg, rgb(29, 66, 171), rgb(33, 115, 220), rgb(30, 147, 255));
      color: #333;
      line-height: 60px;
    }
  }
</style>
<style lang="scss">
  .app-container {
  }

</style>
