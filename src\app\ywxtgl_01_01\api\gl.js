import { createAPI } from '@/utils/request.js';

// 接口地址映射
const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL }/ywxtgl_01_01/ywxtglgl`;

const APP_PRE1 = `${BASE_URL }/xtjkyyj_01_01/topography`;

// ！ 接口别名的命名需要遵循  类名 + 方法名，风格需遵循驼峰风格（首字母小写）  注：idea中可安装CamelCase，使用快捷键 shift + alt + u 自动转换为驼峰效果。

export const getXtgjqk = () => createAPI(`${APP_PRE }/getXtgjqk`, 'get');

export const getYjzyyxqk = () => createAPI(`${APP_PRE }/getYjzyyxqk`, 'get');

export const getNcsyl = () => createAPI(`${APP_PRE }/getNcsyl`, 'get');

export const getCpusyl = () => createAPI(`${APP_PRE }/getCpusyl`, 'get');

export const getCcsyl = () => createAPI(`${APP_PRE }/getCcsyl`, 'get');

export const getYsjcjrwqk = () => createAPI(`${APP_PRE }/getYsjcjrwqk`, 'get');

export const findRwcxTp = () => createAPI(`${APP_PRE1 }/findRwcxTp`, 'get');

export const findZtxxTp = () => createAPI(`${APP_PRE1 }/findZtxxTp`, 'get');
