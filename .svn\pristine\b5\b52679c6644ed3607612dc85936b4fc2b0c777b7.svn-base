import { createAPI } from '@/utils/request';

const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/xtjkyyj_01_01/xtjkyyjXtjkRwcx`;

// ！ 接口别名的命名需要遵循  类名 + 方法名，风格需遵循驼峰风格（首字母小写）  注：idea中可安装CamelCase，使用快捷键 shift + alt + u 自动转换为驼峰效果。

// 查询元数据版本列表
export const findPageList = (data) => createAPI(`${APP_PRE}/findPageList`, 'get', data);

export const add = (data) => createAPI(`${APP_PRE }/add`, 'post', data);

export const update = (data) => createAPI(`${APP_PRE }/update`, 'post', data);

export const del = (data) => createAPI(`${APP_PRE }/delete`, 'post', data);
