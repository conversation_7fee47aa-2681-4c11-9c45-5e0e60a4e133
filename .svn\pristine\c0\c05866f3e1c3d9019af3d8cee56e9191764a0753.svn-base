import { createAPI } from '@/utils/request.js';

const BASE_URL = '';

/* 查询分页列表数据 */
export const findList = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzy/findList`, 'get', data);

/* 查询单条数据 */
export const findOne = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzy/findOne`, 'get', data);
//
// /* 新增 */
export const add = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzy/add`, 'post', data);
//
// /* 修改 */
export const update = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzy/update`, 'post', data);
//
// /* 删除 */
export const del = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzy/delete`, 'post', data);
//
// /* 新增功能资源明细 */
export const addGnzymx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzymx/add`, 'post', data);
//
// /* 获取功能资源明细 */
export const findListByPage = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzymx/findListByPage`, 'get', data);
//
// /* 获取功能角色 */
export const findListByJsPage = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJszy/findListByPage`, 'get', data);
//
// /* 修改功能资源明细 */
export const updateGnzymx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzymx/update`, 'post', data);
//
// /* 删除 */
export const delZymx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzymx/delete`, 'post', data);
//
// /* 删除 */
export const delPlZymx = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxGnzymx/deletePl`, 'post', data);
//
// /* 删除 */
export const delZyjs = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJszy/delete`, 'post', data);
//
// /* 删除 */
export const delPlZyjs = (data) => createAPI(`${BASE_URL }/xtgl_02_01/jcqxJszy/deletePl`, 'post', data);
