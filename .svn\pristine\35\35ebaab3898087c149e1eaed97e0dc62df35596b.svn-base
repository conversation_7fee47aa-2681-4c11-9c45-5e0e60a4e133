import { localData, sessionData } from '../../../../../utils/local-utils';

export default {
  /**
   * 存储用户登录信息
   * @param state
   * @param info
   */
  setLogin(state, info) {
    state.userInfo = info;
    sessionData('set', 'userInfo', info);
  },
  /**
   * 更改用户登录角色信息
   * @param state
   * @param info
   */
  setLoginRole(state, role) {
    state.roleId = role;
    sessionData('set', 'roleId', role);
  },
  /**
   * 存储用户菜单列表
   * @param state
   * @param info
   */
  setMenuList(state, info) {
    if (Array.isArray(info)) {
      state.menuList = info;
      sessionData('set', 'menuList', info);
    } else {
      state.menuList = [];
      sessionData('set', 'menuList', []);
    }
  },
  /**
   * 资源列表
   * @param state
   * @param list
   */
  setResourceList(state, list) {
    if (Array.isArray(list)) {
      state.resourceList = list;
      sessionData('set', 'roleList', list);
    } else {
      state.resourceList = [];
      sessionData('set', 'roleList', []);
    }
  },
  /**
   * 登录之后
   * @param state
   * @param val
   */
  setHasLogin(state, val) {
    state.hasLogin = val;
  },
  /**
   * 设置资源loading
   * @param state
   * @param val
   */
  setRoleLoading(state, val) {
    state.roleLoading = val;
  }
};
