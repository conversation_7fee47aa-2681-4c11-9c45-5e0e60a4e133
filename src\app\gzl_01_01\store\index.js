// https://webpack.js.org/guides/dependency-management/#requirecontext
import state from './state';
import mutations from './mutations';
import actions from './actions';

const modulesFiles = require.context('./modules', true, /index.js$/);

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((module, modulePath) => {
  // set './app.js' => 'app'
  const namesArr = modulePath.split('/');
  const moduleName = namesArr[namesArr.length - 2];
  // const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
  const value = modulesFiles(modulePath);
  module[moduleName] = value.default;
  return module;
}, {});

export default {
  namespaced: true,
  modules,
  state: {
    ...state
  },
  mutations: {
    ...mutations
  },
  actions: {
    ...actions
  }

};
