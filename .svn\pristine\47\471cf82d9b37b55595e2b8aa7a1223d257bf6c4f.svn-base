<template>
  <el-table class="zhxy-table" :data="tableData" height="100%" stripe border>
    <el-table-column prop="zjbh" label="中间件编号"></el-table-column>
    <el-table-column prop="zjlx" label="中间件名称"></el-table-column>
    <el-table-column prop="szjip" label="所在主机IP" width="200"></el-table-column>
    <el-table-column prop="zjjyt" label="中间件用途"></el-table-column>
    <el-table-column prop="dk" label="端口"></el-table-column>
    <el-table-column prop="fzr" label="负责人" width="100"></el-table-column>
    <el-table-column prop="kyzt" label="可用状态" width="120">
      <template slot-scope="scope">
        <span v-if="scope.row.kyzt === 1 ">可用</span>
        <span v-else>异常</span>
      </template>
    </el-table-column>
    <el-table-column prop="yyhhs" label="应用会话数(个)"></el-table-column>
    <el-table-column prop="hdxc" label="活动线程(个)"></el-table-column>
    <el-table-column prop="kxxc" label="空闲线程(个)"></el-table-column>
    <el-table-column prop="dqljs" label="当前连接数(个)"></el-table-column>
    <el-table-column prop="fdnc" label="非堆内存(Mb)"></el-table-column>
    <el-table-column prop="dsw" label="堆内存(Mb)"></el-table-column>
    <el-table-column prop="sws" label="事务数(个)"></el-table-column>
    <el-table-column prop="ycsm" label="异常说明"></el-table-column>

  </el-table>
</template>

<script>
export default {
  name: 'ZjjjcTable',
  props: {
    tableData: {
      type: Array,
      default: () => ([])
    }
  }
};
</script>
