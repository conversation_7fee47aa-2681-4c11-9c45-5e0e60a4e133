import { createAPI } from '@/utils/request.js';

const BASE_URL = '';
// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/tysfrz_01_01/tysfrzYygl`;
/**
 * 应用管理获取数据 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getYyList = (data) => createAPI(`${APP_PRE}/findList`, 'get', data);
/**
 * 删除应用数据 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const deleteYyList = (data) => createAPI(`${APP_PRE}/delete`, 'post', data);
/**
 * 新增应用数据 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const addYyList = (data) => createAPI(`${APP_PRE}/add`, 'post', data);

// 应用详情
/**
 * 获得应用详情基本信息 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getYyDetailList = (data) => createAPI(`${APP_PRE}/findOne`, 'get', data);
/**
 * 获取应用所属分类options ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getYyDetailOptions = (data) => createAPI(`${APP_PRE}/findyflList`, 'get', data);
/**
 * 更新应用基础信息 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const updateYyDetail = (data) => createAPI(`${APP_PRE}/update`, 'post', data);
/**
 * IP新增添加
 * @param data
 * @returns {AxiosPromise}
 */
// export const addYyIP = (data) => createAPI(`${APP_PRE}/updateOptionsssList`, 'post', data);
/**
 * 删除IP接口
 * @param data
 * @returns {AxiosPromise}
 */
// export const deleteYyIP = (data) => createAPI(`${APP_PRE}/deletIPList`, 'post', data);
/**
 * 改变IP状态
 * @param data
 * @returns {AxiosPromise}
 */
// export const updateYyIP = (data) => createAPI(`${APP_PRE}/deletIPList`, 'post', data);
/**
 * 获取群组信息tree data
 * @param data
 * @returns {AxiosPromise}
 */
// export const getYyGoupTree = (data) => createAPI(`${APP_PRE}/getGourpIPList`, 'get', data);
/**
 * 群组信息更改/保存
 * @param data
 * @returns {AxiosPromise}
 */
// export const updateYyGoupTree = (data) => createAPI(`${APP_PRE}/getGourpIPList`, 'post', data);
/**
 * 获取用户弹窗内信息
 * @param data
 * @returns {AxiosPromise}
 */
// export const getYyUserTable = (data) => createAPI(`${APP_PRE}/getUserist`, 'get', data);
/**
 * 获取用户弹窗 新增的table 信息
 * @param data
 * @returns {AxiosPromise}
 */
// export const getYyUserAddTable = (data) => createAPI(`${APP_PRE}/getUseraddist`, 'get', data);
/**
 * 新增用户信息弹窗数据保存addZh {yyid,zhlist}
 * @param data
 * @returns {AxiosPromise}
 */
// export const updateYyUserAddTable = (data) => createAPI(`${APP_PRE}/updateUseraddist`, 'post', data);
// /**
//  * 获取认证日志统计信息
//  * @param data
//  * @returns {AxiosPromise}
//  */
// export const getYyLogInfo = (data) => createAPI(`${APP_PRE}/findTjlist`, 'post', data);
/**
 * 获取日志table 数据
 * @param data
 * @returns {AxiosPromise}
 */
export const getYyLogList = (data) => createAPI(`${APP_PRE}/getLogInfoList`, 'post', data);
