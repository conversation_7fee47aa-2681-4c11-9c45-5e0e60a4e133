/*
 * el:指令所绑定的元素，可以用来直接操作 DOM
 * binding：一个对象，包含以下 property:
 *   name：指令名，不包括 v- 前缀。
 *   value：指令的绑定值，例如：v-my-directive="1 + 1" 中，绑定值为 2。
 *   expression：字符串形式的指令表达式。例如 v-my-directive="1 + 1" 中，表达式为 "1 + 1"。
 *   arg：传给指令的参数，可选。例如 v-my-directive:foo 中，参数为 "foo"。
 *   modifiers：一个包含修饰符的对象。例如：v-my-directive.foo.bar 中，修饰符对象为 { foo: true, bar: true }。
 * vnode：Vue 编译生成的虚拟节点
 */
/**
 * v-copy: 一键复制
 * @type {{bind(*, {value: *}): void, unbind(*): void, componentUpdated(*, {value: *}): void}}
 *  <button v-copy="copyText">复制</button>   copyText:变量名称
 */
import Vue from 'vue';

export const copy = {
  bind(el, { value }) {
    el.$value = value;
    el.handler = () => {
      if (!el.$value) {
        // 值为空的时候，给出提示。可根据项目UI仔细设计
        console.log('无复制内容');
        return;
      }
      // 动态创建 textarea 标签
      const textarea = document.createElement('textarea');
      // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
      textarea.readOnly = 'readonly';
      textarea.style.position = 'absolute';
      textarea.style.left = '-9999px';
      // 将要 copy 的值赋给 textarea 标签的 value 属性
      textarea.value = el.$value;
      // 将 textarea 插入到 body 中
      document.body.appendChild(textarea);
      // 选中值并复制
      textarea.select();
      const result = document.execCommand('Copy');
      if (result) {
        console.log('复制成功'); // 可根据项目UI仔细设计
      }
      document.body.removeChild(textarea);
    };
    // 绑定点击事件，就是所谓的一键 copy 啦
    el.addEventListener('click', el.handler);
  },
  // 当传进来的值更新的时候触发
  componentUpdated(el, { value }) {
    el.$value = value;
  },
  // 指令与元素解绑的时候，移除事件绑定
  unbind(el) {
    el.removeEventListener('click', el.handler);
  }
};

/**
 * v-longpress 长按
 * @type {{bind: longpress.bind, unbind(*): void, componentUpdated(*, {value: *}): void}}
 *  <button v-longpress:3000="longpress">长按</button> v-longpress:[触发需要的时间]=[触发的function]
 */
export const longpress = {
  bind(el, binding, vNode) {
    if (typeof binding.value !== 'function') {
      // eslint-disable-next-line no-throw-literal
      throw 'callback must be a function';
    }
    // 运行函数
    const handler = (e) => {
      binding.value(e);
    };
    // 定义变量
    let pressTimer = null;
    // 设置长按的时间
    let pressTime = binding.arg || 2000;
    pressTime = Number(pressTime);
    // 创建计时器（ 2秒后执行函数 ）
    const start = (e) => {
      if (e.type === 'click' && e.button !== 0) {
        return;
      }
      if (pressTimer === null) {
        pressTimer = setTimeout(() => {
          handler();
        }, pressTime);
      }
    };
    // 取消计时器
    const cancel = (e) => {
      if (pressTimer !== null) {
        clearTimeout(pressTimer);
        pressTimer = null;
      }
    };
    // 添加事件监听器
    el.addEventListener('mousedown', start);
    el.addEventListener('touchstart', start);
    // 取消计时器
    el.addEventListener('click', cancel);
    el.addEventListener('mouseout', cancel);
    el.addEventListener('touchend', cancel);
    el.addEventListener('touchcancel', cancel);
  },
  // 当传进来的值更新的时候触发
  componentUpdated(el, { value }) {
    el.$value = value;
  },
  // 指令与元素解绑的时候，移除事件绑定
  unbind(el) {
    el.removeEventListener('click', el.handler);
  }
};

/**
 * v-clickoutside copy from elementUI dom操作
 * @desc 点击元素外面才会触发的事件
 * @example
 *  v-clickoutside:可选触发的区域="handleClose"  v-clickoutside:flow-edit-meng="editover"
 */

// 避免重复绑定
const isServer = Vue.prototype.$isServer;
// 所有绑定了clickoutside指令的元素的dom对象数组
const nodeList = [];
// 用来做存放于dom对象中clickoutside相关参数对象的key
const ctx = '@@clickoutsideContext';
/* istanbul ignore next */
// element封装的一些常用dom操作，这里on可以先当做是addEventListener的封装
const on = (function FnOnSelf() {
  if (!isServer && document.addEventListener) {
    return (element, event, handler) => {
      if (element && event && handler) {
        element.addEventListener(event, handler, false);
      }
    };
  }
  return function FnAttachSelf(element, event, handler) {
    if (element && event && handler) {
      element.attachEvent(`on${ event}`, handler);
    }
  };
}());
let startClick;
// dom id
let seed = 0;

// 鼠标按下时，记录此时事件信息
// eslint-disable-next-line no-unused-expressions,no-return-assign
!Vue.prototype.$isServer && on(document, 'mousedown', (e) => (startClick = e));

// 鼠标松开时候，遍历绑定clickoutside的节点，进行判断是否在节点外部以触发回调
// eslint-disable-next-line no-unused-expressions
!Vue.prototype.$isServer && on(document, 'mouseup', (e) => {
  nodeList.forEach((node) => node[ctx].documentHandler(e, startClick));
});

function createDocumentHandler(el, binding, vnode, arg) {
  // eslint-disable-next-line complexity
  return function FnDocumentEventSelf(mouseup = {}, mousedown = {}) {
    if (!vnode
      || !vnode.context
      || !mouseup.target
      || !mousedown.target
      || el.contains(mouseup.target)
      || el.contains(mousedown.target)
      || el === mouseup.target
      || (vnode.context.popperElm
        && (vnode.context.popperElm.contains(mouseup.target)
          || vnode.context.popperElm.contains(mousedown.target)))) return;
    // 设置起作用区域className
    if (arg && !document.querySelector(`.${arg}`).contains(mousedown.target)) {
      return;
    }

    if (binding.expression
      && el[ctx].methodName
      && vnode.context[el[ctx].methodName]) {
      vnode.context[el[ctx].methodName]();
    } else {
      // eslint-disable-next-line no-unused-expressions
      el[ctx].bindingFn && el[ctx].bindingFn();
    }
  };
}
export const clickoutside = {
  bind(el, binding, vnode) {
    nodeList.push(el);
    const id = seed++;
    el[ctx] = {
      id,
      documentHandler: createDocumentHandler(el, binding, vnode, binding.arg),
      methodName: binding.expression,
      bindingFn: binding.value
    };
  },

  update(el, binding, vnode) {
    el[ctx].documentHandler = createDocumentHandler(el, binding, vnode, binding.arg);
    el[ctx].methodName = binding.expression;
    el[ctx].bindingFn = binding.value;
  },

  unbind(el) {
    const len = nodeList.length;

    for (let i = 0; i < len; i++) {
      if (nodeList[i][ctx].id === el[ctx].id) {
        nodeList.splice(i, 1);
        break;
      }
    }
    delete el[ctx];
  }
};

// 自定义指令 点击 除本身区域外触发 简洁版 (如果有多个v-dom 会有问题)
/*    clickoutside: {
  // 初始化指令
  inserted(el, binding, vnode) {
    // eslint-disable-next-line consistent-return
    const documentHandler = (e) => {
      // 这里判断点击的元素是否是本身，是本身，则返回
      if (el.contains(e.target)) {
        return false;
      }
      // 判断指令中是否绑定了函数
      if (binding.expression) {
        // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
        binding.value(e);
      }
    };

    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    // eslint-disable-next-line no-underscore-dangle
    el.__vueClickOutside__ = documentHandler;
    const dom = document.querySelector('.flow-edit-meng');
    dom.addEventListener('mousedown', documentHandler);
  },
  update() {
  },
  unbind(el, binding) {
    // 解除事件监听
    // eslint-disable-next-line no-underscore-dangle
    document.removeEventListener('mousedown', el.__vueClickOutside__);
    // eslint-disable-next-line no-underscore-dangle
    delete el.__vueClickOutside__;
  }
} */

/**
 * 埋点设置
 * @type {{bind: track.bind}}
 */
export const track = {
  // 钩子函数，只调用一次，指令第一次绑定到元素时调用。在这里可以进行一次性的初始化设置
  bind: (el, binding, vnode) => {
    if (binding.value) {
      // 这里参数是根据自己业务可以自己定义
      const params = {
        currentUrl: binding.value.currentUrl,
        actionType: binding.value.actionType,
        frontTriggerType: binding.value.triggerType,
        businessCode: binding.value.businessCode,
        behavior: binding.value.behavior,
        service: 'xxx'
      };
      // 如果是浏览类型，直接保存
      if (binding.value.triggerType === 'browse') {
        // 调用后台接口保存数据
        // api.eventTrack.saveEventTrack(params);
      } else if (binding.value.triggerType === 'click') {
        // 如果是click类型，监听click事件
        el.addEventListener('click', (event) => {
          // 调用后台接口保存数据
          // api.eventTrack.saveEventTrack(params);
        }, false);
      }
    }
  }
};

let originDisplay = 'block';
// 按钮渲染
const btnRender = (el, btnInfo) => {
  // 设置默认 按钮展示/可点击
  const { isShow = true, isDisabled = false } = btnInfo;
  el.style.display = isShow ? originDisplay : 'none';
  el.setAttribute('disabled', isDisabled);
};
/**
 * 判断元素显示隐藏/是否可选
 * @type {{inserted(*=, *, *): void, update(*=, *, *): void}}
 */
export const has = {
  inserted(el, binding, vnode) {
    originDisplay = getComputedStyle(el, null).display; // 获取原始元素上 display样式
    // let btnPermissionsArr = vnode.context.$route.meta.btnPermissions;
    btnRender(el, binding.value);
  },
  update(el, binding, vnode) {
    btnRender(el, binding.value);
  }
};

export const dragDom = {
  inserted(el, binding, vnode) {
    const dragDomTag = el.querySelector('.drag-tag') ? el.querySelector('.drag-tag') : el;
    dragDomTag.onmousedown = (e) => {
      const disx = e.pageX - el.offsetLeft;
      const disy = e.pageY - el.offsetTop;
      const setDragPosition = (event) => {
        el.style.left = `${event.pageX - disx }px`;
        el.style.top = `${event.pageY - disy }px`;
      };
      document.addEventListener('mousemove', setDragPosition);
      // document.removeEventListener('mouseup', setDragPosition);
      dragDomTag.onmouseup = () => {
        document.removeEventListener('mousemove', setDragPosition);
      };
      dragDomTag.onmouseleave = () => {
        document.removeEventListener('mousemove', setDragPosition);
      };
    };
  },
  // 当VNode更新的时候会执行updated，可以触发多次
  updated(el) {}
};
