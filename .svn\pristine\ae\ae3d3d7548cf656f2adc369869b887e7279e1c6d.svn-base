<template>
  <div class="ysjbb-content">
    <div class="ysjbb-content-main">
      <!-- 标题-->
      <v-title name="业务系统监测"></v-title>
      <rwcx-search ref="searchElement" @search="search"></rwcx-search>
      <div class="button">
        <el-button type="primary" size="small" @click="add()">新增</el-button>
      </div>
      <!-- table-->
      <div class="table">
        <div class="table-content">
          <rwcx-table :scroller-height="scrollerHeight" :table-data="tableData"  @updateCjrwgz="updateCjrwgz"  @delCjrwgz="delCjrwgz"></rwcx-table>
        </div>
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>

      <!--新增修改弹窗-->
      <el-dialog
        ref="dialogEdit"
        customClass="zhxy-dialog-view"
        width="850px"
        :visible.sync="addDialogVisible"
        :title="dialogType === 'edit'?'修改业务系统':'新增业务系统'"
        :close-on-click-modal="false" @close="closeCjrwgz('rwxxForm')">
        <rwcx-edit ref="dialogEditContentByCjrwgz" :cxbs-visible="cxbsVisible" :FormData="fjghsjygl"></rwcx-edit>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" size="small" @click="confirm('rwxxForm')">确定</el-button>
          <el-button type="" size="small" @click="closeCjrwgz('rwxxForm')">取消</el-button>
        </div>
      </el-dialog>

    </div>

  </div>
</template>

<script>
import {
  findPageList,
  add,
  update,
  del
} from '@/app/xtjkyyj_01_01/api/rwcx/rwcx.js';

import VTitle from '@/components/title/VTitle';
import RwcxSearch from './rwcxComponents/rwcxSearch';
import RwcxTable from './rwcxComponents/rwcxTable';
import RwcxEdit from './rwcxComponents/rwcxEdit';

const defaultFjghsjygl = {
  rwlb: '',
  cxmc: '',
  cxbs: '',
  bgzcjgsj: '',
  zxjgsj: 0,
  scbgsj: '',
  yxip: '',
  dlyhm: '',
  dlmm: '',
  qdml: '',
  gbml: ''

};

export default {
  name: 'ysjbbgl',
  components: {
    RwcxSearch,
    RwcxTable,
    RwcxEdit,
    VTitle
  },
  data() {
    return {
      fjghsjygl: { ...defaultFjghsjygl },
      page: 1,
      pageSize: 30,
      scrollerHeight: 0, // 高度
      currentPage: 1, // 初始页
      tableData: [], // 列表数据集合
      pageTotal: 0,
      dialogType: 'new',
      cxbsVisible: true,
      addDialogVisible: false

    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /**
     * 监听页面尺寸改变table size
     */
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight - height - 220;
    },
    /**
     * search 搜索事件
     * @param params
     * @returns {Promise<void>}
     */
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        rwlb: null,
        cxmc: null,
        cxzt: null
      };

      if (params) {
        Object.assign(param, params);
        if (param.cxmc === '') {
          param.cxmc = null;
        }
      }
      console.log(param);
      findPageList(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        rwlb: this.$refs.searchElement.listQuery.rwlb,
        cxmc: this.$refs.searchElement.listQuery.cxmc,
        cxzt: this.$refs.searchElement.listQuery.cxzt
      };
      this.search(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage, // 页码
        rwlb: this.$refs.searchElement.listQuery.rwlb,
        cxmc: this.$refs.searchElement.listQuery.cxmc,
        cxzt: this.$refs.searchElement.listQuery.cxzt
      };
      this.search(param);
    },

    /**
     * 新增系统参数弹窗事件
     */
    add() {
      this.fjghsjygl = { ...defaultFjghsjygl };
      this.dialogType = 'new';
      this.cxbsVisible = true;
      this.addDialogVisible = true;
    },
    /**
     * 关闭用户窗口时处理
     * @param formName
     */
    closeCjrwgz(formName) {
      this.$refs.dialogEditContentByCjrwgz.$refs.rwxxForm.clearValidate();
      this.addDialogVisible = false;
    },

    /**
     * 修改用户信息
     * @param row
     */
    updateCjrwgz(row) {
      this.addDialogVisible = true;
      this.dialogType = 'edit';
      this.cxbsVisible = false;

      if (!row.zjyt) {
        row.zjyt = '';
      }

      Object.assign(this.fjghsjygl, row);
    },

    /**
     * 新增/修改
     * @param formName
     */
    confirm(formName) {
      this.$refs.dialogEditContentByCjrwgz.$refs.rwxxForm.validate((valid) => {
        if (valid) {
          if (this.dialogType !== 'edit') {
            add(this.fjghsjygl).then((result) => {
              this.closeCjrwgz(formName);
              this.search();
              this.$message.success('新增成功');
            });
          } else {
            update(this.fjghsjygl).then((result) => {
              this.closeCjrwgz(formName);
              this.search();
              this.$message.success('修改成功');
            });
          }
        }
      });
    },

    /**
       * 删除
       * @param row
       */
    delCjrwgz(row) {
      const id = row.cxbs || ''; // 参数标识
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            del({ cxbs: id }).then((res) => {
              if (res.code === 200) {
                const index = this.tableData.findIndex((item) => item.cxbs === id);
                if (index > -1) {
                  this.tableData.splice(index, 1);
                }
                this.$message.success('删除成功');
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    }

  }
};
</script>

<style lang="scss" scoped>
.button{
  margin-top: -3.1rem;
  margin-right: 1rem;
  float: right;
}

</style>
