<template>
  <div class="zhgl-content">
    <div class="zhgl-content-main">
      <!--      标题-->
      <v-title name="账号管理"></v-title>
      <zhgl-search ref="searchElement" @search="search" @reset="reset"></zhgl-search>
      <!--      table 表单部分 -->
      <!--      按钮-->
      <div class="button-list">
        <el-button type="primary" size="small" @click="addYh()">新增</el-button>
        <el-dropdown  @command="batchOperation">
          <el-button size="small" type="primary">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="delete">
                批量删除
            </el-dropdown-item>
            <el-dropdown-item command="lock">
                批量锁定
            </el-dropdown-item>
            <el-dropdown-item command="unlock">
                批量解锁
            </el-dropdown-item>
            <el-dropdown-item command="delay">
                过期设置
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" size="small" @click="editFileVisible = true">导入</el-button>
        <download-excel
          class="export-excel-wrapper"
          :fetch= "tableExport"
          :fields="tableFields"
          :before-generate = "()=>{downLoadLoading = true}"
          :before-finish = "()=>{downLoadLoading = false}"
          name="账号管理.xls">
          <el-button size="small" type="primary" :loading="downLoadLoading" @click="downloadFile">导出</el-button>
        </download-excel>
      </div>
      <!--      table-->
      <div class="table">
        <div class="table-content" v-loading="tableLoading">
          <zhgl-table
            :scroller-height="scrollerHeight"
            :table-data="tableData"
            @handleSelectionChange="handleSelectionChange"
            @delTable="delTable"
            @modifyData="modifyData"
            @modifyMm="modifyMm"
            @editStatus="showEditStatus"
            @showGroupDialog="showGroupDialog"
            @formDetail="formDetail">
          </zhgl-table>
        </div>
        <!--        table pageInation-->
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>
    <!--群组修改弹窗-->
    <el-dialog
      customClass="zhxy-dialog-view"
      :visible.sync="groupVisible"
      title="编辑群组"
      :close-on-click-modal="false">
      <div style="max-height: 500px;overflow: auto">
        <el-tree
          ref="groupTree"
          :data="groupData"
          show-checkbox
          node-key="qzid"
          :default-checked-keys="qzchecked"
          :props="defaultProps">
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" :loading="groupConfirmLoading" @click="groupConfirm">确定</el-button>
        <el-button type="" size="small" @click="groupClose">取消</el-button>
      </div>

    </el-dialog>

    <!--新增修改弹窗-->
    <el-dialog
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="addDialogVisible"
      :title="dialogType === 'edit'?'修改账号': dialogType === 'detail' ? '账号详情' : '添加账号'"
      :close-on-click-modal="false"
      @close="tableClose">
      <zhgl-edit ref="dialogEditContent" :mm-regular="mmRegular" :form-data="formData" :dialog-type="dialogType"></zhgl-edit>
      <div v-if="dialogType !== 'detail'" slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" :loading="tableConfirmLoading" @click="tableConfirm">确定</el-button>
        <el-button type="" size="small" @click="tableClose">取消</el-button>
      </div>

    </el-dialog>

    <!--    密码重置/编辑-->
    <el-dialog
      :visible.sync="editMmVisible"
      customClass="zhxy-dialog-view"
      :title="'修改密码'"
      :close-on-click-modal="false">
      <div>
        <el-form
          ref="editMmForm"
          :model="mmForm"
          :rules="mmRules"
          label-width="100px"
          label-position="right">
          <el-form-item label="密码" prop="mm">
            <el-input v-model="mmForm.mm" placeholder="密码" size="small"/>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" :loading="mmLoading" @click="mmConfirm">确定</el-button>
        <el-button type="" size="small" @click="mmClose">取消</el-button>
      </div>
    </el-dialog>
    <!--    文件上传dialog-->
    <el-dialog
      :visible.sync="editFileVisible"
      customClass="zhxy-dialog-view"
      title="上传文件"
      :close-on-click-modal="false">
      <div>
        <file-upload>
          <template #head>
            <div style="margin-bottom: 20px">
              <el-button type="warning" size="small" @click="()=>{}">下载模板</el-button>
            </div>

          </template>
        </file-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="fileConfirm">确定</el-button>
        <el-button type="" size="small" @click="fileClose">取消</el-button>
      </div>
    </el-dialog>

    <!--    过期时间设置-->
    <el-dialog
      :visible.sync="editSjVisible"
      customClass="zhxy-dialog-view"
      :title="'修改过期时间'"
      :close-on-click-modal="false">
      <div>
        <el-form
          ref="editGqsjForm"
          :model="gqsjForm"
          :rules="gqsjRules"
          label-width="100px"
          label-position="right">
          <el-form-item label="过期时间" prop="gqsj">
            <el-date-picker
              style="width: 100%"
              size="small"
              v-model="gqsjForm.gqsj"
              type="datetime"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" :loading="gqsjConfirmLoading" @click="gqsjConfirm">确定</el-button>
        <el-button type="" size="small" @click="gqsjClose">取消</el-button>
      </div>
    </el-dialog>

    <!--    过期时间设置-->
    <el-dialog
      :visible.sync="editStatusVisible"
      customClass="zhxy-dialog-view"
      title="状态编辑"
      :close-on-click-modal="false">
      <div>
        <el-form
          ref="editStatusForm"
          :model="statusForm"
          :rules="statusRules"
          label-width="100px"
          label-position="right">
          <el-form-item prop="zt" label="状态">
            <el-select
              style="width: 100%"
              v-model="statusForm.zt"
              placeholder="用户状态"
              size="small">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" :loading="statusConfirmLoading" @click="statusConfirm">确定</el-button>
        <el-button type="" size="small" @click="statusClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import { cloneDeep } from 'lodash';
import { deletePrompt } from '@/utils/action-utils';
import dayjs from 'dayjs';
import md5 from 'js-md5';
import {
  getPageTabel, addPageTabel, deletePageTabel, getMmRegular, batchDeletePageTabel, getGroupList, updatePageTabel, updatePassword, updateBatchDelay, saveGroupList, updateBatchUnclock, updateBatchClock, updateStatus
} from '@/app/tysfrz_01_01/api/zhgl';
import ZhglSearch from './zhglComponents/InfoSearch';
import ZhglTable from './zhglComponents/InfoTable';
import ZhglEdit from './zhglComponents/InfoEdit';
import FileUpload from './zhglComponents/FileUpload';

export default {
  name: 'zhgl',
  components: {
    ZhglSearch,
    ZhglTable,
    VTitle,
    ZhglEdit,
    FileUpload
  },
  data() {
    return {
      // 编辑状态 弹窗
      editStatusVisible: false,
      statusOptions: [{
        label: '停用',
        value: 2
      }, {
        label: '启用',
        value: 1
      }, {
        label: '锁定',
        value: 3
      }],
      // 编辑状态 弹窗数据
      statusForm: {
        zt: ''
      },
      // 编辑状态弹窗 regular
      statusRules: {
        zt: [
          {
            required: true,
            trigger: 'change',
            message: '请输入状态'
          }
        ]
      },
      // 编辑状态弹窗 确定loading
      statusConfirmLoading: false,
      // 导出数据
      tableFields: {
        账号id: 'zhid',
        证件号: 'sfzh',
        姓名: 'xm',
        联系电话: 'sjh',
        数据来源: 'zhly',
        状态: 'zhzt',
        群组: 'qzmc',
        过期时间: 'gqsj'
      },
      downLoadLoading: false,
      // 页面加载loading
      tableLoading: false,
      // 编辑/新增弹窗确定loading
      tableConfirmLoading: false,
      // groupVisible
      groupVisible: false,
      // 群组弹窗确定按钮loading
      groupConfirmLoading: false,
      // 群组已选中
      qzchecked: [],
      // 群组弹窗数据
      groupData: [],
      // 群组树结构 props
      defaultProps: {
        children: 'children',
        label: 'qzmc',
        id: 'qzid'
      },
      // 导入文件visible
      editFileVisible: false,
      // 密码按钮loading
      mmLoading: false,
      // 密码form
      mmForm: {
        mm: ''
      },
      // 密码rules
      mmRules: {
        mm: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入密码'
          },
          {
            pattern: /(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[\W])(?=.*[\S])^[0-9A-Za-z\S]{8,16}$/g,
            message: '请输入正确的密码格式（必须8-16位且包含大写字母,小写字母,数字,特殊符号）'
          }
        ]
      },
      mmRegular: {
        pattern: /(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[\W])(?=.*[\S])^[0-9A-Za-z\S]{8,16}$/g,
        message: '请输入正确的密码格式（必须8-16位且包含大写字母,小写字母,数字,特殊符号）'
      },
      // 编辑密码visible
      editMmVisible: false,
      // 过期时间弹窗visible
      editSjVisible: false,
      // 批量锁定弹窗visible
      editLockVisible: false,
      // 过期时间确定loading
      gqsjConfirmLoading: false,
      // 过期时间form
      gqsjForm: {
        gqsj: ''
      },
      // 过期时间rules
      gqsjRules: {
        gqsj: [
          {
            required: true,
            trigger: 'blur',
            message: '请选择过期时间'
          }
        ]
      },
      // 编辑/添加/查看 数据form表单信息
      formData: {
        zhid: '',
        xm: '',
        mm: '',
        sjh: '',
        yx: '',
        gqsj: '',
        sfzh: 1,
        zhzt: 1
      },
      scrollerHeight: 0,
      currentPage: 1, // 初始页
      tableData: [], // 列表数据集合
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      // 暂存的选中row数据
      tempRow: {},
      zhSelection: [], // table多选框数据
      yhidList: [],
      addDialogVisible: false,
      dialogType: 'new'
    };
  },
  created() {
    /**
     * 获取密码正则校验规则
     */
    getMmRegular().then((res) => {
      this.mmRegular.message = res.data.content.csbssm;
      this.mmRegular.pattern = res.data.content.csz;
      this.mmRules.mm[1].message = res.data.content.csbssm;
      this.mmRules.mm[1].pattern = res.data.content.csz;
    });
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 编辑状态弹窗确定
    statusConfirm(val) {
      const params = {
        zhid: this.tempRow.zhid,
        zhzt: this.statusForm.zt
      };
      this.statusConfirmLoading = true;
      // 状态编辑接口
      updateStatus(params).then(() => {
        this.$message.success('状态编辑成功');
        this.tempRow.zhzt = this.statusForm.zt;
      }).finally(() => {
        this.statusConfirmLoading = false;
        this.statusClose();
      });
    },
    // 编辑状态弹窗取消
    statusClose() {
      this.editStatusVisible = false;
    },
    // 编辑状态弹窗 展示
    showEditStatus(val) {
      this.tempRow = val;
      this.statusForm.zt = val.zhzt;
      this.editStatusVisible = true;
    },
    // 导出数据 通过 json-excel 导出
    async tableExport() {
      const params = {
        page: 1,
        pagesize: this.pageTotal
      };
      // 获取全部表单数据
      try {
        const result = await getPageTabel(params);
        return result.data.content;
      } catch (err) {
        this.$message.error(err);
        return false;
      }
    },
    /**
     * 监听页面尺寸改变table size
     */
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight*0.8 - height - 265;
    },
    // search part-------------
    /**
     * search 搜索事件
     * @returns {Promise<void>}
     */
    search() {
      this.tableLoading = true;
      const gqsj = this.$refs.searchElement.listQuery.gqsj;
      let yxqkssj = null;
      let yxqjssj = null;
      if (gqsj) {
        yxqkssj = dayjs(gqsj[0]).format('YYYY-MM-DD HH:mm:ss');
        yxqjssj = dayjs(gqsj[1]).format('YYYY-MM-DD HH:mm:ss');
      }

      const params = {
        page: this.page,
        pagesize: this.pageSize,
        ...this.$refs.searchElement.listQuery,
        yxqkssj,
        yxqjssj
      };
      params.gqsj = '';
      // 调用 获取分页接口
      getPageTabel(params).then((res) => {
        this.tableLoading = false;
        this.tableData = res.data.content;
        this.pageTotal = res.data.pageInfo.total;
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    /**
     * reset 搜索重置事件
     * @returns {Promise<void>}
     */
    reset() {
      this.page = 1;
      this.search();
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.search();
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.page = val;
      this.search();
    },
    // search part------------- end -----------------
    // table part -----------------------------------
    /**
     * 新增用户弹窗事件
     */
    addYh() {
      this.dialogType = 'new';
      this.addDialogVisible = true;
      this.formData = {
        zhid: '',
        xm: '',
        mm: '',
        sjh: '',
        yx: '',
        gqsj: '',
        sfzh: '',
        zhzt: ''
      };
    },
    // 表单信息详情查看
    formDetail(params) {
      this.dialogType = 'detail';
      Object.assign(this.formData, params);
      this.addDialogVisible = true;
    },
    /**
     * 修改用户信息
     * @param row
     */
    modifyData(params) {
      this.addDialogVisible = true;
      this.dialogType = 'edit';
      Object.assign(this.formData, params);
      // this.$nextTick(() => {
      //   this.$refs.dialogEditContent.rules.mm[0].required = false;
      // });
    },
    /**
     * 新增/修改 确定事件
     * @param formName
     */
    tableConfirm() {
      // 表单校验
      this.$refs.dialogEditContent.$refs.editForm.validate((valid) => {
        if (valid) {
          // 新增的确定事件
          if (this.dialogType === 'new') {
            const params = cloneDeep(this.formData);
            // params.mm = md5(params.mm);
            if (params.gqsj) {
              params.gqsj = dayjs(params.gqsj)
                .format('YYYY-MM-DD HH:mm:ss');
            }
            // 账号信息 添加新增接口调用
            this.tableConfirmLoading = true;
            addPageTabel(params).then((res) => {
              this.$message.success('信息添加成功');
              // 刷新数据
              this.search();
            }).finally(() => {
              this.tableConfirmLoading = false;
              this.tableClose();
            });
          } else {
            // 修改form操作
            const params = cloneDeep(this.formData);
            // params.mm = md5(params.mm);
            if (params.gqsj) {
              params.gqsj = dayjs(params.gqsj)
                .format('YYYY-MM-DD HH:mm:ss');
            }
            this.tableConfirmLoading = true;

            updatePageTabel(params).then((res) => {
              this.$message.success('信息编辑成功');
              // 刷新数据
              this.search();
            }).catch((err) => {
              this.$message.success(err);
            }).finally(() => {
              this.tableConfirmLoading = false;
              this.tableClose();
            });
          }
        }
      });
    },

    /**
     * 关闭编辑/新增 窗口时处理
     * @param formName
     */
    tableClose() {
      this.$refs.dialogEditContent.$refs.editForm.resetFields();
      this.addDialogVisible = false;
    },

    /**
     * 修改密码
     * @param row
     */
    modifyMm(row) {
      this.tempRow = row;
      this.editMmVisible = true;
      this.mmForm.mm = '';
    },

    // 密码 编辑确定
    mmConfirm() {
      this.$refs.editMmForm.validate((valid) => {
        if (valid) {
          // 新增的确定事件
          // 密码修改接口
          this.mmLoading = true;
          const params = {
            zhid: this.tempRow.zhid,
            mm: this.mmForm.mm
          };
          updatePassword(params).then(() => {
            this.$message.success('密码修改成功');
          }).finally(() => {
            this.mmLoading = false;
            this.editMmVisible = false;
          });
        }
      });
    },
    // 密码 编辑取消
    mmClose() {
      this.$refs.editMmForm.resetFields();
      this.editMmVisible = false;
    },
    /**
     * 删除用户
     * @param row
     */
    delTable(row) {
      // 设置参数
      const config = {
        text: '是否删除?',
        btnText: '执行中...'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        // 删除接口
        const params = {
          zhid: row.zhid
        };
        deletePageTabel(params).then(() => {
          this.$message.success('删除成功');
          const index = this.tableData.findIndex((x) => x.zhid === row.zhid);
          if (index > -1) {
            this.tableData.splice(index, 1);
          }
          successAction();
        }).catch((err) => {
          this.$message.error(err);
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 群组弹窗确定
    groupConfirm() {
      const hasChecked = this.$refs.groupTree.getCheckedNodes();
      // const checked = hasChecked.map((item) => ({ id: item.id, name: item.label }));
      this.groupConfirmLoading = true;
      // 保存群组关系 接口
      const params = {
        zhid: this.tempRow.zhid,
        qzid: hasChecked.map((item) => item.qzid).join(',')
      };
      saveGroupList(params).then(() => {
        this.tempRow.qzmc = hasChecked.map((item) => item.qzmc).join(',');
        this.tempRow.qzid = hasChecked.map((item) => item.qzid).join(',');
        this.$message.success('群组数据保存成功');
        this.groupVisible = false;
      }).finally(() => {
        this.groupConfirmLoading = false;
      });
    },
    // 群组弹窗取消
    groupClose() {
      this.groupVisible = false;
    },
    // 群组弹窗展示
    showGroupDialog(val) {
      if (!val.qzid) {
        this.$set(val, 'qzid', '');
      }
      if (!val.qzmc) {
        this.$set(val, 'qzmc', '');
      }
      const idList = val.qzid.split(',');
      this.tempRow = val;
      this.groupVisible = true;
      this.qzchecked = [];
      idList.forEach((item) => {
        this.qzchecked.push(item);
      });
      // 获取群组列表数据
      getGroupList().then((res) => {
        this.groupData = res.data.content;
      });
    },
    // 文件导入弹窗的 确定
    fileConfirm() {
      this.editFileVisible = false;
    },
    // 文件导入弹窗的 取消
    fileClose() {
      this.editFileVisible = false;
    },
    // table 头部按钮的操作------------------------------------
    /**
     * 多选框选中赋值
     * @param val
     */
    handleSelectionChange(val) {
      this.zhSelection = val;
    },
    // 批量删除操作
    batchDelete() {
      // 设置参数
      const config = {
        text: '是否进行批量删除?',
        btnText: '执行中...'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        // 批量删除接口
        const params = {
          str: this.zhSelection.map((x) => x.zhid)
        };
        batchDeletePageTabel(params).then(() => {
          this.$message.success('删除成功');
          successAction();
          this.search();
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 批量锁定操作
    batchLock() {
      // 设置参数
      const config = {
        text: '是否进行批量锁定?',
        btnText: '执行中...'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        // 批量锁定接口 调用
        const params = {
          str: this.zhSelection.map((x) => x.zhid)
        };
        updateBatchClock(params).then(() => {
          this.$message.success('批量锁定成功');
          successAction();
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 批量解锁操作
    batchUnlock() {
      // 设置参数
      const config = {
        text: '是否进行批量解锁?',
        btnText: '执行中...'
      };
      // 设置确定的callback 及成功操作
      const deleteItem = (successAction) => {
        // 批量解锁接口 调用
        const params = {
          str: this.zhSelection.map((x) => x.zhid)
        };
        updateBatchUnclock(params).then(() => {
          this.$message.success('批量解锁成功');
        }).finally(() => {
          successAction();
        });
      };
      // 删除提示调用
      deletePrompt(config, deleteItem);
    },
    // 批量操作
    batchOperation(command) {
      if (this.zhSelection.length <= 0) {
        this.$message.warning('请先选择数据');
        return;
      }
      switch (command) {
        case 'delete':
          this.batchDelete();
          break;
        case 'delay': // 过期时间设置
          this.editSjVisible = true;
          break;
        case 'lock': // 锁定设置
          this.batchLock();
          break;
        case 'unlock':
          this.batchUnlock();
          break;
        default:
      }
    },
    // 过期时间 编辑确定
    gqsjConfirm() {
      this.$refs.editGqsjForm.validate((valid) => {
        if (valid) {
          // 模拟接口
          this.gqsjConfirmLoading = true;
          // 批量延期设置
          const params = {
            gqxj: this.gqsjForm.gqsj ? dayjs(this.gqsjForm.gqsj).format('YYYY-MM-DD HH:mm:ss') : '',
            str: this.zhSelection.map((x) => x.zhid)
          };
          updateBatchDelay(params).then((res) => {
            this.editSjVisible = false;
          }).finally(() => {
            this.gqsjConfirmLoading = false;
          });
          // 新增的确定事件
        }
      });
    },
    // 过期时间 编辑取消
    gqsjClose() {
      this.$refs.editGqsjForm.resetFields();
      this.editSjVisible = false;
    },
    // 文件下载导出
    downloadFile() {
      console.log('下载');
    }
  }
};
</script>
<style lang="scss" scoped>
.zhgl-content {
  &-main {
    background-color: #ffffff;
    padding: $page-content-padding;
  }

  .button-list {
    display: flex;
    justify-content: flex-end;

    > div, > button {
      margin-left: $page-content-margin;
    }
  }

  .table {
    &-content {
      margin-top: $page-content-margin;
    }

  }

  .dialog-footer {
    text-align: center;
  }
}
</style>
