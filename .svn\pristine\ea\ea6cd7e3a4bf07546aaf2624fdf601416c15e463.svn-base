import Vue from 'vue';
import VueRouter from 'vue-router';
// import Home from '../app/index/pages/home/<USER>';

Vue.use(VueRouter);

// 无需权限的路由
const constantRoutes = [
  // {
  //   path: '/home',
  //   name: '样例测试',
  //   component: () => import('@/app/layout_03_01/pages/home/<USER>'),
  //   meta: {
  //     name: '样例测试',
  //     type: '1'
  //   }
  // },
  {
    path: '/login',
    name: 'login',
    component: () => import('../app/layout_03_01/pages/Login')
  },
  {
    path: '/login_common',
    name: 'login',
    component: () => import('../app/layout_03_01/pages/LoginCommon')
  },
  {
    path: '/403',
    name: '403',
    component: () => import('../app/layout_03_01/pages/403')
  },
  {
    path: '/404',
    name: '404',
    component: () => import('../app/layout_03_01/pages/404')
  }
];

// 解决vue-router在3.0版本以上重复跳转报错问题(初始登陆/菜单点击)
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location)
    .catch((err) => err);
};
/**
 * vueRouter实例方法
 * @returns {VueRouter}
 */
const createRouter = () => new VueRouter({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
});
// 创建vueRouter实例
const router = createRouter();

/**
 * 获取App中所有小应用包含的routers
 * @returns {[]}
 */
function getAppRouters() {
  const routesApp = require.context('@/app/', true, /(.*?)\/router\/(.*?)\.js$/);
  const routers = [];
  routesApp.keys()
    .forEach((key) => {
      const routerItem = routesApp(key).default || routesApp(key);
      routers.push(...routerItem);
    });
  return routers;
}

const asyncRoutes = getAppRouters();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export { resetRouter, asyncRoutes };

export default router;
