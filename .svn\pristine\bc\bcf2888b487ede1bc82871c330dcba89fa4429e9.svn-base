<template>
  <div class="dialog-content">
    <el-form ref="yhForm" :model="FormData" :rules="rules" label-width="100px"
             label-position="right">
      <el-form-item label="参数标识" prop="csbs">
        <el-input :disabled="!yhidVisible" v-model="FormData.csbs" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="参数标识说明" prop="csbssm">
        <el-input v-model="FormData.csbssm" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="参数值" prop="csz">
        <el-input v-model="FormData.csz" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="备注" prop="bz">
        <el-input type="textarea" :rows="3" v-model="FormData.bz"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Yhgl',
  props: {
    FormData: {
      type: Object,
      default: () => ({
        csbs: '111',
        csbssm: '222',
        csz: '333',
        bz: ''
      })
    },
    yhidVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 参数标识校验
    // eslint-disable-next-line consistent-return
    const validateCsbs = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入参数标识'));
      }
      callback();
    };

    // 参数标识说明校验
    // eslint-disable-next-line consistent-return
    const validateCsbssm = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入参数标识说明'));
      }
      callback();
    };

    // 参数值说明校验
    // eslint-disable-next-line consistent-return
    const validateCsz = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入参数标识说明'));
      }
      callback();
    };
    return {
      rules: {
        csbs: [{
          required: true,
          trigger: 'blur',
          validator: validateCsbs
        }],
        csbssm: [{
          required: true,
          trigger: 'blur',
          validator: validateCsbssm
        }],
        csz: [{
          required: true,
          trigger: 'blur',
          validator: validateCsz
        }]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 10px;
  }
</style>
