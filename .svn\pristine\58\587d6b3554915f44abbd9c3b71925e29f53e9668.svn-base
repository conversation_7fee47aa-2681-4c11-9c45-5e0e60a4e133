// 引入mockjs
import Mock from 'mockjs';

const mockRequest = [
  {
    url: '/api/xtgl_02_01/jcqxYh/findListByPage', // 接口url
    methods: 'get', // 请求方式
    data: { // data 返回的数据结果
      code: 0,
      message: '',
      data: {
        data: {
          content: [
            {
              yhid: '12345',
              xm: 'name',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '*************************************************',
              xm: '',
              mm: '',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            },
            {
              yhid: '111',
              xm: '222',
              mm: '333',
              sjh: '',
              dzyx: '',
              sfms: '',
              sfzj: 1,
              kdlfs: '1100000000',
              yhzt: 1,
              sjlybz: '',
              bz: '',
              bmmc: undefined,
              jsmc: ''
            }
          ], // 列表数据集合
          pageInfo: {
            total: 40
          }
        }
      }
    }
  }

];

mockRequest.forEach((item) => {
  Mock.mock(RegExp(`${item.url}*`), item.methods, item.data);
});
export default Mock;
