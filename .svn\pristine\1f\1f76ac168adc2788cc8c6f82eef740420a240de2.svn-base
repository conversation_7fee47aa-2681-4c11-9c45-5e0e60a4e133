<template>
  <el-dialog
    :width="width"
    :title="title"
    :visible.sync="visible"
    custom-class="choose-user-dialog"
    append-to-body>
    <div class="origin-user-part">
      <div class="origin-user-part-left">
        <div class="content-top">
          <div class="zhxy-form zhxy-form-search-part" style="align-items: center;margin: 0">
            <el-form label-width="120" inline :model="departyhmentSearch" ref="searchForm">
              <el-form-item style="align-items: center">
                <el-input
                  class="zhxy-form-inline" v-model="departyhmentSearch.idxm"
                  style="width: 548px;margin: 0"
                  placeholder="请输入用户姓名或ID进行快速查找"
                  size="small">
                  <el-button slot="append" @click="clickUserJsyh(departyhmentSearch)">查询</el-button>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="content-main">
          <div class="content-main-left zhxy-tree-nomal">
            <el-tree
              :props="defaultProps"
              highlight-current
              :node-key="nodeKey"
              :data="treeData"
              @node-click="bmNodeClick"
              ref="tree">
               <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{data[defaultProps.label]}}
              </span>
            </el-tree>
          </div>
          <div class="content-main-right">
            <el-table
              :data="tableData"
              stripe
              border
              class="zhxy-table"
              height="calc(100% - 26px)"
              header-row-class-name="origin-table-header-middle"
              row-key="id"
              ref="treeTable"
              @selection-change="handleSelectionChange">
              <el-table-column
                align="center"
                type="selection"
                label=""
                width="52">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="xm"
                label="可选用户"
                width="100">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="yhid"
                label="用户id"
                width="120">
              </el-table-column>
            </el-table>
            <el-pagination
              style="text-align: center"
              small
              :page-size="30"
              layout="prev, pager, next"
              @current-change="handleCurrentChange"
              :total=total>
            </el-pagination>
          </div>
        </div>

      </div>
      <div class="origin-usr-part-right">
        <el-table :data="multipleSelection" stripe border class="zhxy-table" height="calc(100% - 26px)" header-row-class-name="origin-table-header">
          <el-table-column prop="xm" label="已选用户" width="260"></el-table-column>
          <el-table-column align="center" prop="" label="" width="60">
            <template slot-scope="scope">
              <el-button icon="el-icon-delete" circle size="small" type="" @click="deletItem(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div slot="footer">
      <el-button size="small" type="primary" @click="addJsyhqd(multipleSelection)">确 定</el-button>
      <el-button size="small" @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>

import {
  updateJsxzy, addJsyh, findBmlistAll, findBmYhlist, findJsxyhlist
} from '../../../api/jsgl/jsgl';

export default {
  name: 'ChooseUserDialog',
  props: {
    width: {
      type: String,
      default: '920px'
    },
    title: {
      type: String,
      default: '选择用户'
    },
    defaultProps: {
      type: Object,
      default: () => ({
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      })
    },
    nodeKey: {
      type: String,
      default: 'bmm'
    },
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // dialog 显隐
      visible: false,
      // 部门用户
      departyhmentSearch: {
        idxm: ''
      },
      pageSize: 30,
      // table page
      page: 1,
      xzpageSize: 30,
      // 部门人员总数量
      total: 0,
      // table page
      xzpage: 1,
      xzbmm: '',
      xzjsid: '',
      treeBmData: this.treeData,
      // 用户table
      tableData: [],
      multipleSelection: []
    };
  },
  methods: {
    // 显示部门树
    showDialog() {
      this.visible = true;
      this.departyhmentSearch.idxm = '';
      this.NodeClickBm();
    },
    // 获取 部门
    NodeClickBm() {
      /**
       * 获取 部门 接口
       */
      findBmlistAll().then((res) => {
        this.treeData = JSON.parse(res.data.content).bmList;
        this.NodeClickBmYhid('');
      }).finally(() => {
        this.loading = false;
      });
    },
    // 获取 部门用户
    NodeClickBmYhid(data) {
      const param = {
        bmm: data.bmm,
        jsid: this.xzjsid,
        idxm: data.idxm,
        pageSize: this.pageSize, // 页码条数
        page: this.page // 页码
      };
      /**
       * 获取 部门用户 接口
       */
      findBmYhlist(param).then((res) => {
        this.tableData = res.data.content;
        this.total = res.data.pageInfo.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 隐藏部门树
    hideDialog() {
      this.visible = false;
    },
    // 勾选部门用户
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 清除勾选部门用户
    deletItem(row) {
      const i = this.multipleSelection.findIndex((x) => x.id === row.id);
      // this.multipleSelection.splice(i, 1);
      this.$refs.treeTable.toggleRowSelection(row, false);
    },
    // 部门树点击后执行
    bmNodeClick(data) {
      this.xzbmm = data.bmm;
      this.NodeClickBmYhid(data);
    },
    // 获取部门用户
    clickUserJsyh(data) {
      data.bmm = this.xzbmm;
      this.NodeClickBmYhid(data);
    },
    addJsyhqd(data) {
      const arr = [];
      data.forEach((item) => {
        const itemparam = {
          jsid: this.xzjsid,
          yhid: item.yhid
        };
        arr.push(itemparam);
      });
      /**
       * 新增 角色下用户 接口
       */
      const param = {
        yharr: arr
      };
      addJsyh(param).then((res) => {
        this.$message.success('新增成功');
        this.multipleSelection = '';
        const paramdata = {
          jsid: this.xzjsid
        };
        this.$emit('NodeClickJsyh', paramdata);
        this.visible = false;
      }).finally(() => {
        this.loading = false;
      });
    },
    /**
     * currentPage change event
     * @param val
     */
    handleCurrentChange(val) {
      this.page = val;
      const param = {
        bmm: this.xzbmm,
        jsid: this.xzjsid,
        idxm: this.departyhmentSearch.idxm,
        pageSize: this.pageSize, // 页码条数
        page: this.page // 页码
      };
      /**
       * 获取 部门用户 接口
       */
      findBmYhlist(param).then((res) => {
        this.tableData = res.data.content;
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
<style lang="scss">
  .choose-user-dialog {
    .el-dialog__body {
      padding: 10px 10px 0;
    }
    .el-dialog__footer {
      padding: 10px;
    }
    tr.origin-table-header-middle{
      th{
        background-color: #ffffff !important;
        padding: 5px;
      }
    }
    tr.origin-table-header{
      th{
        padding: 4px;
      }
    }
  }
</style>
<style lang="scss" scoped>
  .choose-user-dialog {
    .origin-user-part {
      height: 500px;
      display: flex;
      justify-content: space-between;
      overflow: auto;
      &-left {
        display: flex;
        flex-direction: column
      }
    }
    .content-main {
      display: flex;
      flex: 1;
      overflow: auto;
      &-left {
        margin-right: 10px;
        width: 265px;
        flex-shrink: 0;
        height: 100%;
        overflow: auto
      }
    }
  }
</style>
