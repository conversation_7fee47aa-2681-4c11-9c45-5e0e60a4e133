<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">服务名称</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.fwmc" placeholder="服务名称"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">服务说明</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.fwsm" placeholder="服务说明"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">服务状态</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.fwzt" placeholder="状态"
                   size="small">
          <el-option
            v-for="item in ztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">服务分类</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.fwfl" placeholder="服务分类"
                   size="small">
          <el-option
            v-for="item in fwflOptions"
            :key="item.dmz"
            :label="item.dmmc"
            :value="item.dmz">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">创建人</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.cjr" placeholder="创建人"
                  size="small"></el-input>
      </el-form-item>

<!--      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">创建时间</span>
        <el-date-picker
          value-format="yyyy-MM-dd HH:mm:ss"
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.cjsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>-->
      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">变更人</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.bgr" placeholder="创建人"
                  size="small"></el-input>
      </el-form-item>

<!--      <el-form-item v-show="isShowLabel">
        <span class="zhxy-form-label" slot="label">变更时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.bgsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>-->

      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
        <p
          class="search-fold"
          @click="()=>{this.isShowLabel = !this.isShowLabel}">
          {{this.isShowLabel ? '收缩' : '展开'}}
          <i :class="!this.isShowLabel ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></p>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'LcglSearch',
  props: {
    // 服务类型 options
    fwflOptions: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      isShowLabel: false,
      // label 宽度
      lableWidth: '120px',
      // labelWidth
      lableWidthSingle: '450px',
      // search params
      listQuery: {
        fwmc: '', // 服务名称
        fwsm: '', // 服务说明
        fwfl: '', // 服务分类
        cjr: '', // 创建人
        // cjsj: '', // 创建时间
        bgr: '', // 变更人
        // bgsj: '', // 变更时间
        fwzt: '' // 服务状态
      },
      // 状态 下拉options
      ztOptions: [
        {
          label: '不可用',
          value: 0
        },
        {
          label: '草稿',
          value: 1
        },
        {
          label: '发布',
          value: 2
        }
      ]
    };
  },
  methods: {
    /**
     * 搜索查询
     */
    search() {
      this.$emit('search', this.listQuery);
    },
    /**
     * 重置搜索条件
     */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });
    }
  }
};
</script>

<style lang="scss" scoped>
  .search-fold {
    color: $pageFontHoverColor;
    display: inline-block;
    margin-left: $page-content-margin;
    margin-right: 5px;
    cursor: pointer
  }
</style>
