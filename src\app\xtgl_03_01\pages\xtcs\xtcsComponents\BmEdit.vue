<template>
  <div class="dialog-content">
    <el-form ref="addYhbmForm" :model="FormData" :rules="yhbmRule" label-width="80px"
             label-position="right">
      <el-form-item prop="yhid" v-if="false">
        <el-input v-model="FormData.yhid"/>
      </el-form-item>
      <el-form-item label="部门" prop="bmm">
        <el-select v-model="FormData.bmm" filterable placeholder="请选择" size="small"
                   style="width: 100%">
          <el-option
            v-for="item in yhbmOptions"
            :key="item.bmm"
            :label="item.bmmc"
            :value="item.bmm">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Yhgl',
  props: {
    FormData: {
      type: Object,
      default: () => ({
        yhid: '',
        bmm: ''
      })
    },
    yhbmOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      yhbmRule: {
        bmm: [{
          required: true,
          trigger: 'blur',
          message: '请选择部门'
        }]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    height: 150px;
    width: 100%;
    padding: 10px;
    overflow: auto
  }
</style>
