/*以系统参数为模板*/
.app_content{
    height: 100%;
    width: 100%;
    padding: 10px 0 0;
    box-shadow: 0 2px 10px 0 rgba(180,190,220,0.27);
    background-color: #fff;
}
.app_content_gl{
    height: 100%;
    width: 100%;
    //padding: 10px 0 0;
    //box-shadow: 0 2px 10px 0 rgba(180,190,220,0.27);
    background-color: #F5F5F5;
	overflow-y: auto;
}
.app_content_tree{
    box-shadow: none;
    background-color:transparent;
    padding: 0;
}
.clear{
    clear: both;
}
.table_qy_tree_right{
    padding: 10px 0 0;
    float: right;
    width: calc(100% - 275px)!important;
    border-radius: 3px;
}
.tree_ssk{
    height: 36px;
    width: 90%;
    margin: 0 auto;
    position: relative;
}
.tree_ssk_icon{
    position: absolute;
    right: 6px;
    top: 1px;
    color: #999;
}
.tree_ssk_input input{
    background: #FFFFFF;
    border: 1px solid #E1E1E1!important;
    border-radius: 100px!important;
}
.tree_ssk_input :hover {
    border-color: #999;
}
.table_qy_tree_left{
    padding: 10px 10px 0 10px;
    float: left;
    height: 100%;
    overflow: auto;
    width: 270px;
    border-radius: 3px;
    background-color: #fff;
}
.table_qy_tree_left .el-scrollbar{
    border: 0;
}
.table_qy_tree_left .el-menu{
    border: 0;
}
.table_qy_tree_left .el-icon-arrow-down{
    display: none;
}
.el-menu-item_sxyd{
    background-color: #5E5F60
}
.tb_ys{
    color: #16a8f8;
    font-size: 18px;
}
.tb_ys:hover{
    color: #0c60e1;
    font-weight: bold;
    font-size: 18px;
}
.table_qy_tree_left .is-opened span{
    color: #333!important;
}
.table_qy_tree_left .el-submenu__title{
    background-color: #fff !important;
}
.el-select .el-input.is-focus .el-input__inner{
    border-color: #0D4AA4;
}
.el-button--primary.is-active, .el-button--primary:active{
    background-color: #0D4AA4;
    border-color: #0D4AA4;
}
.el-button--primary.is-active, .el-button--primary:hover{
    background-color: #0D4AA4;
    border-color: #0D4AA4;
}
.el-button--primary.is-active, .el-button--primary:visited{
    background-color: #0D4AA4;
    border-color: #0D4AA4;
}
.el-select .el-input__inner:focus{
    border-color: #0D4AA4;
}
.el-select:hover{
    border-color: #0D4AA4;
}
.el-input__inner:hover{
    border-color: #0D4AA4;
}
.el-input__inner:focus{
    border-color: #0D4AA4;
}
.el-pagination__sizes .el-input .el-input__inner:hover{
    border-color: #0D4AA4;
}
.el-pager li.active{
    color: #0D4AA4;
}
.el-pager li:hover{
    color: #0D4AA4;
}
.el-button--primary.is-active, .el-button--primary:hover{
    background-color: #0D4AA4;
    border-color: #0D4AA4;
}
.table_overflow{
    overflow-y: auto;
    width: 100%
}
#app .hideSidebar .main-container{
    height: 100%;
}
.hasTagsView .app-main{
    height: calc(100% - 105px);
    width: calc(100% - 8px);
}
.gjcx{
    height: 41px;
    line-height: 41px;
    margin:0 30px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #0D4AA4;
    letter-spacing: 0;
}
.table_qy{
    height: 100%;
    width: 100%;
    background-color: #FFFFFF;
}
.table_qy_gl{
    //height: 100%;
    width: 100%;
    background-color: #FFFFFF;
}
.table_qy div{
    //border: 0;
}
.el-table--border, .el-table--group{
    border-color: #dfe6ec;
}
.el-table{
    border: 1px solid #dfe6ec!important;
}
.el-table::before{
    background-color: transparent;
}
.el-table--border::after, .el-table--group::after{
    background-color: #dfe6ec;
}
.el-table th{
    background-color: #eef3fd;
    color: #40537A;
    font-family: PingFangSC-Semibold;
    font-weight: 500;
}
.el-table--striped .el-table__body tr.el-table__row--striped td{
    background-color: #EEF3FD;
}
.el-table th.is-leaf, .el-table td{
    border-color: #dfe6ec;
}
.el-table td, .el-table th{
    padding: 0 0!important;
}
.el-table th>.cell{
    padding-top: 7px;
    padding-bottom: 7px;
}
.cxk_qy{
    height: auto;
    width: 100%;
    text-align: left;
    font-size: 14px;
    padding-left: 20px;
}
.gnan{
    height: 30px;
    width: 100%;
    text-align: left;
    font-size: 14px;
    padding-left: 20px;
    margin-bottom: 15px;
}
.lb_title{
    position: relative;
    text-indent: 12px;
    height:30px;
    width: 100%;
    text-align: left;
    font-size: 14px;
    padding-left: 20px;
    font-weight: bold;
}
.table_padding{
    padding: 0 20px;
}
.table_content{
    width: 100%;
}
.el-dropdown:active{
    border: 0;
}
.el-popper{
    margin-top: 2px;
}
.button_search{
    background: #0D4AA4;
    border-radius: 4px;
    padding: 7px 20px;
}
.button_reset{
    background: #FFFFFF;
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    color: #333;
    padding: 7px 20px;
}
.button_delete{
    background: #DB5043;
    border: 1px solid #DB5043;
    border-radius: 4px;
    padding: 7px 20px;
}
.el-form-item{
    //margin-right: 25px;
    //margin-bottom: 0;
}
.lb_title:before{
    content: '';
    width: 5px;
    height: 16px;
    position: absolute;
    top: 5px;
    left: 16px;
    background: #0D4AA4;
    box-shadow: 0 2px 6px 0 rgba(21,68,176,0.20);
    border-radius: 5px;
}
.pageIn{
    height: 45px;
    width: 100%;
    display:flex;
    text-align: center;
    align-items:center;
    justify-content: center;
    line-height: 100%;
    margin-top: 0;
}
/*login*/
.login_content{
    width: 100vw;
    min-width: 1040px;
    height: 100vh;
    min-height: 560px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 25px 0 0 40px;
    box-sizing: border-box;
    position: relative;
.loge_loge{
    width: 288px;
    height: 35px;
    background-size: 100%;
}
.login_nr{
    width: 386px;
    height: 460px;
    position: absolute;
    top: calc(50% - 230px);
    left: calc(50% - 193px);
    background: #FFFFFF;
    box-shadow: 0 7px 36px 0 rgba(0,0,0,0.38);
    border-radius: 4px;
    padding: 55px 60px 45px;
    box-sizing: border-box;
}
.login_dlfs{
    width: 100%;
    display: inline-block;
    margin: 0 auto;
    font-size: 14px;
    font-family: PingFangSC-Semibold;
    letter-spacing: 0;
    color: #FF9412;
    height: 30px;
    line-height: 30px;
    text-align: center;
.dlfs_active{
    font-size: 17px;
    color: #0D4AA4!important;
    font-weight: bold;
}
.tyrz_icon{
    margin-left: 5px;
}
.fgf{
    font-size: 23px;
    position: relative;
    top: -3px;
    margin:0 5px;
    color: #0D4AA4!important;
}
}
.srk_qy{
    margin-top: 20px;
    line-height: 40px;
    color: #333333;
    font-size:  16px;
    font-family: PingFangSC-Regular;
.ssk{
    position: relative;
    box-sizing: border-box;
}
.ssk_big{
    width: 100%;
    background: #FFFFFF;
    height: 40px;
    border: 0;
    padding-left: 40px;
    text-align: justify;
    border-bottom: 1px solid #999;
    margin-bottom: 16px;
}
.yhid{
    position: absolute;
    left: 10px;
}
.dh{
    position: absolute;
    right: 7px;
}
.ssk_small{
    width: calc(100% - 110px);
    height: 40px;
    border: 0;
    padding-left: 40px;
    box-sizing: border-box;
    text-align: justify;
    border-bottom: 1px solid #999;
    margin-bottom: 5px;
}
.ssk_yzm{
    position: absolute;
    right: 0;
    width: 100px;
    height: 40px;
    border: 0;
}
.ssk input:active{
    border: 0;
    -webkit-tap-highlight-color:rgba(255,0,0,0);
    outline:none;
    border-bottom: 1px solid #ededed;
}
.ssk input:focus{
    border: 0;
    -webkit-tap-highlight-color:rgba(255,0,0,0);
    outline:none;
    border-bottom: 1px solid #ededed;
}
.ssk input:visited{
    border: 0;
    -webkit-tap-highlight-color:rgba(255,0,0,0);
    outline:none;
    border-bottom: 1px solid #ededed;
}
.login_checkbox{
    border: 1px solid #dcdfe6;
}
.el-checkbox__label{
    position: relative;
    top: -2px;
    padding-left: 5px;
}
.ssk .icon{
    width: 25px;
    height: 25px;
    display: inline-block;
    position: absolute;
    left: 10px;
    top: 7px;
    color: #8295BA;
    font-size: 16px;
    line-height: 25px;
}
.dtan_fh{
    height: 40px;
    width: 100% !important;
    margin-top: 1.8rem;
    text-align: center;
    font-size: 16px;
    line-height: 40px;
    color: #fff;
    letter-spacing:5px;
    background-image: linear-gradient(180deg, #199FF0 0%, #406FD8 100%);
    background: #0D4AA4;
    border: 1px solid #DEDEDE;
    border-radius: 24px;
}
.dlgn{
    width: 100%;
    font-family: PingFangSC-Regular;
    font-size: 1rem;
    color: #FFFFFF;
    height: calc(100vh - 31rem);
    line-height:40px;
}
.wjmm{
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #999999;
    letter-spacing: 0;
    width: 100%;
    text-align: center;
    margin-top: 8px;
    line-height: 20px;
}
}
.login_footer{
    width: 100%;
    position: absolute;
    bottom: 20px;
    opacity: 0.78;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
}
}
.sbj{
    width: 100%;
    margin-top: 20px !important;
}
.el-table__body tr td{
    height: 40px !important;
    min-height: 40px !important;
}
.el-table__body tr td .el-button--text{
    padding: 0;
}
.hasBorder{
    border: 1px;
}
.col100 .el-form-item,
.col100 .el-textarea{
    width: 100%;
}
.col100  .el-form-item__content{
    width: calc(100% - 595px);
}
.app_content{
    overflow: auto;
}
.hasTagsView{
    height: 100%;
}
