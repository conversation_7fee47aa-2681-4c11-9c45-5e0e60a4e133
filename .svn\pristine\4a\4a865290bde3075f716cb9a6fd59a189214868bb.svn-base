<template>
<span class="number-grow" ref="numberGrow">{{currentNumber}}</span>
</template>

<script>
export default {
  name: 'NumberGrow',
  props: {
    // 实际数字
    value: {
      type: Number,
      default: 0
    },
    // 变换时间
    time: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentNumber: 0
    };
  },
  watch: {
    value: {
      handler() {
        this.numberGorw();
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 数字变换
     */
    numberGorw() {
      const step = (this.value * 10) / (this.time * 1000);
      let timer = null;
      let temp = 0;
      // eslint-disable-next-line no-return-assign
      const timerFn = () => timer = setTimeout(() => {
        temp += step;
        if (temp > this.value) {
          clearTimeout(timer);
          this.currentNumber = this.value;
        } else {
          this.currentNumber = parseInt(temp, 10);
        }
        timerFn();
      }, 10);
      timerFn();
    }
  }
};
</script>

<style scoped>

</style>
