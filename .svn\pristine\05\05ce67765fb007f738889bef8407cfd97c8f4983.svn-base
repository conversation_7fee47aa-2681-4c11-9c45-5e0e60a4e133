<template>
  <div class="dybzx-page-content">
    <div class="jksqgl-pageform-header">
      <span class="zhxy-form-label" slot="label">统计信息</span>
    </div>
    <div v-for="(item,id) in dyjk" :key="id" class="jbxx-page-content-top">
      <div class="tjxx-page-content-first">
        <div class="tjxx-page-content-first-title">{{ item.time }}</div>
        <div class="tjxx-page-content-first-mid">
          <div class="tjxx-page-content-first-mid-left">
            <p class="tjxx-page-content-first-number">{{ item.sumnumber }}</p>
            <p class="tjxx-page-content-first- written">总计</p>
          </div>
          <div class="tjxx-page-content-first-mid-centre">
            <p class="tjxx-page-content-first-number-centre">{{ item.succnumber }}</p>
            <p class="tjxx-page-content-first-written">成功</p>
          </div>
          <div class="tjxx-page-content-first-mid-right">
            <p class="tjxx-page-content-first-number-right">{{ item.fallnumber }}</p>
            <p class="tjxx-page-content-first- written">失败</p>
          </div>
        </div>
        <div class="tjxx-page-content-first-last">
          <p class="tjxx-page-content-first-last-title">平均响应时间:</p>
          <p class="tjxx-page-content-first-last-number">{{ item.xytime }}</p></div>
      </div>
    </div>
    <div class="jksqgl-pageform-header">
      <span class="zhxy-form-label" slot="label">调用记录</span>
    </div>
    <div>
      <div class="tjxx-page-content-tyjl">
        <div class="block">
          <span class="demonstration">创建时间</span>
          <el-date-picker
            v-model="value"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right">
          </el-date-picker>
        </div>
        <div>
          <span class="demonstration">结果状态</span>
          <el-select v-model="ztform.zt" placeholder="请选择" >
            <el-option v-for="item in ztoptions" :key="item.id" :label="item.zt" :value="item.id"></el-option>
          </el-select>
        </div>
        <div class="tjxx-page-content-tyjl-anniu">
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
        </div>
      </div>
    </div>
    <div style="width: 100%; padding-top: 10px">
      <el-table :data="tablerecord" height="250" border style="width: 100%">
        <el-table-column prop="zxzt" label="执行状态:" width="200"></el-table-column>
        <el-table-column prop="zxsj" label="执行开始时间:" width="300"></el-table-column>
        <el-table-column prop="time" label="执行时间(ms):" width="300"></el-table-column>
        <el-table-column prop="message" label="调用日志信息:" ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dbsxhq',
  props: {
    dyjk: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      ztform: {
        zt: ''
      },
      tablerecord: [
        {
          zxzt: '成功',
          zxsj: '2021-05-18',
          time: '20ms',
          message: 'username为: 20110120;系统代码为: 0; appid为QYWX. @DY20191014.'
        },
        {
          zxzt: '成功',
          zxsj: '2021-05-18',
          time: '20ms',
          message: 'username为: 20110120;系统代码为: 0; appid为QYWX. @DY20191014.'
        },
        {
          zxzt: '成功',
          zxsj: '2021-05-18',
          time: '20ms',
          message: 'username为: 20110120;系统代码为: 0; appid为QYWX. @DY20191014.'
        },
        {
          zxzt: '成功',
          zxsj: '2021-05-18',
          time: '20ms',
          message: 'username为: 20110120;系统代码为: 0; appid为QYWX. @DY20191014.'
        }
      ],
      ztoptions: [
        {
          id: '000000',
          zt: '全部'
        },
        {
          id: '000001',
          zt: '成功'
        },
        {
          id: '000002',
          zt: '失败'
        }],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      value: ''
    };
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    box-sizing: border-box;
  }
}

.jksqgl-pageform-header{
  height: 50px;
  padding:0 20px;
}
.jbxx-page-content-top {
  width: 33.33%;
  display: flex;
  justify-content: space-between;
  padding-left: 100px;
  background-color: #FFFFFF;
}

.tjxx-page-content-first {
  width: 100%;
  height: 350px;
  padding-top: 30px;
  padding-left: 15px;

  .tjxx-page-content-first-title {
    background-color: #ededed;
    width: 400px;
    height: 50px;
    color: #3a8ee6;
    line-height: 50px;
    padding: 0px 50px;
    box-sizing: border-box;
  }

  .tjxx-page-content-first-mid {
    display: flex;
    justify-content: space-between;
    width: 400px;
    height: 200px;
    border: #ededed 1px solid;
    border-bottom: none;
    padding: 50px 50px 0px;
    text-align: center;
    box-sizing: border-box;

    .tjxx-page-content-first-mid-left {
      .tjxx-page-content-first-number {
        color: #2d8cf0;
      }

      .tjxx-page-content-first- written {
      }
    }

    .tjxx-page-content-first-mid-centre {
      .tjxx-page-content-first-number-centre {
        color: darkorange;
      }
    }

    .tjxx-page-content-first-mid-right {
      .tjxx-page-content-first-number-right {
        color: red;
      }
    }
  }

  .tjxx-page-content-first-last {
    background-color: #FFFFFF;
    width: 400px;
    height: 40px;
    display: flex;
    justify-content: center;
    line-height: 40px;
    border: #ededed 1px solid;
    border-top: none;
    box-sizing: border-box;

    .tjxx-page-content-first-last-title {
      color: #515a6e;
    }

    .tjxx-page-content-first-last-number {
      color: green;
    }
  }
}
.tjxx-page-content-tyjl{
  display: flex;
  margin-top: 20px;
  box-sizing: border-box;
  .demonstration{
    padding: 0px 20px;
  }
}
.tjxx-page-content-tyjl-anniu{
  padding-left: 400px;
}
</style>
