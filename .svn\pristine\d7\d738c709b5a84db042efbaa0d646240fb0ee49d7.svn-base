<template>
  <div ref="myChart" style="width: 100%;height: 100%;box-sizing: border-box;"></div>
</template>

<script>
// 引入基本模板
import { echarsMixins } from '@/app/ywxtgl_01_01/mixins/echars-resize';

import * as echarts from 'echarts/core';
// 引入各种图表，图表后缀都为 Chart
import {
  BarChart
} from 'echarts/charts';
// 引入提示框，标题，直角坐标系等组件，组件后缀都为 Component
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
  // GeoCoComponent
} from 'echarts/components';
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import {
  CanvasRenderer
} from 'echarts/renderers';

// 注册必须的组件
echarts.use(
  [TitleComponent, TooltipComponent, GridComponent, LegendComponent, BarChart, CanvasRenderer]
);

export default {
  mixins: [echarsMixins],
  props: {
    data: {
      type: Object
    },
    color: {
      type: String,
      default() {
        return '#3398DB';
      }
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    data(val) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);
      this.echarsDom = myChart;

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              show: true
            }
          }
        },
        grid: {
          left: '4%',
          top: '15%',
          right: '5%',
          bottom: '10%',
          containLabel: true
        },
        legend: {
          data: ['采集量', '执行时间'],
          bottom: '10',
          textStyle: {
            color: '#396A87',
            fontSize: 12
          }
        },
        xAxis: {
          // data: [
          //   '任务1',
          //   '任务2',
          //   '任务3',
          //   '任务4',
          //   '任务5',
          //   '任务6',
          //   '任务7',
          //   '任务8',
          //   '任务9',
          //   '任务10'
          // ],
          data: this.data.xData,
          axisLine: {
            show: true, // 隐藏X轴轴线
            lineStyle: {
              color: '#D9D9D9',
              width: 1
            }
          },
          axisTick: {
            show: true, // 隐藏X轴刻度
            alignWithLabel: true
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#396A87', // X轴文字颜色
              fontSize: 12
            },
            interval: 0,
            rotate: 60
          }
        },
        yAxis: [{
          type: 'value',
          name: '采集量',
          nameTextStyle: {
            color: '#396A87',
            fontSize: 12
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#396A87',
              fontSize: 12
            }
          }
        },
        {
          type: 'value',
          name: '执行时间',
          nameTextStyle: {
            color: '#396A87',
            fontSize: 12
          },
          position: 'right',
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#396A87',
              width: 2
            }
          },
          axisLabel: {
            show: true,
            formatter: '{value} min', // 右侧Y轴文字显示
            textStyle: {
              color: '#396A87',
              fontSize: 12
            }
          }
        }
        ],
        series: [
          {
            name: '采集量',
            type: 'bar',
            barWidth: 18,
            itemStyle: {
              normal: {
                color: '#E29052'
              }
            },
            // data: [133, 23, 114, 67, 89, 35, 67, 96, 90, 46, 75, 85]
            data: this.data.yData1
          },
          {
            name: '执行时间',
            type: 'line',
            yAxisIndex: 1, // 使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
            showAllSymbol: true, // 显示所有图形。
            symbol: 'circle', // 标记的图形为实心圆
            symbolSize: 6, // 标记的大小
            itemStyle: {
              // 折线拐点标志的样式
              color: '#26D9FF',
              borderColor: '#26D9FF',
              width: 2,
              shadowColor: '#26D9FF',
              shadowBlur: 2
            },
            lineStyle: {
              color: '#26D9FF',
              width: 2,
              shadowBlur: 2
            },
            // data: [4.2, 3.5, 2.9, 7.8, 2, 3, 4.2, 3.5, 2.9, 7.8, 2, 3]
            data: this.data.yData2
          }
        ]
      };

      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
