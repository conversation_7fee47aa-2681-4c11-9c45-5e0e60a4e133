<template>
  <div class="sidebar">
    <template v-if="hasOneShowingChild(item.children,item)">
        <el-menu-item @click="goMenuPath(item)" :index="item.path" class="submenu-title-noDropdown">
          <i :class="noIconStatus(item)"></i>
          <span :class="item.icon ? '' : 'icon-tab'" slot="title">{{item.name}}</span>
        </el-menu-item>
    </template>

    <el-submenu v-else ref="subMenu" :index="item.id">
      <template slot="title">
        <i :class="item.icon"></i>
        <span :class="item.icon ? '' : 'icon-tab'" slot="title">{{item.name}}</span>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.id"
        :collapse="collapse"
        :item="child"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>
<script>

export default {
  name: 'SidebarItem',
  components: {},
  props: {
    collapse: {
      type: Boolean,
      default: false
    },
    // route object
    item: {
      type: Object,
      required: true
    }
  },
  data() {
    return {};
  },
  computed: {
    noIconStatus() {
      return (item) => {
        if (!item.icon && this.collapse) {
          return 'el-icon-menu';
        }
        return item.icon || '';
      };
    }
  },
  methods: {
    goMenuPath(route) {
      if (route.isOut === '1') {
        this.$emit('rollback');
        window.open(route.path, '_blank');
        return;
      }
      if (route.openType === '2') {
        this.$emit('rollback');
        window.open(`${window.location.origin}/#${route.path}`, '_blank');
      } else {
        this.$router.push(route.path);
      }
    },
    hasOneShowingChild(children = [], parent) {
      if (children.length === 0) {
        return true;
      }
      return false;
    }
  }
};
</script>
<style lang="scss" scoped>
.icon-tab{
  margin-left: 29px;
}
</style>
