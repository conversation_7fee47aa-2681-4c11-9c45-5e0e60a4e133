<template>
  <div class="info-table">
    <el-table
      class="zhxy-table"
      :data="formData"
      stripe
      border
      height="100%">
      <el-table-column prop="anmc" label="按钮名称"></el-table-column>
      <el-table-column
        :property="radio"
        width="200">
        <template slot="header" slot-scope="scope">
          <span v-if="false">{{scope.$index}}</span>
          <el-radio-group @change="radioChange" v-model="radio">
            <el-radio label="0">启用</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </template>
        <template slot-scope="scope">
          <el-radio-group @change="sfkyChange" v-model="scope.row.kzqx">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="按钮别名">
        <template slot-scope="scope">
          <el-input v-model="scope.row.anbm" size="small"></el-input>
        </template>
      </el-table-column>
    </el-table>
  </div>

</template>

<script>
export default {
  props: {
    // 按钮数据
    formData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      radio: '' // all radio
    };
  },
  watch: {

  },
  methods: {
    // 是否可用 radio ALL 切换 事件
    sfkyChange() {
      let count = 0;
      this.formData.forEach((item) => {
        if (item.kzqx === 1) {
          count++;
        }
      });
      if (count === 0) {
        this.radio = '1';
      }
      if (count === this.formData.length) {
        this.radio = '0';
      }
      if (count > 0 && count < this.formData.length) {
        this.radio = '';
      }
    },
    /**
     * row 内 radio 切换效果
     * @param val
     */
    radioChange(val) {
      if (val === '') {
        return;
      }
      if (val === '0') {
        this.formData.forEach((item) => {
          item.kzqx = 1;
        });
      } else {
        this.formData.forEach((item) => {
          item.kzqx = 0;
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
  .info-table {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    height: 310px;
  }
</style>
