<template>
  <div class="dialog-content">
    <el-form ref="yhForm" :model="FormData" :rules="rules" label-width="100px"
             label-position="right">
      <el-form-item label="用户ID" prop="yhid">
        <el-input :disabled="!yhidVisible" v-model="FormData.yhid" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="用户姓名" prop="xm">
        <el-input v-model="FormData.xm" placeholder="必填" size="small"/>
      </el-form-item>
      <el-form-item label="密码" prop="mm">
        <el-input v-model="FormData.mm" placeholder="密码为数字或特殊字符" size="small"/>
      </el-form-item>
      <el-form-item label="手机号" prop="sjh">
        <el-input v-model="FormData.sjh" placeholder="选填" size="small"/>
      </el-form-item>
      <el-form-item label="电子邮箱" prop="dzyx">
        <el-input v-model="FormData.dzyx" placeholder="选填" size="small"/>
      </el-form-item>
      <el-form-item label="身份描述" prop="sfms">
        <el-input v-model="FormData.sfms" placeholder="选填" size="small"/>
      </el-form-item>
      <el-form-item prop="xm" label="是否自建" style="margin-bottom: 10px !important;">
        <el-radio-group v-model="FormData.xm">
          <el-radio :label="1">自建</el-radio>
          <el-radio :label="0">非自建</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="用户状态">
        <el-radio-group v-model="FormData.radio">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="可登录方式">
        <el-radio-group v-model="FormData.radio">
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">统一身份认证</el-radio>
          <el-radio :label="3">其他方式登录</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="bz">
        <el-input type="textarea" :rows="3" v-model="FormData.bz"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Yhgl',
  props: {
    FormData: {
      type: Object,
      default: () => ({
        yhid: '111',
        xm: '222',
        mm: '333',
        sjh: '',
        dzyx: '',
        sfms: '',
        sfzj: 1,
        kdlfs: '1100000000',
        yhzt: 1,
        sjlybz: '',
        bz: '',
        bmmc: undefined,
        jsmc: ''
      })
    },
    yhidVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 用户ID校验
    // eslint-disable-next-line consistent-return
    const validateYhid = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入工号'));
      }
      callback();
    };

    // 姓名校验
    // eslint-disable-next-line consistent-return
    const validateXm = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入姓名'));
      }
      callback();
    };
    return {
      rules: {
        yhid: [{
          required: true,
          trigger: 'blur',
          validator: validateYhid
        }],
        xm: [{
          required: true,
          trigger: 'blur',
          validator: validateXm
        }],
        mm: [{
          required: true,
          trigger: 'blur',
          message: '请输入密码'
        },
        {
          min: 8,
          message: '密码长度不能少于8位'
        },
        {
          pattern: /(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[\W])(?=.*[\S])^[0-9A-Za-z\S]{8,16}$/g,
          message: '请输入正确的密码格式（必须8-16位且包含大写字母,小写字母,数字,特殊符号）'
        }
        ]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 10px;
  }
</style>
