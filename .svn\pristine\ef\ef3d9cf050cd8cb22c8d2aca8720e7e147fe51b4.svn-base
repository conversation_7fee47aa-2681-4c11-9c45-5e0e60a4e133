<template>
  <div class="zhxy-form zhxy-form-search-part">
    <el-form label-width="120px" inline ref="searchForm" :model="formData"
             class="zhxy-form zhxy-form-search-part">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">部门码/名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.bmmmc" placeholder="部门码/名称"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">是否二级部门</span>
        <el-select class="zhxy-form-inline" v-model="formData.sfejbm" placeholder="是否二级部门"
                   size="small">
          <el-option
            v-for="item in partOptions"
            :key="item.value"
            :label="item.lable"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">是否为保密行政管理单位</span>
        <el-select class="zhxy-form-inline" v-model="formData.sfwbmxzgldw" placeholder="是否为保密行政管理单位"
                   size="small">
          <el-option
            v-for="item in isBmOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item v-show="isShowLabel">-->
<!--        <span class="zhxy-form-label" slot="label">人事机构码</span>-->
<!--        <el-input class="zhxy-form-inline" v-model="formData.rsjgm" placeholder="人事机构码"-->
<!--                  size="small"></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item v-show="isShowLabel">-->
<!--        <span class="zhxy-form-label" slot="label">本科生学院码</span>-->
<!--        <el-input class="zhxy-form-inline" v-model="formData.bksxym" placeholder="本科生学院码"-->
<!--                  size="small"></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item v-show="isShowLabel">-->
<!--        <span class="zhxy-form-label" slot="label">研究生学院码</span>-->
<!--        <el-input class="zhxy-form-inline" v-model="formData.yjsxym" placeholder="研究生学院码"-->
<!--                  size="small"></el-input>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
        <p class="search-fold"
           @click="()=>{this.isShowLabel = !this.isShowLabel}">
          {{ this.isShowLabel ? '收缩' : '展开' }}
          <i :class="!this.isShowLabel ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></p>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'DepartmentSubSearch',
  data() {
    return {
      // 下级部门搜索展开
      isShowLabel: false,
      // 下级部门搜索form数据
      formData: {
        bmmmc: '',
        sfejbm: '',
        sfwbmxzgldw: '',
        rsjgm: '',
        bksxym: '',
        yjsxym: ''
      },
      isBmOptions: [
        {
          value: 0,
          label: '否'
        },
        {
          value: 1,
          label: '是'
        }
      ],
      partOptions: [
        {
          value: 0,
          lable: '否'
        },
        {
          value: 2,
          lable: '是'
        }
      ]
    };
  },
  methods: {
    reset() {
      this.formData = {
        bmmmc: '',
        sfejbm: '',
        sfwbmxzgldw: '',
        rsjgm: '',
        bksxym: '',
        yjsxym: ''
      };

      this.search();
    },
    search() {
      this.$emit('subSearch', this.formData);
    }
  }
};
</script>

<style scoped>

</style>
