import { createAPI } from '@/utils/request.js';

// 接口地址映射
const BASE_URL = '';

// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/xtgl_02_01/jcqxBm`;

// ！ 接口别名的命名需要遵循  类名 + 方法名，风格需遵循驼峰风格（首字母小写）  注：idea中可安装CamelCase，使用快捷键 shift + alt + u 自动转换为驼峰效果。

// 查询单条代码分类数据
export const findBmList = (data) => createAPI(`${APP_PRE}/findBmList`, 'get', data);

export const findBmByBmm = (data) => createAPI(`${APP_PRE}/findBmByBmm`, 'get', data);

export const findSjbmList = (data) => createAPI(`${APP_PRE}/findSjbmList`, 'get', data);

export const findBmListByPageFbmm = (data) => createAPI(`${APP_PRE}/findBmListByPageFbmm`, 'get', data);

export const findYhListByPageBmm = (data) => createAPI(`${APP_PRE}/findYhListByPageBmm`, 'get', data);

export const findYhListByBmm = (data) => createAPI(`${APP_PRE}/findYhListByBmm`, 'get', data);

export const updateBm = (data) => createAPI(`${APP_PRE}/updateBm`, 'post', data);

export const addBm = (data) => createAPI(`${APP_PRE}/addBm`, 'post', data);

export const deleteBmBatch = (data) => createAPI(`${APP_PRE}/deleteBmBatch`, 'post', data);

export const drBmBatch = (data) => createAPI(`${APP_PRE}/drBmBatch`, 'post', data);

export const deleteYhBm = (data) => createAPI(`${APP_PRE}/deleteYhBm`, 'post', data);

export const deleteYhBmBatch = (data) => createAPI(`${APP_PRE}/deleteYhBmBatch`, 'post', data);
