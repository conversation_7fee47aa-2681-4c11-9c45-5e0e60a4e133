<template>
  <div id="app" class="zoom" v-loading="pageInitLoading">
    <router-view />
  </div>
</template>
<script>
import { mapState } from 'vuex';

export default {
  computed: {
    ...mapState('layout_03_01/user', ['roleLoading']),
    // 页面 初始化加载loading
    pageInitLoading() {
      return this.roleLoading && this.$route.path !== '/login' && this.$route.path !== '/login_common';
    }
  }
};
</script>

<style lang="scss">
html,body{
  width: 100%;
  height: 100%;
  font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  font-size: $page-font-size;
}
#app{
  width: 100%;
  height: 100%;
}
.zoom{
  zoom: 1.25;
}
</style>
