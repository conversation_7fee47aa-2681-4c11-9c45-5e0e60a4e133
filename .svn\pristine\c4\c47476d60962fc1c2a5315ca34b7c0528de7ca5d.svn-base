<template>
  <div class="lcgl-content">
    <div class="lcgl-content-main">
      <v-title name="流程配置"></v-title>
      <div class="content">
        <div class="table">
          <!--      按钮-->
          <div class="button-list">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="addTable">
              新建
            </el-button>
          </div>
          <!--          环节基础信息-->
          <info-table
            ref="singleTable"
            :loading="tableLoading"
            @modify="modify"
            @deleteItem="deleteItem"
            @buttonConfig="buttonConfig"
            @selectTarget="selectTarget"
            @getTargetTable="getTargetTable"
            :table-data="tableData">
          </info-table>
        </div>
      </div>
      <div class="content">
        <div class="table">
          <!--      按钮-->
          <div class="title">
            <v-title name="目标环节"></v-title>
          </div>
          <target-info-table
            v-loading="targetTableLoading"
            @selectHandleType="selectHandleType"
            @selectHandleTarget="selectHandleTarget"
            @deleteTarget="deleteTarget"
            @getTargetData="getTargetData"
            :table-data="targetTable">
          </target-info-table>
        </div>
      </div>
    </div>
    <!--    环节新建编辑弹窗-->
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="500px">
      <div>
        <edit-info-table ref="editForm" :form-data="editForm"></edit-info-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="addButtonLoading" size="small" type="primary" @click="addTableCertain">确 定</el-button>
        <el-button size="small" @click="addTableClose">取 消</el-button>
      </span>
    </el-dialog>
    <!--    按钮配置弹窗-->
    <el-dialog
      title="按钮权限配置"
      :visible.sync="visibleButton"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="500px">
      <div>
        <edit-button :form-data="buttonForm"></edit-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="buttonConfigCertainLoading" size="small" type="primary"
                   @click="buttonConfigCertain">确 定</el-button>
        <el-button size="small" @click="buttonConfigCancel">取 消</el-button>
      </span>
    </el-dialog>
    <!--    办理类型弹窗-->
    <el-dialog
      title="办理类型"
      :visible.sync="visibleType"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="500px">
      <div>
        <el-radio-group v-model="radio">
          <el-radio v-for="item in radioList" :label="item.value" :key="item.value">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="this.handleTypeCertainLoading" size="small" type="primary" @click="handleTypeCertain">确 定</el-button>
        <el-button size="small" @click="handleTypeCancel">取 消</el-button>
      </span>
    </el-dialog>
    <!--    选择目标环节弹窗-->
    <el-dialog
      title="选择目标环节"
      :visible.sync="visibleTarget"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="500px">
      <div style="height: 300px">
        <edit-target-table ref="targetTable" v-loading="selectTargetLoading"
                           :table-data="targetTableData"></edit-target-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="targetTableCertainLoading" size="small" type="primary"
                   @click="targetTableCertain">确 定</el-button>
        <el-button size="small" @click="targetTableCancel">取 消</el-button>
      </span>
    </el-dialog>
    <!--    选择处理目标弹窗-->
    <el-dialog
      title="选择处理目标"
      :visible.sync="visibleHandleTarget"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      width="500px">
      <div style="height: 400px;overflow: auto">
        <handle-target-table
          ref="handleTargetTable"
          @getHandleTargetRow="getHandleTargetRow"
          @selectHandleTargetDetail="selectHandleTargetDetail"
          :table-data="handleTargetTableData">
        </handle-target-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="handleTargetTableCertainLoading" size="small" type="primary" @click="handleTargetTableCertain">确 定</el-button>
        <el-button size="small" @click="handleTargetTableCancel">取 消</el-button>
      </span>
    </el-dialog>
    <!--    岗位类型列表-->
    <el-dialog
      title="岗位类型列表"
      :visible.sync="visiblePost"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="800px">
      <div style="height: 400px;overflow: auto">
        <post-table
          v-loading="TargetDetailLoading"
          ref="postTable"
          :table-data="postTableData">
        </post-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="postTableCertainLoading" size="small" type="primary" @click="postTableCertain">确 定</el-button>
        <el-button size="small" @click="postTableCancel">取 消</el-button>
      </span>
    </el-dialog>
    <!--    部门列表弹窗-->
    <el-dialog
      title="部门列表"
      :visible.sync="visibleDepartment"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="800px">
      <div style="height:400px;overflow: auto">
        <el-tree
          v-loading="treeLoading"
          show-checkbox
          :check-strictly="true"
          ref="deparmentTree"
          :props="defaultProps"
          highlight-current
          :default-checked-keys="departmentChecked"
          :data="departmentTreeData"
          node-key="bmm">
              <span slot-scope="{ node, data }">
                {{ data.bmmc }}
              </span>
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="departmentTableCertain">确 定</el-button>
        <el-button size="small" @click="departmentTableCancel">取 消</el-button>
      </span>
    </el-dialog>
    <!--    选择固定单位岗位-->
    <choose-user-dialog
      :title="chooseTitle"
      v-loading="TargetDetailLoading"
      :treeLoading="treeLoading"
      :treeDetailLoading="treeDetailLoading"
      :tree-data="chooseTree"
      :tree-detail="treeDetail"
      :defaultProps="defaultProps"
      :page="page"
      :pageSize="pageSize"
      :total="total"
      @searchInfo="searchInfo"
      ref="ChooseUserDialog"
      @nodeClick="nodeClick"
      @pageChange="pageChange"
      @cancel="closeFixedPost"
      @certain="getFixedPost">
    </choose-user-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import {
  lclistFind,
  lclistAdd,
  lclistUpdate,
  lcmxFind,
  lcBtnConfigFind,
  lcmcSave,
  lcBtnConfigSave,
  lclistDelete,
  lcmblistFind,
  lcmblistDelete,
  lclxSave,
  lcmxPostListFind,
  clmbFind,
  organizationFind,
  organizationDetail,
  organizationPeopleDetail,
  lcmxSave
} from '../../api/lcgl/lcgl';

// import RoleTreeDialog from './jsglComponents/RoleTreeDialog';
import VTitle from '../../../../components/title/VTitle';
import InfoTable from './lcglEditComponents/InfoTable';
import EditInfoTable from './lcglEditComponents/EditInfoTable';
import EditButton from './lcglEditComponents/EditButton';
import EditTargetTable from './lcglEditComponents/EditTargetTable';
import TargetInfoTable from './lcglEditComponents/TargetInfoTable';
import HandleTargetTable from './lcglEditComponents/HandleTargetTable';
import PostTable from './lcglEditComponents/PostTable';
import ChooseUserDialog from './lcglEditComponents/ChooseUserDialog';

export default {
  components: { // 注册组件
    VTitle,
    InfoTable,
    EditInfoTable,
    EditButton,
    EditTargetTable,
    TargetInfoTable,
    HandleTargetTable,
    PostTable,
    ChooseUserDialog
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chooseTitle: '选择单位固定岗位',
      treeLoading: false, // 人员组件 tree loading
      treeDetailLoading: false, // 人员组件tree 详情 loading
      tableLoading: false, // table (上) 加载loading
      addButtonLoading: false, // 新建button loading
      selectTargetLoading: false, // 获取目标环节弹窗loading
      targetTableCertainLoading: false, // 获取目标环节确定loading
      buttonConfigCertainLoading: false, // 按钮权限配置弹窗loading
      targetTableLoading: false, // 环节明细table loading
      handleTypeCertainLoading: false, // 环节类型弹窗确定 loading
      TargetDetailLoading: false, // 岗位弹窗数据 loading
      postTableCertainLoading: false, // 岗位弹窗确定 loading
      handleTargetTableCertainLoading: false, // 处理目标弹窗确定 loading
      radio: '', // 按钮权限配置 radio
      visibleButton: false, // 按钮权限配置
      visibleTarget: false, // 选择目标环节弹窗visible
      visibleType: false, // 选择办理类型弹窗visible
      visibleHandleTarget: false, // 选择处理目标弹窗visible
      visiblePost: false, // 选择岗位弹窗visible
      visibleDepartment: false, // 部门列表弹窗visible
      tempHjid: '', // 当前选中的hjid
      currentRow: {}, // 上半部分table 点击current
      handleTargetRow: {}, // 获取处理目标明细
      currentHjRow: {}, // 当前选择的环节明细点击data
      currentVal: {}, // 当前流程单击数据
      buttonForm: [], // 编辑按钮权限弹窗数据
      visible: false, // 新建/编辑 弹窗visible
      title: '新建环节', // 新建/编辑弹窗 title
      treeDetail: [], // 组织结构节点详情
      targetDataCurrent: {}, // 目标详情点击数据
      departmentChecked: [], // 部门列表选中数组key
      // 编辑默认值
      editForm: {
        fwdyid: this.id, // 服务ID
        hjid: '', // 环节ID
        hjmc: '', // 环节名称
        mbhj: '', // 目标环节
        bzxx: '', // 帮助信息
        pxh: '', // 排序号
        sfky: 1, // 是否可用
        cjr: '', // 创建人
        cjsj: '', // 创建时间
        bgr: '', // 变更人
        bgsj: '' // 变更时间
      },
      // 环节基础数据(上部分)
      tableData: [
        // {
        //   fwdyid: '', // 服务ID
        //   Hjid: '', // 环节ID
        //   Hjmc: '111', // 环节名称
        //   Mbhj: '', // 目标环节
        //   Bzxx: '1', // 帮助信息
        //   Pxh: '', // 排序号
        //   Sfky: 1, // 是否可用
        //   Cjr: '', // 创建人
        //   Cjsj: '', // 创建时间
        //   bgr: '', // 变更人
        //   Bgsj: '' // 变更时间
        // }
      ],
      // 目标环节基础数据(下部分)
      targetTable: [],
      // 选择目标环节弹窗 list
      targetTableData: [],
      // 办理类型 radiolist
      radioList: [
        {
          label: '单一签核',
          value: 1
        },
        {
          label: '多人单一签核',
          value: 2
        },
        {
          label: '多人全部处理',
          value: 3
        }
      ],
      // 处理类型table data
      handleTargetTableData: [],
      // 岗位列表弹窗数据
      postTableData: [],
      // tree组件 树结构属性别名
      defaultProps: {
        children: 'children',
        label: 'label',
        id: ''
      },
      defaultProp: {
        children: 'children',
        label: 'bmmc',
        id: 'bmm'
      },
      departmentTreeData: [],
      chooseTree: [],
      page: 1,
      pageSize: 50,
      total: 0
    };
  },
  watch: {},
  created() {
    this.getHjData();
  },
  methods: {
    /**
     * 获取环节列表数据
     */
    getHjData() {
      // 获取环节接口 fwdyid:服务定义ID
      this.tableLoading = true;
      lclistFind({ fwdyid: this.id }).then((res) => {
        this.tableData = res.data.content || [];
        const old = [];
        this.tableData.forEach((x) => {
          if (x.hjid === this.currentRow.hjid) {
            old.push(x);
          }
        });
        this.$nextTick(() => {
          if (old.length > 0) {
            this.$refs.singleTable.setCurrent(old[0]);
          } else {
            this.$refs.singleTable.setCurrent();
          }
          // this.$refs.singleTable.setCurrent(old);
        });
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    /**
     * 新建按钮点击事件
     */
    addTable() {
      this.visible = true;
      this.title = '新建环节';
      // reset
      this.editForm = {
        fwdyid: this.id, // 服务ID
        hjid: '', // 环节ID
        hjmc: '', // 环节名称
        mbhj: '', // 目标环节
        bzxx: '', // 帮助信息
        pxh: '', // 排序号
        sfky: 1, // 是否可用
        cjr: '', // 创建人
        cjsj: '', // 创建时间
        bgr: '', // 变更人
        bgsj: '' // 变更时间
      };
    },
    /**
     * 获取目标环节table(下半部分)
     * @param val
     */
    getTargetTable(val) {
      if (val) {
        this.currentRow = val;
      }
      this.tempHjid = this.currentRow.hjid;
      const params = {
        fwdyid: this.id,
        hjid: this.tempHjid
      };
      this.targetTableLoading = true;
      lcmblistFind(params).then((res) => {
        this.targetTable = res.data.content || [];
        this.targetTable.forEach((item) => {
          if (!item.bllx) {
            this.$set(item, 'bllx', '');
          }
          if (!item.clmb) {
            this.$set(item, 'clmb', '');
          }
        });
      }).finally(() => {
        this.targetTableLoading = false;
      });
    },
    /**
     * 选择目标环节 tableCheckList
     * @param val
     */
    selectTarget(val) {
      this.visibleTarget = true;
      // 接口数据获取/处理
      this.getTargetTableData(val);
    },
    /**
     * 获取目标环节数据接口
     */
    getTargetTableData(val) {
      this.selectTargetLoading = true;
      // data reset
      this.targetTableData = [];
      const params = {
        fwdyid: this.id,
        hjid: val.hjid
      };
      lcmxFind(params).then((res) => {
        this.targetTableData = res.data.content || [];
        const checkedList = this.targetTableData.filter((item) => item.check);
        this.$nextTick(() => {
          this.$refs.targetTable.toggleSelection(checkedList);
        });
      }).finally(() => {
        this.selectTargetLoading = false;
      });
    },
    /**
     * 选择环节列表 确定 按钮 事件
     */
    targetTableCertain() {
      this.targetTableCertainLoading = true;
      const params = {
        mbhj: this.$refs.targetTable.multipleSelection,
        fwdyid: this.id,
        hjid: this.tempHjid
      };
      lcmcSave(params).then((res) => {
        if (res.code === 200) {
          this.getHjData();
          this.getTargetTable();
        }
      }).finally(() => {
        this.targetTableCertainLoading = false;
        this.visibleTarget = false;
      });
    },
    /**
     * 选择环节列表 取消 按钮 事件
     */
    targetTableCancel() {
      this.visibleTarget = false;
    },
    /**
     * 新建/编辑 环节弹窗 确定按钮接口调用 事件
     */
    submitForm() {
      const form = cloneDeep(this.editForm);
      // 调用新建环节接口
      this.addButtonLoading = true;
      lclistAdd(form).then((res) => {
        if (res.code === 200) {
          this.$message.success('环节新建成功');
          // 刷新数据
          this.getHjData();
        }
      }).finally(() => {
        this.addButtonLoading = false;
        this.visible = false;
      });
    },
    /**
     * 新建/编辑 环节弹窗 确定按钮 事件
     */
    addTableCertain() {
      this.$refs.editForm.$refs.form.validate((valid) => {
        if (valid) {
          if (this.title === '新建环节') {
            this.submitForm();
          } else {
            this.modifyCertain();
          }
          return true;
        }
        console.log('error submit!!');
        return false;
      });
    },
    /**
     * 新建/编辑 环节弹窗 取消按钮 事件
     */
    addTableClose() {
      this.visible = false;
    },
    /**
     * 编辑环节 按钮点击事件
     * @param val
     */
    modify(val) {
      this.title = '编辑流程';
      this.visible = true;
      Object.assign(this.editForm, val);
    },
    /**
     * 编辑按钮 确定点击事件
     */
    modifyCertain() {
      const form = cloneDeep(this.editForm);
      // 调用新建环节接口
      this.addButtonLoading = true;
      lclistUpdate(form).then((res) => {
        if (res.code === 200) {
          this.$message.success('环节编辑成功');
          // 刷新数据
          this.getHjData();
          this.getTargetTable();
        }
      }).finally(() => {
        this.addButtonLoading = false;
        this.visible = false;
      });
    },
    /**
     * 按钮权限编辑 点击事件
     * @param val
     */
    buttonConfig(val) {
      this.title = '按钮权限配置';
      this.visibleButton = true;
      // 调用接口 获取 按钮权限列表
      const params = {
        fwdyid: this.id,
        hjid: val.hjid
      };
      lcBtnConfigFind(params).then((res) => {
        const list = res.data.content || [];
        const json = {
          1: '正式提交',
          2: '保存草稿',
          3: '通过',
          4: '拿回',
          5: '退回',
          6: '驳回'
        };
        list.forEach((item) => {
          item.anmc = json[item.anlx];
          if (!item.anbm) {
            item.anbm = json[item.anlx];
          }
        });
        this.buttonForm = list;
      });
    },
    /**
     * 按钮权限配置确定按钮 点击事件
     */
    buttonConfigCertain() {
      this.buttonConfigCertainLoading = true;
      // 调用接口保存
      const params = {
        hjan: this.buttonForm,
        fwdyid: this.id,
        hjid: this.tempHjid
      };
      lcBtnConfigSave(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('权限配置保存成功');
        }
      }).finally(() => {
        this.buttonConfigCertainLoading = false;
        this.visibleButton = false;
      });
    },
    /**
     * 按钮权限配置 取消按钮 点击事件
     */
    buttonConfigCancel() {
      this.visibleButton = false;
    },
    /**
     * 基础环节删除接口事件
     * @param id
     */
    deleteCurrent(params, instance, done) {
      lclistDelete(params).then((res) => {
        if (res.code === 200) {
          const index = this.tableData.findIndex((item) => item.hjid === params.hjid);
          if (index > -1) {
            this.tableData.splice(index, 1);
          }
          this.$message.success('删除成功');
          this.getTargetTable();
        }
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    /**
     * 删除按钮 点击事件
     * @param val
     */
    deleteItem(val) {
      const params = {
        fwdyid: this.id,
        hjid: val.hjid
      };
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteCurrent(params, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 暂存当前点击目标环节信息
     * @param row
     */
    getTargetData(row) {
      this.targetDataCurrent = row;
    },
    /**
     * 选择办理类型
     * @param val
     */
    selectHandleType(val) {
      this.visibleType = true;

      this.radio = val.bllx || '';
      this.currentHjRow = val;
    },
    /**
     * 办理类型弹窗 确定按钮事件
     */
    handleTypeCertain() {
      if (!this.radio) {
        this.$message.warning('请选择办理类型');
        return;
      }
      const params = cloneDeep(this.targetDataCurrent);
      params.bllx = this.radio;
      this.handleTypeCertainLoading = true;
      lclxSave(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('类型保存成功');
          this.currentHjRow.bllx = this.radio;
        }
      }).finally(() => {
        this.visibleType = false;
        this.handleTypeCertainLoading = false;
      });
    },
    /**
     * 办理类型弹窗 取消按钮事件
     */
    handleTypeCancel() {
      this.visibleType = false;
    },
    /**
     * 处理目标
     * @param val
     */
    selectHandleTarget(val) {
      this.visibleHandleTarget = true;
      const result = [
        {
          // 岗位类型 2/5/8
          clmbmx: '',
          clmbmxList: [],
          // checked: false,
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 1 // 处理目标类型
        },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 2 // 处理目标类型
        },
        // {
        //   checked: false,
        //   clmbid: '3', // 处理目标ID
        //   fwdyid: '', // 服务定义ID
        //   zhid: '', // 转换ID
        //   clmblx: 3, // 处理目标类型
        //   Cjr: '', // 创建人
        //   Cjsj: '', // 创建时间
        //   bgr: '', // 变更人
        //   Bgsj: '' // 变更时间
        // },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 4 // 处理目标类型
        },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 5 // 处理目标类型
        },
        // {
        //   checked: false,
        //   clmbid: '6', // 处理目标ID
        //   fwdyid: '', // 服务定义ID
        //   zhid: '', // 转换ID
        //   clmblx: 6, // 处理目标类型
        //   Cjr: '', // 创建人
        //   Cjsj: '', // 创建时间
        //   bgr: '', // 变更人
        //   Bgsj: '' // 变更时间
        // },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 7 // 处理目标类型
        },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 8 // 处理目标类型
        },
        // {
        //   checked: false,
        //   clmbid: '9', // 处理目标ID
        //   fwdyid: '', // 服务定义ID
        //   zhid: '', // 转换ID
        //   clmblx: 9, // 处理目标类型
        //   Cjr: '', // 创建人
        //   Cjsj: '', // 创建时间
        //   bgr: '', // 变更人
        //   Bgsj: '' // 变更时间
        // },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 10 // 处理目标类型
        },
        // {
        //   checked: false,
        //   clmbid: '11', // 处理目标ID
        //   fwdyid: '', // 服务定义ID
        //   zhid: '', // 转换ID
        //   clmblx: 11, // 处理目标类型
        //   Cjr: '', // 创建人
        //   Cjsj: '', // 创建时间
        //   bgr: '', // 变更人
        //   Bgsj: '' // 变更时间
        // },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 12 // 处理目标类型
        },
        {
          clmbmx: '',
          clmbmxList: [],
          checked: false,
          clmbid: '', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 13 // 处理目标类型
        },
        // {
        //   checked: false,
        //   clmbid: '14', // 处理目标ID
        //   fwdyid: '', // 服务定义ID
        //   zhid: '', // 转换ID
        //   clmblx: 14, // 处理目标类型
        //   Cjr: '', // 创建人
        //   Cjsj: '', // 创建时间
        //   bgr: '', // 变更人
        //   Bgsj: '' // 变更时间
        // },
        {
          clmbmx: '',
          clmbmxList: [],
          clmbid: '15', // 处理目标ID
          fwdyid: '', // 服务定义ID
          zhid: '', // 转换ID
          clmblx: 15 // 处理目标类型
        }
      ];
      let result1 = [];
      const params = val;
      clmbFind(params).then((res) => {
        result1 = res.data.content || [];
        this.handleTargetTableData = result;
        const list = [];
        this.handleTargetTableData.forEach((item) => {
          result1.forEach((i) => {
            if (i.clmblx === item.clmblx) {
              Object.assign(item, i);
              // item.clmbmxList = i.clmbmxList;
              // item.clmbmx = 'i.clmbmx';
              list.push(item);
            }
          });
        });
        // const list = this.handleTargetTableData.filter((x) => result1.some((i) => i.clmblx === x.clmblx));
        this.$nextTick(() => {
          this.$refs.handleTargetTable.toggleSelection(list);
        });
      });
    },
    /**
     * 目标环节table删除按钮 点击事件
     * @param val
     */
    deleteTarget(val) {
      const params = val;
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteTargetCurrent(params, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 基础环节删除接口事件
     * @param id
     */
    deleteTargetCurrent(params, instance, done) {
      lcmblistDelete(params).then((res) => {
        if (res.code === 200) {
          const index = this.targetTable.findIndex((item) => item.zhid === params.zhid);
          if (index > -1) {
            this.targetTable.splice(index, 1);
          }
          this.$message.success('删除成功');
          this.getHjData();
        }
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    /**
     * 获取当前处理目标 内容
     * @param val
     */
    getHandleTargetRow(val) {
      this.handleTargetRow = val;
    },
    /**
     * 处理目标信息
     * @param val
     */
    selectHandleTargetDetail(val) {
      // 2:起草者单位  5:本单位固定岗位 8:任意单位岗位
      const GWGL = [
        2, 5, 8
      ];
      // 15: 任意人员
      const RYRY = [15];
      // 12: 固定单位岗位弹窗 13: 固定单位人员弹窗
      const GW_RY = [12, 13];
      if (GWGL.some((x) => x === val.clmblx)) {
        this.TargetDetailLoading = true;
        this.visiblePost = true;
        // TODO 接口
        // const params = val;
        lcmxPostListFind().then((res) => {
          this.postTableData = res.data.content || [];
          // this.postTableData = [
          //   {
          //     jsid: '1',
          //     jsmc: '正职领导'
          //   },
          //   {
          //     jsid: '2',
          //     jsmc: '学生工作主管'
          //   },
          //   {
          //     jsid: '3',
          //     jsmc: '副职领导'
          //   },
          //   {
          //     jsid: '4',
          //     jsmc: '人事秘书'
          //   }
          // ];
          let clmbmxList = [];
          clmbmxList = this.handleTargetRow.clmbmxList || [];
          const list = [];
          this.postTableData.forEach((item) => {
            clmbmxList.forEach((i) => {
              if (i.jsid === item.jsid) {
                Object.assign(item, i);
                list.push(item);
              }
            });
          });
          this.$nextTick(() => {
            this.$refs.postTable.toggleSelection(list);
          });
        }).finally(() => {
          this.TargetDetailLoading = false;
        });
      }
      if (RYRY.some((x) => val.clmblx === x)) {
        this.visibleDepartment = true;
        this.departmentChecked = [];
        const list = this.handleTargetRow.clmbmxList || [];
        list.forEach((item) => {
          this.departmentChecked.push(item.bmm);
        });
        this.treeLoading = true;
        organizationFind().then((res) => {
          this.departmentTreeData = res.data.content || [];
        }).finally(() => {
          this.TargetDetailLoading = false;
          this.treeLoading = false;
        });
      }
      if (GW_RY.some((x) => x === val.clmblx)) {
        if (val.clmblx === 13) {
          this.chooseTitle = '指定单位固定人员';
          Object.assign(this.defaultProps, {
            label: 'xm',
            id: 'yhid',
            children: 'children'
          });
        } else {
          this.chooseTitle = '指定单位固定岗位';
          Object.assign(this.defaultProps, {
            label: 'jsmc',
            id: 'jsid',
            children: 'children'
          });
        }
        this.$refs.ChooseUserDialog.clearNode();
        this.treeLoading = true;
        organizationFind().then((res) => {
          this.chooseTree = res.data.content || [];
          this.treeDetail = [];
          // this.$refs.ChooseUserDialog.clearNode();
          const checked = cloneDeep(this.handleTargetRow.clmbmxList) || [];
          // this.$refs.ChooseUserDialog.multipleSelection = checked;
          this.$refs.ChooseUserDialog.toggleSelection(checked);
        }).finally(() => {
          // this.TargetDetailLoading = false;
          this.treeLoading = false;
        });
        this.$refs.ChooseUserDialog.showDialog();
        this.page = 1;
        const searchInit = {
          node: {
            bmm: '',
            page: this.page,
            pageSize: this.pageSize,
            info: ''
          }
        };
        this.handleTargetRow = val;
        this.searchInfo(searchInit);
      }
    },
    /**
     * 岗位列表确定 点击事件
     */
    postTableCertain() {
      this.postTableCertainLoading = true;
      const params = this.$refs.postTable.multipleSelection;
      this.handleTargetRow.clmbmxList = params;
      const list = [];
      params.forEach((item) => {
        list.push(item.jsmc);
      });
      const name = list.join(',');
      this.$set(this.handleTargetRow, 'clmbmx', name);
      // console.log(this.handleTargetRow, this.handleTargetTableData);
      // this.updateMxDom();
      this.visiblePost = false;
      this.postTableCertainLoading = false;
    },
    /**
     * 岗位列表取消 点击事件
     */
    postTableCancel() {
      this.visiblePost = false;
    },
    /**
     * 处理目标弹窗 确定按钮
     */
    handleTargetTableCertain() {
      this.handleTargetTableCertainLoading = true;
      const params = {
        clmb: this.$refs.handleTargetTable.multipleSelection,
        fwdyid: this.id,
        zhid: this.targetDataCurrent.zhid
      };
      lcmxSave(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('保存成功');
          this.getTargetTable();
        }
      }).finally(() => {
        this.handleTargetTableCertainLoading = false;
        this.visibleHandleTarget = false;
      });
    },
    /**
     * 处理目标弹窗 取消按钮
     */
    handleTargetTableCancel() {
      this.visibleHandleTarget = false;
    },
    /**
     * 部门列表 弹窗 确定按钮 点击事件
     */
    departmentTableCertain() {
      this.getCheckedNodes();
      this.visibleDepartment = false;
    },
    /**
     * 部门列表 弹窗 取消按钮 点击事件
     */
    departmentTableCancel() {
      this.visibleDepartment = false;
    },
    /**
     * 获取 部门列表选取数据
     */
    getCheckedNodes() {
      const res = this.$refs.deparmentTree.getCheckedNodes(false, true);
      this.handleTargetRow.clmbmxList = res;
      const list = [];
      res.forEach((item) => {
        list.push(item.bmmc);
      });
      const name = list.join(',');
      this.$set(this.handleTargetRow, 'clmbmx', name);
      // console.log(this.handleTargetRow, this.handleTargetTableData);
      // this.updateMxDom();
    },
    /**
     * 获取 组织详情页码change
     * @param node
     */
    pageChange(val) {
      this.page = val.page || 0;
      const params = {
        bmm: val.currentNode.bmm,
        page: this.page,
        pageSize: this.pageSize,
        info: this.$refs.ChooseUserDialog.departmentSearch.name
      };
      this.treeDetailLoading = true;
      const isPerson = this.handleTargetRow.clmblx === 13; // 人员选择弹窗
      if (isPerson) {
        organizationPeopleDetail(params).then((res) => {
          this.treeDetail = res.data.content || [];
          this.total = res.data.pageInfo.total || 0;
        }).finally(() => {
          this.treeDetailLoading = false;
        });
      } else {
        organizationDetail(params).then((res) => {
          this.treeDetail = res.data.content || [];
          this.total = res.data.pageInfo.total || 0;
        }).finally(() => {
          this.treeDetailLoading = false;
        });
      }
    },
    /**
     * 用户/岗位弹窗 search
     * @param val
     */
    searchInfo(val) {
      this.page = 1;
      const params = {
        bmm: val.node.bmm || '',
        page: this.page,
        pageSize: this.pageSize,
        info: val.info
      };
      this.treeDetailLoading = true;
      const isPerson = this.handleTargetRow.clmblx === 13; // 人员选择弹窗
      if (isPerson) {
        organizationPeopleDetail(params).then((res) => {
          this.treeDetail = res.data.content || [];
          this.total = res.data.pageInfo.total || 0;
        }).finally(() => {
          this.treeDetailLoading = false;
        });
      } else {
        organizationDetail(params).then((res) => {
          this.treeDetail = res.data.content || [];
          this.total = res.data.pageInfo.total || 0;
        }).finally(() => {
          this.treeDetailLoading = false;
        });
      }
    },
    /**
     * 获取 部门列表选取数据
     * @param node
     */
    nodeClick(node) {
      this.page = 1;
      const params = {
        bmm: node.bmm,
        page: this.page,
        pageSize: this.pageSize,
        info: this.$refs.ChooseUserDialog.departmentSearch.name
      };
      this.treeDetailLoading = true;
      const isPerson = this.handleTargetRow.clmblx === 13; // 是否人员选择弹窗
      if (isPerson) {
        organizationPeopleDetail(params).then((res) => {
          this.total = res.data.pageInfo.total || 0;
          this.treeDetail = res.data.content || [];
        }).finally(() => {
          this.treeDetailLoading = false;
        });
      } else {
        organizationDetail(params).then((res) => {
          this.treeDetail = res.data.content || [];
          this.total = res.data.pageInfo.total || 0;
        }).finally(() => {
          this.treeDetailLoading = false;
        });
      }
    },
    /**
     * 固定单位岗位 确定按钮 点击事件
     * @param val
     */
    getFixedPost(val) {
      this.handleTargetRow.clmbmxList = val;
      const list = [];
      val.forEach((item) => {
        if (this.handleTargetRow.clmblx === 13) {
          list.push(item.xm);
        } else {
          list.push(item.jsmc);
        }
      });
      const name = list.join(',');
      this.$set(this.handleTargetRow, 'clmbmx', name);
      // console.log(this.handleTargetRow, this.handleTargetTableData);
      // this.updateMxDom();
      this.closeFixedPost();
    },
    /**
     * 固定单位岗位 关闭弹窗 取消事件
     */
    closeFixedPost() {
      this.$refs.ChooseUserDialog.hideDialog();
    }
  }
};

</script>

<style lang="scss" scoped>
.lcgl-content {

  &-main {
    height: 100%;
    background-color: #ffffff;
    padding: $page-content-padding;
    display: flex;
    flex-direction: column;

    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .table {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;

        .title {
          padding-top: $page-content-padding;
        }
      }
    }

    .button-list {
      margin-bottom: $page-content-margin;
    }
  }
}
</style>
<style lang="scss">
.lcgl-content {

}

</style>
