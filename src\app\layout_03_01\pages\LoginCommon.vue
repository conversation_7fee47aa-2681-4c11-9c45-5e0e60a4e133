<template>
  <div class="login-container">
    <el-form
      ref="loginForm"
      class="login-form"
      :model="loginForm"
      :rules="loginRules"
      autocomplete="on"
      label-position="left">

      <div class="title-container">
        <h3 class="title">登 录</h3>
      </div>

      <el-form-item prop="username" class="form-input">
        <div class="form-item">
          <span class="svg-container">
            <i class="el-icon-s-custom"/>
          </span>
          <el-input
            ref="username"
            v-model="loginForm.username"
            placeholder="Username"
            name="username"
            type="text"
            tabindex="1"
            autocomplete="on"
          />
        </div>
      </el-form-item>

      <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
        <el-form-item prop="password" class="form-input">
          <div class="form-item">
            <span class="svg-container">
              <i class="el-icon-lock"/>
            </span>
            <el-input
              ref="password"
              v-model="loginForm.password"
              placeholder="Password"
              name="password"
              tabindex="2"
              autocomplete="on"
              auto-complete="new-password"
              @keyup.native="checkCapslock"
              @blur="capsTooltip = false"
              show-password></el-input>
          </div>
        </el-form-item>
      </el-tooltip>
      <el-form-item prop="verificationCode">
        <div class="form-item">
          <el-input
            maxlength="4"
            class="form-input"
            v-model="loginForm.verificationCode"
            placeholder=""
            name="verificationCode"
            type="text"
            tabindex="3"
            autocomplete="on"
          />
          <div @click="getVerificationCode" class="code" v-loading="codeLoading">
            <img width="120" height="47" :src="verification" alt="验证码">
          </div>
        </div>
      </el-form-item>

      <!--      <el-form-item>
              <div class="form-item">
                <p class="form-checked">保持登录</p>
                <el-checkbox v-model="loginForm.remember"/>
              </div>
            </el-form-item>-->
      <div class="login-btn-list">
        <el-button
          :loading="loadStatus(loading,roleLoading)"
          type="primary"
          style="width:49%;"
          @click.native.prevent="handleLogin">
          账号密码登录
        </el-button>
        <el-button
          :loading="loadStatus(loading,roleLoading)"
          type="primary"
          style="width:49%;margin:0;"
          @click.native.prevent="handleLoginAuth">
          统一身份认证登录
        </el-button>
      </div>

    </el-form>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'; // 相关语法糖
import { localData, sessionData } from '@/utils/local-utils.js';
import { loginApi, verificationGet, authLoginApi } from '@/app/layout_03_01/api/login/login-api.js';
import md5 from 'js-md5';

export default {
  name: 'Login',
  components: {},
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入正确的用户名'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      const pPattern = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*? ]).*$/;
      console.log(pPattern.test(value));
      if (!pPattern.test(value)) {
        callback(new Error('最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符'));
      } else {
        callback();
      }
    };
    return {
      // 验证码loading
      codeLoading: false,
      // 验证码信息
      verification: '',
      // 登录用户信息
      loginForm: {
        username: 'hsoftzhxy',
        password: 'banner',
        verificationCode: '',
        remember: false
      },
      // 登录校验
      loginRules: {
        username: [{
          required: true,
          trigger: 'blur',
          validator: validateUsername
        }],
        password: [{
          required: true,
          trigger: 'blur'
        }],
        verificationCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur'
          }]
      },
      // 大写提示
      capsTooltip: false,
      // 登录loading
      loading: false,
      // 重定向信息
      redirect: undefined,
      // url query
      otherQuery: {}
    };
  },
  computed: {
    ...mapState('layout_03_01/user', ['roleLoading']),
    loadStatus() {
      return (load, role) => {
        if (load) {
          return true;
        }
        if (!load && !role) {
          return false;
        }
        return false;
      };
    }
  },
  watch: {
    // 监听路由信息 过去重定向信息
    $route: {
      handler(route) {
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query);
          if (query.code) {
            this.loading = true;
            authLoginApi({ code: query.code }).then((res) => {
              const result = res.data.content;
              // 存储信息到cookie
              this.saveUserInfo(result);
              // 进入app跳转首页
              this.$router.push({
                path: this.redirect || '/',
                query: this.otherQuery
              });
            }).catch(() => {
              this.loading = false;
            });
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.setRoleLoading(false);
    // 获取验证码
    this.getVerificationCode();
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus();
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus();
    }
  },
  methods: {
    ...mapMutations('layout_03_01/user', ['setLogin', 'setLoginRole', 'setHasLogin', 'setRoleLoading']), // vuex setLogin
    /**
     * 获取登陆验证码
     */
    getVerificationCode() {
      this.codeLoading = true;
      verificationGet()
        .then((res) => {
          if (res.code === 200) {
            this.verification = res.data.content;
          } else {
            this.verification = '';
          }
        })
        .catch(() => {
          this.loginForm.verificationCode = '';
        })
        .finally(() => {
          this.codeLoading = false;
        });
    },
    /**
     * 大写提示
     */
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z');
    },
    /**
     * 保存/重置用户信息
     * @param response
     */
    saveUserInfo(response) {
      this.setHasLogin(true);
      // // 判断复选框是否被勾选 勾选则调用配置cookie方法
      // if (this.loginForm.remember) {
      //   // 登陆成功设置 token
      //   const token = response.token || '';
      //   setToken(token, 7);
      // } else {
      //   // 登陆成功设置 token
      //   const token = response.token || '';
      //   setToken(token, 1);
      // }
      // 存储用户基础信息
      let jsid = '';
      if (response.roleList) {
        jsid = response.roleList[0] ? response.roleList[0].jsid : '';
      }
      const userInfo = {
        info: response.data,
        list: response.roleList
      };
      this.setLogin(userInfo || {});
      this.setLoginRole(jsid);
      // 清楚 权限列表
      sessionData('clear', 'roleList');
    },
    /**
     * 登录接口调用
     * @param response
     */
    loginApp() {
      // button loading...
      this.loading = true;
      const params = {
        userName: this.loginForm.username,
        passWord: md5(this.loginForm.password),
        code: this.loginForm.verificationCode
      };
      // 登录接口
      loginApi(params)
        .then((res) => {
          if (res.code === 200) {
            const result = res.data.content;
            // 存储信息到cookie
            this.saveUserInfo(result);
            // 进入app跳转首页
            this.$router.push({
              path: this.redirect || '/',
              query: this.otherQuery
            });
            // this.$router.push({ path: '/' });
          } else {
            this.getVerificationCode();
          }
        })
        .catch(() => {
          this.loading = false;
          this.getVerificationCode();
        });
    },
    /**
     * 登录按钮
     */
    handleLogin() {
      // 登录校验
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          // 登录操作
          this.loginApp();
          return;
        }
        console.log('error submit!!');
      });
    },
    /**
     * router解析
     * @param query
     * @returns {{}}
     */
    getOtherQuery(query) {
      return Object.keys(query)
        .reduce((acc, cur) => {
          if (cur !== 'redirect') {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
    },
    /**
     * 统一身份认证登录
     */
    handleLoginAuth() {
      console.log('统一身份认证登录');
      const urlAuth = 'http://*************:8080/spring_oauth_server_war_exploded/oauth/authorize';
      const urlPro = 'http://localhost:8080/#/login';
      const clientId = 'hit_wsfw';
      const time = new Date().getTime();
      window.location.href = `${urlAuth}?response_type=code&scope=read&client_id=${clientId}&redirect_uri=${urlPro}&state=${time}`;
    }
  }
};
</script>

<style lang="scss">
$bg: #283443;
$light_gray: #fff;
$cursor: #fff;
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 100%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .form-item {
    width: 100%;
    display: flex;
    align-items: center;
  }

  .form-input {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  .login-btn-list{
    display: flex;
    justify-content: space-between;
  }

  .code {
    background: #FFFFFF;
    width: 120px;
    height: 47px;
    margin-left: 20px
  }

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .form-checked {
    font-size: 14px;
    font-weight: 400;
    color: $light_gray;
    margin-right: 10px;
  }
}
</style>
