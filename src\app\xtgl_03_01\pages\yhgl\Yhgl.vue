<template>
  <div class="yhgl-content">
    <div class="yhgl-content-main">
      <!--      标题-->
      <v-title name="用户管理"></v-title>
      <yhgl-search ref="searchElement" @search="search"></yhgl-search>
      <!--      table 表单部分 -->
      <!--      按钮-->
      <div class="button-list">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="addYh()">新增</el-button>
        <el-button type="danger" icon="el-icon-delete-solid" size="small" @click="plscYh()">批量删除
        </el-button>
      </div>
      <!--      table-->
      <div class="table">
        <div class="table-content">
          <yhgl-table :scroller-height="scrollerHeight" :table-data="tableData" @handleSelectionChange="handleSelectionChange" @delYh="delYh" @modifyYh="modifyYh" @addYhbm="addYhbm" @editYhbm="editYhbm"></yhgl-table>
        </div>
        <!--        table pageInation-->
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>

    <!--新增修改弹窗-->
    <el-dialog
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="addDialogVisible"
      :title="dialogType === 'edit'?'修改用户':'新增用户'"
      :close-on-click-modal="false" @close="closeYh('yhForm')">
      <yhgl-edit ref="dialogEditContent" :yhid-visible="yhidVisible" :FormData="yh"></yhgl-edit>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="confirm('yhForm')">确定</el-button>
        <el-button type="" size="small" @click="closeYh('yhForm')">取消</el-button>
      </div>

    </el-dialog>

    <!--新增用户部门-->
    <el-dialog :visible.sync="addYhbmDialogVisible" customClass="zhxy-dialog-view" :title="yhbmdialogType === 'edit'?'修改部门':'添加部门'"
               :close-on-click-modal="false" @close="closeYhbm('addYhbmForm')">
      <yhgl-bm-edit ref="dialogBmContent" :form-data="yhbm">
      </yhgl-bm-edit>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="confirmYhbm('addYhbmForm')">确定</el-button>
        <el-button type="" size="small" @click="closeYhbm('addYhbmForm')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import { cloneDeep } from 'lodash';
import md5 from 'js-md5';
import YhglSearch from './yhglComponents/InfoSearch';
import YhglTable from './yhglComponents/InfoTable';
import YhglEdit from './yhglComponents/InfoEdit';
import YhglBmEdit from './yhglComponents/BmEdit';
import {
  findListByPage, findOneByID, findOne, del, add, update, deleteBatchYh, findBmList, addYhbm, updateYhbm
} from '../../api/yhgl/yhgl';

const defaultYhBm = {
  yhid: '',
  bmm: '',
  id: ''
};

const defaultYh = {
  yhid: '',
  xm: '',
  mm: '',
  sjh: '',
  dzyx: '',
  sfms: '',
  sfzj: '1',
  kdlfs: '1100000000',
  yhzt: '1',
  sjlybz: '',
  bz: '',
  bmmc: '',
  jsmc: ''
};

export default {
  name: 'yhgl',
  components: {
    YhglSearch,
    YhglTable,
    VTitle,
    YhglEdit,
    YhglBmEdit
  },
  data() {
    return {
      yh: { ...defaultYh },
      scrollerHeight: 0,
      currentPage: 1, // 初始页
      tableData: [], // 列表数据集合
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      yhSelection: [], // table多选框数据
      yhidList: [],
      addDialogVisible: false,
      addYhbmDialogVisible: false,
      yhidVisible: true,
      dialogType: 'new',
      yhbmdialogType: 'new',
      yhbm: {
        yhid: '',
        bmm: '',
        id: ''
      },
      options: {
        type: Array,
        default: () => []
      }
    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /**
     * 监听页面尺寸改变table size
     */
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight*0.8 - height - 265;
    },
    /**
     * search 搜索事件
     * @param params
     * @returns {Promise<void>}
     */
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        idxm: null,
        bmmc: null,
        jsmc: null,
        sjh: null,
        dzyx: null,
        sfms: null,
        kdlfs: null,
        bz: null,
        sfzj: null,
        yhzt: null,
        cjsjkssj: null,
        cjsjjssj: null,
        bgsjkssj: null,
        bgsjjssj: null
      };
      if (params) {
        Object.assign(param, params);
      }
      findListByPage(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    /**
     * 查询条件重置
     */
    reset() {
      this.listQuery.idxm = null;
      this.listQuery.sfzj = null;
      this.listQuery.yhzt = null;
      this.search();
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        idxm: this.$refs.searchElement.listQuery.idxm,
        bmmc: this.$refs.searchElement.listQuery.bmmc,
        jsmc: this.$refs.searchElement.listQuery.jsmc,
        sjh: this.$refs.searchElement.listQuery.sjh,
        dzyx: this.$refs.searchElement.listQuery.dzyx,
        sfms: this.$refs.searchElement.listQuery.sfms,
        kdlfs: this.$refs.searchElement.listQuery.kdlfs,
        bz: this.$refs.searchElement.listQuery.bz,
        sfzj: this.$refs.searchElement.listQuery.sfzj,
        yhzt: this.$refs.searchElement.listQuery.yhzt,
        cjsjkssj: this.$refs.searchElement.listQuery.cjsjkssj,
        cjsjjssj: this.$refs.searchElement.listQuery.cjsjjssj,
        bgsjkssj: this.$refs.searchElement.listQuery.bgsjkssj,
        bgsjjssj: this.$refs.searchElement.listQuery.bgsjjssj
      };
      this.search(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      console.log(val);
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage, // 页码
        idxm: this.$refs.searchElement.listQuery.idxm,
        bmmc: this.$refs.searchElement.listQuery.bmmc,
        jsmc: this.$refs.searchElement.listQuery.jsmc,
        sjh: this.$refs.searchElement.listQuery.sjh,
        dzyx: this.$refs.searchElement.listQuery.dzyx,
        sfms: this.$refs.searchElement.listQuery.sfms,
        kdlfs: this.$refs.searchElement.listQuery.kdlfs,
        bz: this.$refs.searchElement.listQuery.bz,
        sfzj: this.$refs.searchElement.listQuery.sfzj,
        yhzt: this.$refs.searchElement.listQuery.yhzt,
        cjsjkssj: this.$refs.searchElement.listQuery.cjsjkssj,
        cjsjjssj: this.$refs.searchElement.listQuery.cjsjjssj,
        bgsjkssj: this.$refs.searchElement.listQuery.bgsjkssj,
        bgsjjssj: this.$refs.searchElement.listQuery.bgsjjssj
      };
      this.search(param);
    },
    /**
     * 多选框选中赋值
     * @param val
     */
    handleSelectionChange(val) {
      this.yhSelection = val;
    },
    /**
     * 新增用户弹窗事件
     */
    addYh() {
      this.yh = cloneDeep({ ...defaultYh });
      this.dialogType = 'new';
      this.yhidVisible = true;
      this.addDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.dialogEditContent.rules.mm[0].required = true;
      });
    },
    /**
     * 关闭用户窗口时处理
     * @param formName
     */
    closeYh(formName) {
      this.$refs.dialogEditContent.$refs[formName].clearValidate();
      this.addDialogVisible = false;
    },
    /**
     * 删除用户
     * @param row
     */
    delYh(row) {
      const id = row.yhid || ''; // yhid
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            del({ yhid: id }).then((res) => {
              if (res.code === 200) {
                const index = this.tableData.findIndex((item) => item.yhid === id);
                if (index > -1) {
                  this.tableData.splice(index, 1);
                }
                this.$message.success('删除成功');
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 修改用户信息
     * @param row
     */
    modifyYh(row) {
      this.addDialogVisible = true;
      this.dialogType = 'edit';
      this.yhidVisible = false;
      this.yh = cloneDeep({ ...defaultYh });
      Object.assign(this.yh, row);
      this.$nextTick(() => {
        this.$refs.dialogEditContent.rules.mm[0].required = false;
      });
    },
    /**
     * 新增/修改
     * @param formName
     */
    confirm(formName) {
      if (this.dialogType !== 'edit') {
        const param = {
          yhid: this.yh.yhid
        };
        findOneByID(param).then((res) => {
          if (Object.keys(res.data).length === 0) {
            const params = cloneDeep(this.yh);
            if (params.mm) {
              params.mm = md5(params.mm);
            }
            add(params).then((result) => {
              this.search();
            }).finally(() => {
              this.closeYh(formName);
            });
          } else {
            this.$message.error('该用户id已存在！');
          }
        });
      } else {
        const params = cloneDeep(this.yh);
        if (params.mm) {
          params.mm = md5(params.mm);
        }
        update(params).then((result) => {
          this.search();
        }).finally(() => {
          this.closeYh(formName);
        });
      }
    },
    /**
     * 批量删除
     */
    plscYh() {
      console.log(this.yhSelection);
      if (this.yhSelection.length === 0) {
        this.$message.success('请勾选用户');
      } else {
        const arr = [];
        this.yhSelection.forEach((item) => {
          const itemparam = {
            yhid: item.yhid
          };
          arr.push(itemparam);
        });
        /**
         * 批量删除 接口
         */
        const param = {
          ids: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },
    /**
     * 批量删除 用户 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      deleteBatchYh(param).then((res) => {
        this.$message.success('删除成功');
        this.search();
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    /**
     * 添加用户所在部门
     * @param yhid
     * @returns {Promise<void>}
     */
    async addYhbm(yhid) {
      this.yhbmdialogType = 'new';
      this.yhbm.yhid = yhid;
      try {
        findBmList(yhid).then((res) => {
          if (res.code === 200) {
            this.$refs.dialogBmContent.yhbmOptions = res.data.content;
          }
        });
        this.addYhbmDialogVisible = true;
      } catch {
        this.loading = false;
      }
    },
    /**
     * 编辑用户所在部门
     * @param yhid
     * @returns {Promise<void>}
     */
    async editYhbm(row) {
      this.yhbmdialogType = 'edit';
      this.yhbm.yhid = row.yhid;
      this.yhbm.id = row.id;
      try {
        findBmList(row.yhid).then((res) => {
          if (res.code === 200) {
            this.$refs.dialogBmContent.yhbmOptions = res.data.content;
          }
        });
        this.$refs.dialogBmContent.FormData.bmm = row.bmm;
        this.addYhbmDialogVisible = true;
      } catch {
        this.loading = false;
      }

      this.addYhbmDialogVisible = true;
    },
    /**
     * 关闭用户部门弹窗
     * @param formName
     */
    closeYhbm(formName) {
      this.$refs.dialogBmContent.$refs[formName].clearValidate();
      this.addYhbmDialogVisible = false;
    },
    /**
     * 提交用户部门
     * @param formName
     */
    confirmYhbm(formName) {
      if (this.yhbmdialogType !== 'edit') {
        const param = {
          yhid: this.yhbm.yhid,
          bmm: this.$refs.dialogBmContent.FormData.bmm
        };
        findOne(param).then((res) => {
          if (Object.keys(res.data).length === 0) {
            addYhbm(param).then((result) => {
              this.search();
              this.$message.success('添加成功！');
            }).finally(() => {
              this.closeYhbm(formName);
            });
          } else {
            this.$message.error('该用户部门已存在！');
          }
        });
      } else {
        console.log(this.yhbm.id);
        const param = {
          yhid: this.yhbm.yhid,
          bmm: this.$refs.dialogBmContent.FormData.bmm,
          id: this.yhbm.id
        };
        findOne(param).then((res) => {
          if (Object.keys(res.data).length === 0) {
            updateYhbm(param).then((result) => {
              this.search();
              this.$message.success('编辑成功！');
            }).finally(() => {
              this.closeYhbm(formName);
            });
          } else {
            this.$message.error('该用户部门已存在！');
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
  .yhgl-content {
    &-main{
      background-color: #ffffff;
      padding: $page-content-padding;
    }
    .table {
      &-content {
        margin-top: $page-content-margin;
      }

    }
    .dialog-footer{
      text-align: center;
    }
  }
</style>
