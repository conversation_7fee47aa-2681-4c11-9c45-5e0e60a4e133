<template>
  <div class="ysjbb-content">
    <div class="ysjbb-content-main">
      <!-- 标题-->
      <v-title name="数据库管理"></v-title>
      <sjkgl-search ref="searchElement" @search="search"></sjkgl-search>
      <div class="button">
        <el-button type="primary" size="small" @click="add()">新增</el-button>
      </div>
      <!-- table-->
      <div class="table">
        <div class="table-content">
          <sjkgl-table :scroller-height="scrollerHeight" :table-data="tableData"  @updateCjrwgz="updateCjrwgz"  @delCjrwgz="delCjrwgz" @Tbjl="Tbjl"></sjkgl-table>
        </div>
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>

      <!--新增修改弹窗-->
      <el-dialog
        ref="dialogEdit"
        customClass="zhxy-dialog-view"
        width="850px"
        :visible.sync="addDialogVisible"
        :title="dialogType === 'edit'?'修改数据库信息':'新增数据库任务信息'"
        :close-on-click-modal="false" @close="closeCjrwgz('rwxxForm')">
        <sjkgl-edit ref="dialogEditContentByCjrwgz" :sjyid-visible="sjyidVisible" :FormData="fjghsjygl"></sjkgl-edit>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" size="small" @click="confirm('rwxxForm')">确定</el-button>
          <el-button type="" size="small" @click="closeCjrwgz('rwxxForm')">取消</el-button>
        </div>
      </el-dialog>

      <!--同步记录弹窗-->
      <el-dialog
        ref="dialogEdit"
        customClass="zhxy-dialog-view"
        width="850px"
        :visible.sync="tbjlDialogVisible"
        :title="dialogType === 'edit'?'状态明细':'状态明细'"
        :close-on-click-modal="false" @close="closeTbjg()">
        <div class="table-content">
          <sjkmx-table :scroller-height="scrollerHeight" :table-data="tbjg_tableData" @search_tbjl="search_tbjl"></sjkmx-table>
        </div>
        <div>
          <el-pagination
            @size-change="handleSizeChange_tbjl"
            @current-change="handleCurrentChange_tbjl"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="tbjg_pageTotal">
          </el-pagination>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button type="" size="small" @click="closeTbjg()">关闭</el-button>
        </div>
      </el-dialog>

    </div>

  </div>
</template>

<script>
import {
  findPageList,
  add,
  update,
  del,
  findTbjlPageList
} from '@/app/xtjkyyj_01_01/api/sjkgl/sjkgl.js';

import VTitle from '@/components/title/VTitle';
import SjkglSearch from './sjkglComponents/sjkglSearch';
import SjkglTable from './sjkglComponents/sjkglTable';
import SjkmxTable from './sjkglComponents/sjkmxTable';
import SjkglEdit from './sjkglComponents/sjkglEdit';

const defaultFjghsjygl = {
  sjyip: '',
  sjymc: '',
  sjyslmc: '',
  sjyms: '',
  sjyzt: '',
  sjylx: '',
  dk: '',
  yhm: '',
  mm: '',
  zxztjcid: '',
  yjjbm: ''
};

export default {
  name: 'ysjbbgl',
  components: {
    SjkglSearch,
    SjkglTable,
    SjkglEdit,
    SjkmxTable,
    VTitle
  },
  data() {
    return {
      fjghsjygl: { ...defaultFjghsjygl },
      page: 1,
      pageSize: 30,
      scrollerHeight: 0, // 高度
      currentPage: 1, // 初始页
      tableData: [], // 列表数据集合
      pageTotal: 0,
      dialogType: 'new',
      sjyidVisible: true,
      rwbhVisible: true,
      addDialogVisible: false,
      tbjlDialogVisible: false,
      tbjg_tableData: [],
      tbjg_pageTotal: 0,
      sjyid: null
    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
    // search 调用
    this.search();
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /**
     * 监听页面尺寸改变table size
     */
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight - height - 220;
    },
    /**
     * search 搜索事件
     * @param params
     * @returns {Promise<void>}
     */
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        sjymc: null,
        sjylx: null,
        sjyzt: null
      };

      if (params) {
        Object.assign(param, params);
        if (param.sjymc === '') {
          param.sjymc = null;
        }
      }
      findPageList(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        sjymc: this.$refs.searchElement.listQuery.sjymc,
        sjylx: this.$refs.searchElement.listQuery.sjylx,
        sjyzt: this.$refs.searchElement.listQuery.sjyzt
      };
      this.search(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage, // 页码
        sjymc: this.$refs.searchElement.listQuery.sjymc,
        sjylx: this.$refs.searchElement.listQuery.sjylx,
        sjyzt: this.$refs.searchElement.listQuery.sjyzt
      };
      this.search(param);
    },

    /**
     * 新增系统参数弹窗事件
     */
    add() {
      this.fjghsjygl = { ...defaultFjghsjygl };
      this.dialogType = 'new';
      this.sjyidVisible = true;
      this.addDialogVisible = true;
    },
    /**
     * 关闭用户窗口时处理
     * @param formName
     */
    closeCjrwgz(formName) {
      this.$refs.dialogEditContentByCjrwgz.$refs.rwxxForm.clearValidate();
      this.addDialogVisible = false;
    },

    /**
     * 修改用户信息
     * @param row
     */
    updateCjrwgz(row) {
      this.addDialogVisible = true;
      this.dialogType = 'edit';
      this.sjyidVisible = false;
      if (!row.dk) {
        row.dk = '';
      }
      if (!row.yhm) {
        row.yhm = '';
      }
      if (!row.mm) {
        row.mm = '';
      }
      if (!row.sjyms) {
        row.sjyms = '';
      }
      Object.assign(this.fjghsjygl, row);
    },

    /**
     * 新增/修改
     * @param formName
     */
    confirm(formName) {
      this.$refs.dialogEditContentByCjrwgz.$refs.rwxxForm.validate((valid) => {
        if (valid) {
          if (this.dialogType !== 'edit') {
            add(this.fjghsjygl).then((result) => {
              this.closeCjrwgz(formName);
              this.search();
              this.$message.success('新增成功');
            });
          } else {
            console.log(this.fjghsjygl);
            update(this.fjghsjygl).then((result) => {
              this.closeCjrwgz(formName);
              this.search();
              this.$message.success('修改成功');
            });
          }
        }
      });
    },

    /**
       * 删除
       * @param row
       */
    delCjrwgz(row) {
      const id = row.sjyid || ''; // 参数标识
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            del({ sjyid: id }).then((res) => {
              if (res.code === 200) {
                const index = this.tableData.findIndex((item) => item.sjyid === id);
                if (index > -1) {
                  this.tableData.splice(index, 1);
                }
                this.$message.success('删除成功');
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },

    /**
     * 修改用户信息
     * @param row
     */
    Tbjl(row) {
      this.tbjlDialogVisible = true;
      this.dialogType = 'edit';
      this.rwbhVisible = false;
      this.search_tbjl(row);
      this.sjyid = row.sjyid;
    },
    /**
     * 修改用户信息
     * @param row
     */
    closeTbjg() {
      this.tbjlDialogVisible = false;
    },

    /**
     * search 搜索事件
     * @param params
     * @returns {Promise<void>}
     */
    search_tbjl(row) {
      const param = {
        pageSize: 30,
        page: 1,
        sjyid: row.sjyid
      };
      Object.assign(param, row);
      findTbjlPageList(param).then((res) => {
        if (res.code === 200) {
          this.tbjg_tableData = res.data.content;
          this.tbjg_pageTotal = res.data.pageInfo.total;
        }
      });
    },

    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange_tbjl(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        sjyid: this.sjyid
      };
      this.search_tbjl(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange_tbjl(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.currentPage, // 页码
        sjyid: this.sjyid
      };
      this.search_tbjl(param);
    }

  }
};
</script>

<style lang="scss" scoped>
.button{
  margin-top: -3.1rem;
  margin-right: 1rem;
  float: right;
}

</style>
