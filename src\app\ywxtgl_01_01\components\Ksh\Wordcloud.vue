<template>
  <div ref="container" :style="{width: '100%', height: '100%'}"></div>
</template>

<script>

const Js2WordCloud = require('js2wordcloud');

export default {
  components: {},
  props: {
    wordData: {
      type: Array,
      default() {
        return [{ name: '暂无数据', value: 0 }];
      }
    }
  },
  data() {
    return {};
  },
  mounted() {
    this.drawWordCloud();
  },
  methods: {
    drawWordCloud() {
      const vm = this;

      const wordFreqs = this.wordData;

      const wordFreqData = new Array();
      for (let i = 0; i < wordFreqs.length; i++) {
        const option = [wordFreqs[i].name, wordFreqs[i].value];
        wordFreqData.push(option);
      }

      const wc = new Js2WordCloud(this.$refs.container);
      wc.setOption({
        backgroundColor: 'transparent',
        color: '#15a4fa',
        color: 'random-light', // 字体颜色 'random-dark' 或者 'random-light'
        gridSize: 15, // 密集程度 数字越小越密集
        weightFactor: 1, // 字体大小=原始大小*weightFactor
        maxFontSize: 20, // 最大字号
        minFontSize: 6, // 最小字号
        fontWeight: 'normal', // 字体粗细
        fontFamily: 'Times, serif', // 字体
        rotateRatio: 0, // 字体倾斜(旋转)概率，1代表总是倾斜(旋转)
        tooltip: {
          show: true
        },
        list: wordFreqData,
        click(item, dimension, event) { // 支持点击事件
          vm.$emit('showWord', item);
        }
      });
    }
  }
};
</script>

<style>

</style>
