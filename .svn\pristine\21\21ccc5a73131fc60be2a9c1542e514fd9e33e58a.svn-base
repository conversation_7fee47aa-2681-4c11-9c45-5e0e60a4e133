<template>
  <div class="cdgl-content">
    <div class="left">
      <div class="main">
        <v-title name="根角色选择"></v-title>
        <div class="main-content" v-loading="roleListLoading">
          <el-radio-group
            v-model="roleRadio"
            size="small"
            @change="roleChange">
            <div class="role-radio-list" v-for="item in roleRadioList" :key="item.jsid">
              <el-radio style="width: 120px;overflow: hidden;text-overflow: ellipsis" :title="item.jsmc"
                        :label="item.jsid" border>{{ item.jsmc }}
              </el-radio>
            </div>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="middle">
      <div class="main">
        <v-title name="菜单(右键执行操作)"></v-title>
        <div class="main-content" style="position: relative;padding-top: 72px">
          <div style="position: absolute;top: 0">
            <div class="cdgl-select-xtbs" style="display: flex">
              <p class="cdgl-xtbs-label" style="color: #515a6e;
    font-weight: bold;">
                系统标识:
              </p>
              <el-select
                class="cdgl-xtbs-value"
                size="small"
                v-model="xtbs"
                @change="changeXtbs"
                placeholder="请选择系统">
                <el-option
                  v-for="item in xtbsOptions"
                  :key="item.dmz"
                  :label="item.dmmc"
                  :value="item.dmz">
                </el-option>
              </el-select>
            </div>

            <el-button
              style="font-weight: bold"
              size="default"
              icon="el-icon-plus" type="text"
              @click="addRootFile">
              新建根文件夹
            </el-button>
          </div>
          <div style="height: 100%;overflow: auto">
            <el-tree
              v-loading="menuListLoading"
              ref="menuTree"
              :expand-on-click-node="false"
              :props="menuProps"
              highlight-current
              :data="menuTree"
              :node-key="menuProps.id"
              default-expand-all
              :filter-node-method="filterNode"
              @node-contextmenu="openMenu"
              @node-click="handleNodeClick">
              <span slot-scope="{ node, data }">
                <i v-if="data.cdlx === 1"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{ data.cdmc }}
              </span>
            </el-tree>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <el-tabs type="card" v-model="activeName" style="height: 100%">
        <el-tab-pane label="菜单详情" name="menu" class="content-tab-pane">
          <department-detail
            :form-data="detailData"
            v-loading="menuDetailLoading"
            v-if="showDetail"
            :isEdit.sync="isEdit"
            @menuUpdate="menuUpdate"
            @openIconDrawers="openIconDrawer"
            @openSourceDrawer="openSourceDrawer">
          </department-detail>
          <p
            v-else
            style="text-align: center;color: #909399;font-size: 14px;">
            暂无选择数据
          </p>
        </el-tab-pane>
      </el-tabs>

    </div>

    <!--    右击弹窗-->
    <right-menu ref="rightMenuPart" :options="rightMenuOptions"></right-menu>
    <!--    新增资源弹窗-->
    <el-dialog
      :title="title"
      :visible.sync="showNewDialog"
      width="500px"
      lock-scroll
      close-on-press-escape>
      <div class="">
        <department-detail
          :show-view="showNewDialog"
          ref="editDialog"
          :form-data="detailInitData"
          :isShowButton="isShowButton"
          :isEdit.sync="isShowButton"
          @openIconDrawers="openIconDrawer"
          @openSourceDrawer="openSourceDrawer">
        </department-detail>
      </div>
      <div slot="footer">
        <el-button :loading="fileAddLoading" @click="fileAdd" type="primary" size="small">确定</el-button>
        <el-button @click="closeAdd" type="" size="small">
          取消
        </el-button>
      </div>
    </el-dialog>

    <role-tree-dialog
      :default-props="{
        label: 'gnzymc',
        id: 'gnzyid',
        children: 'children'
      }"
      v-loading="sourceLoading"
      @drawerCertain="drawerCertain"
      :title="'资源管理'"
      :tree-data="resourceTree"
      :is-radio="isRadio"
      :check-list="checkList"
      ref="roleTree">
    </role-tree-dialog>
    <icon-tree-dialog @setIcon="setIcon" :checked-name="checkName" ref="iconTree"/>
  </div>
</template>

<script>
import {
  findRoleList, findMenuList, findMenuDetail, menuDetailSave, menuDelete, findResourcesList, resourceAdd, resourceAddMore, getXtbsApi
} from '@/app/xtgl_03_01/api/cdgl/cdgl.js';

import { cloneDeep } from 'lodash';
import VTitle from '../../../../components/title/VTitle';
import DepartmentDetail from './cdglComponents/DepartmentDetail';
import RoleTreeDialog from '../components/RoleTreeDialog';
import IconTreeDialog from '../components/IconTreeDialog';
import RightMenu from '../../components/RightMenu/index';

export default {
  components: { // 注册组件
    VTitle,
    DepartmentDetail,
    RightMenu,
    RoleTreeDialog,
    IconTreeDialog
  },
  data() {
    return {
      // 系统标识 select
      xtbs: '',
      // 系统表示 selectOptions
      xtbsOptions: [
        {
          value: '1',
          label: 'bzw'
        },
        {
          value: '2',
          label: 'test'
        }
      ],
      // 是否单选
      isRadio: false,
      // 内置资源选中
      checkList: [],
      // 新建菜单title
      title: '新建根文件',
      // 资源抽屉loading
      sourceLoading: false,
      // 资源抽屉数据
      resourceTree: [],

      // 左侧角色列表 loading
      roleListLoading: false,
      // 根角色radio
      roleRadio: '',
      // 角色radio list
      roleRadioList: [],
      // 菜单列表tree prop格式
      menuProps: {
        label: 'cdmc',
        id: 'cdid',
        children: 'children'
      },
      // 当前点击tree的信息
      currentData: {},
      // 菜单列表数据
      menuTree: [],
      // 菜单列表loading
      menuListLoading: false,
      // 菜单详情展示
      showDetail: false,
      // 菜单详情loading
      menuDetailLoading: false,
      // 文件夹新建loading
      fileAddLoading: false,
      // 菜单详情数据
      detailData: {
        cdid: '', // 菜单id
        jsid: '',
        fcdid: null, // 父级id
        fcdmc: '', // 父级名称
        tb: '', // 图标
        pxh: '', // 排序号
        nzgnzyid: '', // 内置功能资源id
        nzgnzydkfs: '', // 内置功能资源打开方式
        wlqqdz: '', // 外部连接请求地址
        wlgnmc: '', // 外部连接请求名称
        cdlx: '', // 菜单类型
        cdmc: '' // 带单名称
      },
      // 选中 iconname
      checkName: '',
      // 新增资源 visible
      showNewDialog: false,
      // 是否展示 编辑按钮
      isShowButton: false,
      // 编辑状态
      isEdit: true,
      // 右键菜单menu list
      rightMenuOptions: {
        list: [],
        top: 0,
        left: 0,
        event: {},
        ref: {}
      },
      // tab标签
      activeName: 'menu',

      // 资源管理树结构数据格式
      defaultProps: {
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      },
      // 默认新建数据
      detailInitData: {
        cdid: '', // 菜单id
        jsid: '',
        fcdid: null, // 父级id
        fcdmc: '', // 父级名称
        tb: '', // 图标
        pxh: '', // 排序号
        nzgnzyid: '', // 内置功能资源id
        nzgnzydkfs: '', // 内置功能资源打开方式
        wlqqdz: '', // 外部连接请求地址
        wlgnmc: '', // 外部连接请求名称
        cdlx: '', // 菜单类型
        cdmc: '' // 带单名称
      }
    };
  },
  mounted() {
    // 默认展开几级
    // setTimeout(() => {
    //   // this.expendDefaultLevel(this.bmtree, 1, 2);
    // }, 2000);
  },
  created() {
    // 获取角色列表/ 菜单列表
    this.pageInit();
  },
  methods: {
    closeAdd() {
      this.$nextTick(() => {
        // this.$refs.editDialog.$refs.form.resetFields();
        this.showNewDialog = false;
      });
    },
    // 系统表示 select change
    changeXtbs() {
      // 获取对应角色 菜单列表
      this.getMenuList();
    },
    /**
     * tree 默认代开层级
     * @param data
     * @param startLevel
     * @param stopLevel
     */
    expendDefaultLevel(data, startLevel, stopLevel) {
      this.defaultTree = [];
      const handleTree = (dataTree, level, needLevel) => {
        dataTree.forEach((item) => {
          // this.$set(item, 'privateLevel', level);
          item.privateLevel = level;
          if (item.privateLevel <= needLevel) {
            this.defaultTree.push(item.bmm);
          }
          if (item.privateLevel <= needLevel && item.children && item.children.length > 0) {
            const index = item.privateLevel + 1;
            handleTree(item.children, index, needLevel);
          }
        });
      };
      handleTree(data, startLevel, stopLevel);
    },
    /**
     * tree node 过滤
     * @param value
     * @param data
     * @returns {boolean}
     */
    filterNode(value, data) {
      if (!value) return true;
      return data.bmmc.indexOf(value) !== -1;
    },
    /**
     * 页面初始化
     */
    pageInit() {
      const next = async () => {
        if (this.roleRadioList.length > 0) {
          this.roleRadio = this.roleRadioList[0].jsid;
          // 获取系统标识 options 数据
          getXtbsApi().then((res) => {
            this.xtbsOptions = res.data.content || [];
            if (this.xtbsOptions && this.xtbsOptions.length > 0) {
              this.xtbs = this.xtbsOptions[0].dmz;
            }
            // 获取菜单列表
            this.getMenuList();
          }).catch(() => {
          });
        }
      };
      // 获取角色列表
      this.getRoleList(next);
      // 默认选择 角色第一个
    },
    /**
     * 角色列表查询
     */
    getRoleList(next) {
      this.roleListLoading = true;
      // 查询角色接口
      findRoleList()
        .then((res) => {
          this.roleRadioList = res.data.content || [];
          if (next) {
            next();
          }
        })
        .finally(() => {
          this.roleListLoading = false;
        });
    },
    /**
     * 获取对应角色的菜单列表
     */
    getMenuList(next) {
      this.menuListLoading = true;
      // 获取菜单列表接口
      const params = {
        jsid: this.roleRadio,
        xtbs: this.xtbs
      };
      findMenuList(params)
        .then((res) => {
          this.menuTree = res.data.content || [];
          if (next) {
            next();
          }
        })
        .finally(() => {
          this.menuListLoading = false;
        });
    },
    /**
     * 角色更改
     * @param val
     */
    roleChange(val) {
      this.showDetail = false;
      // 获取对应角色 菜单列表
      this.getMenuList();
    },
    /**
     * Menutree node click
     * @param data
     */
    handleNodeClick(data) {
      this.$refs.rightMenuPart.hideRightMenu();
      this.currentData = data;
      // 获取菜单详情接口
      this.getMenuDetail();
    },
    /**
     * 获取菜单详情
     */
    getMenuDetail() {
      this.showDetail = true;
      this.isEdit = true;
      // TODO 接口获取
      // this.detailData = {
      //   jsid: '', // 角色id
      //   cdid: '', // 菜单id
      //   cdmc: '系统管理',
      //   sjcd: '111', // 上级菜单
      //   cdlx: 2, // 菜单类型
      //   wlqqdz: '', // 外部连接地址
      //   nzgnzy: '', // 内置功能资源
      //   nzgnzydkfs: 1, // 打开方式
      //   tb: 'el-icon-search', // 图标
      //   pxh: '' // 排序号
      // };
      this.menuDetailLoading = true;
      const params = {
        jsid: this.roleRadio,
        cdid: this.currentData.cdid
      };
      findMenuDetail(params)
        .then((res) => {
          this.detailData = res.data.content || [];
        })
        .finally(() => {
          this.menuDetailLoading = false;
        });
    },
    /**
     * 设置icon图标确定
     * @param val
     */
    setIcon(val) {
      if (this.showNewDialog) {
        this.detailInitData.tb = val;
      } else {
        this.detailData.tb = val;
      }
      this.$refs.iconTree.hideDialog();
    },
    /**
     * 打开icon 列表
     */
    openIconDrawer() {
      if (this.showNewDialog) {
        this.checkName = this.detailInitData.tb;
      } else {
        this.checkName = this.detailData.tb;
      }
      this.$refs.iconTree.showDialog();
    },
    /**
     * 菜单编辑保存
     */
    menuUpdate() {
      this.menuDetailLoading = true;
      const params = cloneDeep(this.detailData);
      params.wlgnmc = params.cdmc;
      menuDetailSave(params)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success('保存成功');
            this.isEdit = true;
            const next = () => {
              this.$nextTick(() => {
                this.setTreeCurrentActive(params);
              });
            };
            this.getMenuList(next);
          }
        })
        .finally(() => {
          this.menuDetailLoading = false;
        });
    },
    /**
     * 资源确定事件
     * @param val
     */
    drawerCertain(val) {
      if (val.length <= 0) {
        this.$message.warning('请选择资源');
        return;
      }
      if (this.isRadio) {
        if (this.showNewDialog) {
          this.detailInitData.nzgnzyid = val[0].gnzyid;
          this.detailInitData.wlgnmc = val[0].gnzymc;
        } else {
          this.detailData.nzgnzyid = val[0].gnzyid;
          this.detailData.wlgnmc = val[0].gnzymc;
        }
        this.$refs.roleTree.hideDialog();
      } else {
        const list = val.map((x) => x.gnzyid);
        const params = {
          cdid: this.currentData.cdid,
          jsid: this.roleRadio,
          cdList: list,
          xtbs: this.xtbs
        };
        this.sourceLoading = true;
        resourceAddMore(params).then((res) => {
          if (res.code === 200) {
            this.$message.success('保存成功');
            this.$refs.roleTree.hideDialog();
            this.getMenuList();
          }
        }).finally(() => {
          this.sourceLoading = false;
        });
      }
    },
    /**
     * 打开资源弹窗
     */
    openSourceDrawer() {
      this.isRadio = true;
      this.$refs.roleTree.showDialog();
      const id = !this.showNewDialog ? this.detailData.nzgnzyid : this.detailInitData.nzgnzyid;
      if (id) {
        this.$nextTick(() => {
          this.$refs.roleTree.$refs.tree.setCheckedKeys([id]);
        });
      } else {
        this.$nextTick(() => {
          this.$refs.roleTree.$refs.tree.setCheckedKeys([]);
        });
      }
      this.sourceLoading = true;
      findResourcesList({ jsid: this.roleRadio })
        .then((res) => {
          this.resourceTree = res.data.content || [];
        })
        .finally(() => {
          this.sourceLoading = false;
        });
    },
    /**
     * 右键菜单列表
     * @param event
     * @param item
     */
    openMenu(event, item) {
      if (item.cdlx !== 1) {
        this.rightMenuOptions.list = [
          {
            label: '查看详情',
            onClick: () => {
              this.rightMenuDetail(item);
            }
          },
          {
            label: '编辑',
            onClick: () => {
              this.rightMenuEdit(item);
            }
          },
          {
            label: '删除',
            onClick: () => {
              this.rightMenuDelete(item);
            },
            style: 'color:red'
          }
        ];
      } else {
        this.rightMenuOptions.list = [
          {
            label: '查看详情',
            onClick: () => {
              this.rightMenuDetail(item);
            }
          },
          {
            label: '新建文件夹',
            onClick: () => {
              this.rightMenuAddFile(item);
            }
          },
          {
            label: '新建系统资源',
            onClick: () => {
              this.rightMenuAddSource(item);
            }
          },
          {
            label: '批量新建系统资源',
            onClick: () => {
              this.rightMenuAddSourceMore(item);
            }
          },
          {
            label: '编辑',
            onClick: () => {
              this.rightMenuEdit(item);
            }
          },
          {
            label: '删除',
            onClick: () => {
              this.rightMenuDelete(item);
            },
            style: 'color:red'
          }
        ];
      }
      this.$refs.rightMenuPart.showRightMenu();
      this.rightMenuOptions.event = event;
      this.rightMenuOptions.ref = this.$refs.rightMenuPart.$el;
    },
    /**
     * 设置tree 选中状态
     * @param val
     */
    setTreeCurrentActive(val) {
      this.$refs.menuTree.setCurrentKey(val[this.menuProps.id]);
    },
    /**
     * 右键编辑事件
     * @param val
     */
    rightMenuEdit(val) {
      // console.log('获取', val);
      this.setTreeCurrentActive(val);
      this.currentData = val;
      this.getMenuDetail();
      this.isEdit = false;

      // 新增
      // this.showNewDialog = true;
    },
    /**
     * 右键详情
     */
    rightMenuDetail(val) {
      this.isEdit = true;
      this.setTreeCurrentActive(val);
      this.currentData = val;
      this.getMenuDetail();
    },
    /**
     * 菜单删除
     */
    rightMenuDelete(val) {
      // this.setTreeCurrentActive(val);
      this.menuDeleteFn(val);
    },
    /**
     * 菜单删除接口
     */
    menuDeleteFn(val) {
      menuDelete({ cdid: val.cdid, jsid: this.roleRadio })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.getMenuList();
            this.showDetail = false;
            this.setTreeCurrentActive('');
          }
        })
        .finally(() => {

        });
    },
    /**
     * 右键添加文件夹
     * @param val
     */
    rightMenuAddFile(val) {
      this.showNewDialog = true;
      this.title = '新建文件夹';
      this.detailInitData = {
        cdid: '', // 菜单id
        jsid: this.roleRadio,
        fcdid: val.cdid, // 父级id
        fcdmc: val.cdmc, // 父级名称
        tb: '', // 图标
        pxh: '', // 排序号
        nzgnzyid: '', // 内置功能资源id
        nzgnzydkfs: '', // 内置功能资源打开方式
        wlqqdz: '', // 外部连接请求地址
        wlgnmc: '', // 外部连接请求名称
        cdlx: 1, // 菜单类型
        cdmc: '' // 带单名称
      };
    },
    /**
     * 右键批量添加资源
     * @param val
     */
    rightMenuAddSourceMore(val) {
      this.isRadio = false;
      this.currentData = val;
      // 资源列表接口
      findResourcesList({ jsid: this.roleRadio })
        .then((res) => {
          this.resourceTree = res.data.content || [];
        })
        .finally(() => {
          this.sourceLoading = false;
        });
      this.$refs.roleTree.showDialog();
      this.$nextTick(() => {
        this.$refs.roleTree.$refs.tree.setCheckedKeys([]);
      });
    },
    /**
     * 右键添加资源
     * @param val
     */
    rightMenuAddSource(val) {
      this.showNewDialog = true;
      this.title = '新建资源';
      this.detailInitData = {
        cdid: '', // 菜单id
        jsid: this.roleRadio,
        fcdid: val.cdid, // 父级id
        fcdmc: val.cdmc, // 父级名称
        tb: '', // 图标
        pxh: '', // 排序号
        nzgnzyid: '', // 内置功能资源id
        nzgnzydkfs: '', // 内置功能资源打开方式
        wlqqdz: '', // 外部连接请求地址
        wlgnmc: '', // 外部连接请求名称
        cdlx: 2, // 菜单类型
        cdmc: '' // 带单名称
      };
    },
    /**
     * 新建根目录文件夹弹窗展示
     */
    addRootFile() {
      if (!this.xtbs) {
        this.$message.warning('请先选择所在系统');
        return;
      }
      this.title = '新建根文件';
      this.showNewDialog = true;
      this.detailInitData = {
        cdid: '', // 菜单id
        jsid: this.roleRadio,
        fcdid: null, // 父级id
        fcdmc: '', // 父级名称
        tb: '', // 图标
        pxh: '', // 排序号
        nzgnzyid: '', // 内置功能资源id
        nzgnzydkfs: '', // 内置功能资源打开方式
        wlqqdz: '', // 外部连接请求地址
        wlgnmc: '', // 外部连接请求名称
        cdlx: 1, // 菜单类型
        cdmc: '' // 带单名称
      };
    },
    /**
     * 文件新增接口
     */
    fileAdd() {
      // 校验
      const params = cloneDeep(this.detailInitData);
      params.xtbs = this.xtbs;
      if (params.cdlx !== 2) {
        params.wlgnmc = params.cdmc;
      }
      // eslint-disable-next-line consistent-return
      this.$refs.editDialog.$refs.form.validate((valid) => {
        if (valid) {
          this.fileAddLoading = true;
          resourceAdd(params)
            .then((res) => {
              if (res.code === 200) {
                this.$message.success('新建成功');
                this.showDetail = false;
                this.getMenuList();
                this.showDetail = false;
              }
            })
            .finally(() => {
              this.fileAddLoading = false;
              this.showNewDialog = false;
            });
        } else {
          return false;
        }
      });
    }
  }
};

</script>

<style lang="scss" scoped>
.cdgl-content {
  display: flex;

  .main {
    height: 100%;
    padding: $page-content-padding;
    background-color: #ffffff;
    overflow: auto;

    &-content {
      height: calc(100% - 42px);
      overflow: auto;
    }
    .cdgl-select-xtbs{
      display: flex;
      padding-top: 10px;
      align-items: center;
      .cdgl-xtbs-label{
        color: rgb(81, 90, 110);
        font-weight: bold;
        padding-right: 10px;
      }
      .cdgl-xtbs-value{
        flex: 1;
      }
    }
  }

  .left {
    padding-right: $page-content-padding;
    flex-shrink: 0;
    width: 200px;
    height: 100%;

    .role-radio-list {
      padding: $page-content-padding;
    }
  }

  .middle {
    padding-right: $page-content-padding;
    flex-shrink: 0;
    width: 300px;
    height: 100%;
  }

  .right {
    padding: $page-content-padding;
    height: 100%;
    flex: 1;
    overflow: auto;
    background-color: #ffffff;
  }
}
</style>
<style lang="scss">
.cdgl-content {
  .el-tabs__content {
    height: calc(100% - 52px);
    overflow: auto;
  }

  .custom-tree-node {
    width: 100%;
  }
}
</style>
