<template>
  <div class="info-table">
    <el-table
      ref="multipleTable"
      class="zhxy-table"
      :data="tableData"
      stripe
      border
      height="100%"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" label="" width="50"></el-table-column>
      <el-table-column prop="hjmc" label="环节名称"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    // table数据
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 选中selection list
      multipleSelection: []
    };
  },
  methods: {
    /**
     * 默认勾选row设置
     * @param rows
     */
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    /**
     * selection change 事件
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>

<style lang="scss" scoped>
  .info-table {
    height: 100%;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto
  }
</style>
