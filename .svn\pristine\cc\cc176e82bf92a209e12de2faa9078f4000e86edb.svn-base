<template>
  <el-table class="zhxy-table" height="100%" :data="tableData" stripe border>
    <el-table-column prop="ywxtbh" label="应用系统编号"></el-table-column>
    <el-table-column prop="ywxtmc" label="应用系统名称"></el-table-column>
    <el-table-column prop="ywxtyt" label="应用系统用途"></el-table-column>
    <el-table-column prop="kyzt" label="可用状态">
      <template slot-scope="scope">
        <span v-if="scope.row.kyzt === 1 ">可用</span>
        <span v-else>异常</span>
      </template>
    </el-table-column>
<!--    <el-table-column prop="fzr" label="负责人"></el-table-column>-->
  </el-table>
</template>

<script>
export default {
  name: 'ZjjjcTable',
  props: {
    tableData: {
      type: Array,
      default: () => ([])
    }
  }
};
</script>

<style scoped>

</style>
