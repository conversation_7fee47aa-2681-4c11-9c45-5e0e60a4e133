<template>
  <div>
    <div class="dybzx-page-content">
      <div class="jksqgl-pageform-header">
        <span class="zhxy-form-label" slot="label">基本信息</span>
        <el-button type="primary" @click="edit()" size="small">编辑</el-button>
      </div>
    </div>
    <div style="display: flex;flex-wrap:wrap">
      <div v-for="(item,index) in arr" :key="index" :style="`width:${item.isHalf ? '50%':'100%'}`">
        <div>
          <span class="jksqgl-page-content-jbxx-left" :style="`width:${item.isHalf ? '30%':'15%'}`">{{
              item.label
            }}</span>
          <span class="jksqgl-page-content-jbxx-right" :style="`width:${item.isHalf ? '70%':'35%'}`">{{
              item.value
            }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'JksqglJbxx',
  props: {
    arr: {
      type: Array,
      default: () => ([])
    }
  },
  methods: {
    edit() {
      this.$emit('editForm');
    }
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    margin-top: 10px;
    padding: 0 10px;
    box-sizing: border-box;
  }

  ///* .button-list {
  //   width: 100%;
  //   display: flex;
  //   flex-direction: row-reverse;
  //   padding-right: 2%;
  //   height: 32px;
  // }*/

  /* .dybzx-page-content-box {
     width: 25%;
     padding: 0 2%;
   }

   .dybzx-page-content-card {
     background-color: #FFFFFF;
     width: 100%;
     height: 228px;
     border: 1px solid #EAEAEA;
     box-shadow: #ededed 0px 0px 5px 1px;
     position: relative;
     overflow: hidden;
     margin-top: 20px;

     .card-tip {
       position: absolute;
       top: 10px;
       right: -30px;
       width: 120px;
       background-color: #286fb7;
       color: #FFFFFF;
       font-size: 16px;
       padding: 7px 0;
       text-align: center;
       transform: rotate(45deg);
     }

     .card-top {
       display: flex;
       align-items: center;
       background-color: #F2F2F2;
       height: 52px;
       padding: 0 10px;

       .card-top-icon {
         width: 26px;
         height: 26px;
         background: url("../../../assets/icon-xitong.png") no-repeat;
         background-size: cover;
       }

       .card-top-title {
         font-size: 16px;
         color: #2d8cf0;
         padding-left: 10px;
       }

     }

     .card-middle {
       padding: 20px 30px;

       .card-middle-item {
         display: flex;
         line-height: 30px;

         .card-middle-item-label {
           width: 80px;
           flex-shrink: 0;
           color: #90a4af;
         }

         .card-middle-item-value {
           overflow: hidden;
           text-overflow: ellipsis;
           white-space: nowrap;
         }
       }

     }

     .card-bottom {
       display: flex;
       height: 45px;

       .card-bottom-btn {
         display: flex;
         justify-content: center;
         align-items: center;
         width: 50%;
         border-top: 1px solid #EAEAEA;
         cursor: pointer;
       }

       .card-bottom-btn:hover {
         opacity: 0.8;
       }

       .card-bottom-left {
         border-right: 1px solid #EAEAEA;
         color: #286fb7;
       }

       .card-bottom-right {
         color: #90a4af;
       }
     }
   }*/
}

.jksqgl-page-content-jbxx-left {
  display: inline-block;
  height: 50px;
  text-align: center;
  border: 1px solid #ededed;
  background-color: #FFFFFF;
  line-height: 50px;
}

.jksqgl-page-content-jbxx-right {
  display: inline-block;
  height: 50px;
  padding-left: 10px;
  border: 1px solid #ededed;
  background-color: #FFFFFF;
  line-height: 50px;
}
</style>
