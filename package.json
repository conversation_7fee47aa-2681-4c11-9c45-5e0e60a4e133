{"name": "vue-spa", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode dev", "build": "vue-cli-service build --mode pro", "dev:mock": "vue-cli-service serve --mode mock"}, "dependencies": {"@tinymce/tinymce-vue": "^3.2.8", "axios": "^0.21.1", "core-js": "^3.6.5", "dayjs": "^1.10.3", "default-passive-events": "^2.0.0", "echarts": "^5.0.2", "element-ui": "^2.14.1", "js-cookie": "^3.0.5", "js-md5": "^0.7.3", "lodash": "^4.17.20", "react": "^19.0.0", "spark-md5": "^3.0.1", "tinymce": "^5.8.2", "uuid": "^8.3.2", "vue": "^2.6.11", "vue-json-excel": "^0.3.0", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.13.12", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-airbnb": "^5.0.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-vue": "^6.2.2", "mockjs": "^1.1.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}