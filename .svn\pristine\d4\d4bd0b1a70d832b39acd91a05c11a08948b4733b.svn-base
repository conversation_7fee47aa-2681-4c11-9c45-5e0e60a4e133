# vue-spa
## <a id="目录结构">目录结构</a>
```vue
vue-spa
├── public                                      静态资源
│   ├── favicon.ico                             favicon图标
│   └── index.html                              模板文件项目入口
├── src                                         
│   ├── app                                     小应用文件夹
│   │   ├── index                               小应用(名称)
│   │   │   ├── api                             数据接口
│   │   │   │   └── user                        数据接口-小应用模块文件夹
│   │   │   │       └── user.js                 数据接口-微页面接口集合
│   │   │   ├── assets                          资源文件夹
│   │   │   │   └── logo.png
│   │   │   ├── components                      小应用全局通用组件
│   │   │   │   └── HelloWorld.vue
│   │   │   ├── directives                      小应用全局自定义指令
│   │   │   ├── filters                         小应用全局filter
│   │   │   ├── mixins                          小应用全局mixins
│   │   │   │   └── index.js
│   │   │   ├── mock                            小应用模拟数据源
│   │   │   │   └── xxxx                        小应用模拟数据模块文件夹
│   │   │   │       └── xxxx.js                 小应用微页面模拟数据源
│   │   │   ├── pages                           微页面文件夹
│   │   │   │   ├── 404.vue
│   │   │   │   ├── About.vue
│   │   │   │   ├── Home.vue
│   │   │   │   ├── rolePage.vue
│   │   │   │   ├── rolePage2.vue
│   │   │   │   └── user
│   │   │   │       ├── index.vue
│   │   │   │       └── UserDetail.vue
│   │   │   ├── router                         小应用路由文件
│   │   │   │   └── index.js
│   │   │   ├── store                          小应用状态管理文件(vuex)
│   │   │   │   ├── actions.js                 小应用改变store中状态异步事件文件
│   │   │   │   ├── index.js                   小应用状态管理模块store集合(state/mutations/actions)
│   │   │   │   ├── modules                    小应用状态管理模块文件夹
│   │   │   │   │   └── user                   小应用状态管理模块名称(下面结构同上)
│   │   │   │   │       ├── actions.js         
│   │   │   │   │       ├── index.js           
│   │   │   │   │       ├── mutations.js       
│   │   │   │   │       └── state.js           
│   │   │   │   ├── mutations.js               小应用改变store中状态同步事件文件
│   │   │   │   └── state.js                   小应用store中存储的各个状态文件
│   │   │   ├── styles                         小应用全局样式文件
│   │   │   │   └── common.css
│   │   │   └── utils                          小应用全局公共方法
│   │   │       └── permission.js
│   │   └── xxxx                               小应用文件名(同上)
│   ├── App.vue                                项目入口页面
│   ├── assets                                 项目通用资源文件
│   │   └── logo.png
│   ├── components                             项目通用组件
│   │   └── HelloWorld.vue
│   ├── directives                             项目通用指令
│   ├── main.js                                入口文件
│   ├── router                                 全局router集合
│   │   └── index.js
│   ├── store                                  项目状态管理集合(vuex)
│   │   ├── actions.js
│   │   ├── index.js
│   │   ├── mutations.js
│   │   └── state.js
│   ├── styles                                 全局通用css
│   │   └── reset.css
│   └── utils                                  全局公共方法
│       ├── auth-utils.js
│       ├── date-utils.js
│       ├── dom-utils.js
│       ├── elementImport.js
│       ├── local-utils.js
│       ├── permission.js
│       ├── request.js
│       └── validate-utils.js
├── .browserslistrc                            浏览器兼容设置
├── .editorconfig                              编译器风格设置
├── .env.dev                                   开发环境下的配置文件
├── .env.pro                                   生产环境下的配置文件
├── .eslintrc.js                               eslint配置文件
├── babel.config.js                            babel语法编译配置文件
├── package-lock.json                          锁定包版本
├── package.json                               项目webpack信息
├── README.md                                
└── vue.config.js                              工程配置文件

```
注意: 其中 app/example 文件夹是基础小应用文件结构, 新添加的小应用可以直接复制更改文件名称

项目需要的安装包:可从 [仓库](http://222.171.203.220:8082/zhaoshuhong/vue-spa/tree/master/environment) 获取
## node 环境搭建
1、下载

下载地址：[https://nodejs.org/zh-cn/download/](http://nodejs.cn/download/) 

选择相应的版本下载(当前长期支持版: 14.15.4 (包含 npm 6.14.10))

![image](http://222.171.203.220:8082/zhaoshuhong/vue-spa/raw/master/environment/node_download.png)

2、解压缩

将文件解压到要安装的位置，并新建两个目录

node-global :npm全局安装位置

node-cache：npm 缓存路径

![image](http://222.171.203.220:8082/zhaoshuhong/vue-spa/raw/master/environment/node_file.png)

3、配置

配置环境变量：

控制面板\系统和安全\系统\高级系统设置\高级\环境变量

将node.exe 所在的目录添加到path环境变量，这样我们在使用命令行时就可以在任意路径使用node命令了，同时该目录下有一个npm.cmd文件，打开文件其实就i是将我们的npm命令映射到node.exe npm-cli.js，由于存在该映射所以只要把node.exe 所在的目录添加到path环境变量，也可以在任何目录下执行npm install了。

![image](http://222.171.203.220:8082/zhaoshuhong/vue-spa/raw/master/environment/node_path.png)

写到这里其实node就算已经装好了(不要忘记配置了global的文件也要添加到环境变量中)。

在命令行中输入如下命令测试

node -v

npm -v

那么node-global :npm全局安装位置，node-cache：npm 缓存路径 又是怎么与npm发生关系呢？

通过如下命令进行配置：

npm config set prefix "E:\node\node-v12.16.1-win-x64\node-global"

npm config set cache "E:\node\node-v12.16.1-win-x64\node-cache"

执行npm命令进行测试：npm install webpack -g

会发现node-global下node_modules中多了webpack 文件夹

![image](http://222.171.203.220:8082/zhaoshuhong/vue-spa/raw/master/environment/node_global.png)

webpack是用来打包的module，通常我们会在命令行中执行，而webpack同样在node-global中做了映射，所以只需要将node-global加入path环境变量即可。

![image](http://222.171.203.220:8082/zhaoshuhong/vue-spa/raw/master/environment/node_global_web.png)

win下，.npmrc文件的位置为：%USERPROFILE%/.npmrc

## git工具安装

+ git 安装
按照操作命令一步一步安装即可  [git学习](https://www.liaoxuefeng.com/wiki/896043488029600/896067074338496)

```vue
// 设置自己的用户信息
$ git config --global user.name "Your Name"
$ git config --global user.email "<EMAIL>"
```
## 编辑器
[webstorm](https://www.jetbrains.com/webstorm/) 
可以进入Settings/Preferences/Version Control/Commit将Use non-modal commit interface取消勾选 显示localChages
## 调试工具 
chrome 可下载 [dev-tools](https://github.com/vuejs/vue-devtools) 扩展工具调试 vue项目


## 项目运行
+ `git config --global core.autocrlf false` 禁止git 转换换行符
+ `git clone [项目地址]`
+ 安装cnpm `npm install -g cnpm --registry=https://registry.npm.taobao.org`
+ `npm install` 为了安装快捷可以使用淘宝镜像 `cnpm install`  
+ 本地运行 `npm run dev`
+ 本地mock `npm run dev:mock`
+ 生产打包 `npm run build`

修改内容： constant.js

## 方法说明
+ 删除提示
  
  |  参数   | 说明 |  类型 | 默认值   
  |  ----  | ----  |  ----  | ----  |
  | config  | title:提示title;text:提示内容;btnText:确定按钮执行文字;confirmButtonText:确定按钮文字;cancelButtonText:取消按钮文字|Object|{text: '是否删除?', btnText: '执行中...', title: '', confirmButtonText: '确定', cancelButtonText: '取消'}|
  | callback  | 确定按钮事件的回调 |function(successDone) ,successDone是成功之后取消按钮加载和box隐藏的方法 |  |
```vue
// 使用说明
<script >
import { deletePrompt } from '@/utils/action-utils.js';

methods:{
  /**
   * 删除按钮事件
   * @param val
   */
  listDelete(val) {
    // 设置参数
    const config = {
      text: '是否删除?',
      btnText: '执行中...'
    };
    // 设置确定的callback 及成功操作
    const deleteItem = (successAction) => {
      // 删除接口
      axios.post('/api',val).then(()=>{
        // 成功操作...
      }).catch(()=>{
          
      }).finally(()=>{
        // 取消加载状态
        successAction();
      })
    };
    // 删除提示调用
    deletePrompt(config, deleteItem);
  }
}
</script>

```
