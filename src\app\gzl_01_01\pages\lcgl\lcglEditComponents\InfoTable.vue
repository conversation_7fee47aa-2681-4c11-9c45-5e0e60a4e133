<template>
  <div class="info-table">
    <el-table
      ref="singleTable"
      v-loading="loading"
      class="zhxy-table"
      :data="tableData"
      highlight-current-row
      @row-click="handleCurrentChange"
      stripe
      border
      height="100%">
      <el-table-column prop="hjmc" label="环节名称" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sfky" label="是否可用" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.sfky | filterSfky }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="bzxx" label="帮助信息" show-overflow-tooltip></el-table-column>
      <el-table-column prop="mbhj" label="目标环节" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span class="button-text" v-if="scope.row.mbhj" @click="selectTarget(scope.row)">{{ scope.row.mbhj }}</span>
          <span v-else> <el-button type="text" @click="selectTarget(scope.row)">选择目标环节</el-button> </span>
        </template>
      </el-table-column>
      <el-table-column prop="pxh" label="排序号" width="150"></el-table-column>
      <!--      <el-table-column prop="cjr" label="创建人" width="130"></el-table-column>
            <el-table-column prop="cjsj" label="创建时间" width="130"></el-table-column>
            <el-table-column prop="bgr" label="变更人" width="130"></el-table-column>
            <el-table-column prop="bgsj" label="变更时间" width="130"></el-table-column>-->
      <el-table-column fixed="right" prop="" label="操作" width="150">
        <template slot-scope="scope">
          <el-button size="small" @click="modify(scope.row)" type="text">编辑</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" @click="buttonConfig(scope.row)" type="text">按钮配置</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" @click="deleteItem(scope.row)" type="text">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

</template>

<script>
export default {
  filters: {
    // 是否可用 filter解析
    filterSfky(val) {
      const list = {
        2: '禁用',
        1: '可用'
      };
      return list[val];
    }
  },
  props: {
    // table 数据
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {
    /**
     * 按钮配置button
     * @param val
     */
    buttonConfig(val) {
      this.$emit('buttonConfig', val);
    },
    /**
     * 编辑button
     * @param val
     */
    modify(val) {
      this.$emit('modify', val);
    },
    /**
     * 删除button
     * @param val
     */
    deleteItem(val) {
      this.$emit('deleteItem', val);
    },
    /**
     * 目标环节 select
     * @param val
     */
    selectTarget(val) {
      this.$emit('selectTarget', val);
    },
    /**
     * table row点击事件
     * @param val
     */
    handleCurrentChange(val) {
      this.$emit('getTargetTable', val);
    },
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    }
  }
};
</script>

<style lang="scss" scoped>
.info-table {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  .button-text{
    color: $page-font-hover-color;
    cursor: pointer;
  }
}
</style>
