<template>
  <div ref="myChart" style="width: 100%;height: 100%;box-sizing: border-box;"></div>
</template>

<script>
// 引入基本模板
import { echarsMixins } from '@/app/ywxtgl_01_01/mixins/echars-resize';
import { cloneDeep } from 'lodash';

const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/line');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  mixins: [echarsMixins],
  props: {
    rqSet: {
      type: Array
    },
    arr1: {
      type: Array
    },
    arr2: {
      type: Array
    },
    arr3: {
      type: Array
    },
    color: {
      type: String,
      default() {
        return '#3398DB';
      }
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    rqSet(val) {
      this.drawLine();
    },
    arr1(val) {
      this.drawLine();
    },
    arr2(val) {
      this.drawLine();
    },
    arr3(val) {
      this.drawLine();
    }

  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);
      this.echarsDom = myChart;

      // const dataC1 = [10, 8, 10, 10, 5, 6, 5];
      // const dataC2 = [15, 20, 10, 10, 8, 15, 10];
      // const dataC3 = [20, 40, 20, 50, 85, 15, 10];
      // const xData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

      // const dataC1 = [0, 0, 0, 0, 0, 1, 0];
      // const dataC2 = [0, 0, 0, 1, 1, 0, 0];
      // const dataC3 = [0, 0, 0, 0, 0, 1, 0];
      // const xData = ['2021-05-26', '2021-05-27', '2021-05-28', '2021-05-29', '2021-05-30', '2021-05-31', '2021-06-01'];

      const dataC1 = cloneDeep(this.arr3);
      const dataC2 = cloneDeep(this.arr2);
      const dataC3 = cloneDeep(this.arr1);
      const xData = cloneDeep(this.rqSet);

      for (let i = 0; i < xData.length; i++) {
        dataC1.splice(i, 1, { name: xData[i], value: dataC1[i] });
        dataC2.splice(i, 1, { name: xData[i], value: dataC2[i] });
        dataC3.splice(i, 1, { name: xData[i], value: dataC3[i] });
      }

      const fontColor = '#30eee9';
      const option = {
        grid: {
          left: '2%',
          right: '2%',
          top: '10%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            lineStyle: {
              color: '#57617B'
            }
          }
        },
        legend: {
          show: false,
          data: ['一级', '二级', '三级']
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            color: '#65aaf1'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
              width: 1
            }
          },
          data: xData
        }],
        yAxis: [{
          type: 'value',
          nameTextStyle: {
            color: '#65aaf1',
            fontStyle: 'normal'
          },
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#65aaf1'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
              width: 1,
              type: 'solid'
            }
          }
        }],
        series: [{
          name: '一级',
          type: 'line',
          stack: '总量',
          symbol: 'circle',
          showSymbol: false,
          symbolSize: 7,
          itemStyle: {
            normal: {
              color: '#ff6666',
              lineStyle: {
                color: '#ff6666',
                width: 1
              },
              areaStyle: {
                // color: '#94C9EC'
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0.4,
                  color: 'rgba(255, 102, 102,0.5)'
                }, {
                  offset: 1,
                  color: 'rgba(255, 102, 102,0.9)'
                }])
              }
            }
          },
          markPoint: {
            itemStyle: {
              normal: {
                color: 'red'
              }
            }
          },
          data: dataC1
        },
        {
          name: '二级',
          type: 'line',
          stack: '总量',
          symbol: 'circle',
          showSymbol: false,
          symbolSize: 7,

          itemStyle: {
            normal: {
              color: '#ffcd46',
              lineStyle: {
                color: '#ffcd46',
                width: 1
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0.4,
                  color: 'rgba(255, 205, 70,0.5)'
                }, {
                  offset: 1,
                  color: 'rgba(255, 205, 70,0.9)'
                }])
              }
            }
          },
          data: dataC2
        },
        {
          name: '三级',
          type: 'line',
          stack: '总量',
          symbol: 'circle',
          showSymbol: false,
          symbolSize: 7,
          itemStyle: {
            normal: {
              color: '#409eff',
              lineStyle: {
                color: '#409eff',
                width: 1
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0.4,
                  color: 'rgba(64,158,255,0.5)'
                }, {
                  offset: 1,
                  color: 'rgba(64,158,255,0.9)'
                }])
              }
            }
          },
          data: dataC3
        }
        ]
      };

      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
