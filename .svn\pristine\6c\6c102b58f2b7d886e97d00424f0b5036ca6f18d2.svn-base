<template>
  <div :style="`height: ${scrollerHeight}`">
    <el-table
      :data="userTableData"
      stripe
      border
      class="zhxy-table"
      height="calc(100% - 40px)"
      @selection-change="selectionChange">
      <el-table-column
        align="center"
        type="selection"
        label=""
        width="52">
      </el-table-column>
      <el-table-column align="center" type="index" label="序号" width="52"></el-table-column>
      <el-table-column prop="xm" label="姓名" width="100"></el-table-column>
      <el-table-column prop="yhid" label="职工号" width="100"></el-table-column>
      <el-table-column prop="bmmc" label="所属部门" width=""></el-table-column>
      <el-table-column prop="jsmx" label="角色明细" width="">
        <template slot-scope="scope">
          <span class="span-active" @click="modifyRole(scope.row)">{{scope.row.jsmx}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="136">
        <template slot-scope="scope">
          <el-button size="small" @click="modifyRole(scope.row)" type="text">编辑角色</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" type="text" @click="delYh(scope.row)">取消授权</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pageIn">
      <el-pagination
        size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[30, 50, 100, 200,500]"
        layout="total, sizes, prev, pager, next, jumper"
        :total=total>
      </el-pagination>
    </div>
  </div>
</template>
<script>
import {
  addJsyh, del, delJsyh, findJsxyhlist, delPlJsyh
} from '../../../api/jsgl/jsgl';

export default {
  name: 'DepartmentUser',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: [String, Number],
      default: 0
    }
  },
  inject: ['reload'],
  data() {
    return {
      jsid: '',
      // table page
      page: 1,
      pageSize: 30,
      // 用户查询条件
      cxtj: '',
      userTableData: this.tableData,
      userdata: {
        name: ''
      },
      total: 0,
      multipleYhSelection: [],
      selections: ''
    };
  },
  methods: {
    selectionChange(selection) {
      this.selections = selection;
    },
    // 批量删除角色下用户
    deleteJsyh() {
      if (this.selections === '') {
        this.$message.success('请勾选角色用户');
      } else {
        const arr = [];
        this.selections.forEach((item) => {
          const itemparam = {
            jsid: this.jsid,
            yhid: item.yhid
          };
          arr.push(itemparam);
        });
        /**
         * 批量删除 角色下用户 接口
         */
        const param = {
          yharr: arr
        };
        this.$confirm('请确认批量删除？', {
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              this.deletePlJsyhCurrent(param, instance, done);
            } else {
              done();
            }
          }
        })
          .then(() => {
          })
          .catch(() => {
          });
      }
    },

    /**
     * 批量删除 角色下用户 接口
     */
    deletePlJsyhCurrent(param, instance, done) {
      delPlJsyh(param).then((res) => {
        this.clickUserJsyh(this.userdata);
        this.$message.success('删除成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    action(params) {
    },
    // 调用父页面方法
    modifyRole(val) {
      this.$emit('modifyUsermx', val, '1');
    },
    /**
     * 删除 角色下用户 接口
     */
    delYh(data) {
      this.$confirm('请确认取消授权？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            this.deleteJsyhCurrent(data, instance, done);
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 删除 角色下用户 接口
     */
    deleteJsyhCurrent(data, instance, done) {
      const param = {
        jsid: this.jsid,
        yhid: data.yhid
      };
      delJsyh(param).then((res) => {
        this.clickUserJsyh(this.userdata);
        this.$message.success('取消授权成功');
      }).finally(() => {
        instance.confirmButtonLoading = false;
        done();
      });
    },
    /**
     * 获取 角色下用户 接口
     */
    clickUserJsyh(data) {
      this.cxtj = data.name;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: data.name
      };
      /**
       * 获取 角色用户 接口
       */
      findJsxyhlist(param).then((res) => {
        this.userTableData = res.data.content;
        this.total = res.data.pageInfo.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    /**
     * 获取 角色下用户 接口(分页)
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: this.cxtj
      };
      /**
       * 获取 角色用户 接口
       */
      findJsxyhlist(param).then((res) => {
        this.userTableData = res.data.content;
      }).finally(() => {
        this.loading = false;
      });
    },
    /**
     * currentPage change event
     * @param val
     */
    handleCurrentChange(val) {
      this.page = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: this.cxtj
      };
      /**
       * 获取 角色用户 接口
       */
      findJsxyhlist(param).then((res) => {
        this.userTableData = res.data.content;
      }).finally(() => {
        this.loading = false;
      });
    },
    /**
     * 获取 角色用户 接口
     */
    resetUserJsyh() {
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        jsid: this.jsid,
        idxm: ''
      };
      /**
       * 获取 角色用户 接口
       */
      findJsxyhlist(param).then((res) => {
        this.userTableData = res.data.content;
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
  .span-active{
    color: #3a8ee6;
  }
  .button-tab {
    margin-bottom: $page-content-padding;
  }
  .department-user {
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }
    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
