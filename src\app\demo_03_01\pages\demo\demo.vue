<template>
  <div class="lcgl-content">
    <div class="lcgl-content-main">
      <v-title name="minio文件测试"></v-title>
      <div class="content">
        <div class="table">
          <!--      按钮-->
          <div class="button-list">
            <el-button type="primary" icon="el-icon-plus" size="small" @click="submitForm">测试上传
            </el-button>

            <a :href='"/demo_03_01/demo007Upload/minio-download"'>下载
            </a>

            <el-button type="primary" icon="el-icon-plus" size="small" @click="downloadWj">删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!--    上传附件弹窗-->
    <el-dialog :title="addName" :visible.sync="dialogAddFile" width="500px" style="padding:0;" @close="resetAdd">
      附件名称：<el-input v-model="addFileName" autocomplete="off" size="small" style="width: 300px;" ></el-input>
      <div class="add-file-right" style="height:70px;margin-left:100px;margin-top:15px;">
        <div class="add-file-right-img" style="margin-left:70px;">上传文件：</div>
        <input type="file" ref="clearFile" @change="getFile($event)" multiple="multiplt" class="add-file-right-input" style="margin-left:70px;" accept=".docx,.doc,.pdf">
        <span class="add-file-right-more">支持扩展名：.doc .docx .pdf </span>
      </div>
      <div class="add-file-list">
        <ul>
          <li v-for="(item, index) in addArr" :key="index"><a >{{item.name}}</a></li>
        </ul>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddFile" size="small">开始上传</el-button>
        <el-button @click="resetAdd" size="small">全部删除</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  minioUpload, minioDelete
} from '../../api/demo/demo';

// import RoleTreeDialog from './jsglComponents/RoleTreeDialog';
import VTitle from '../../../../components/title/VTitle';

export default {
  components: { // 注册组件
    VTitle
  },
  data() {
    return {
      addName: '上传附件',
      addFileName: '',
      addArr: [],
      // 新建/编辑弹窗 visible
      dialogAddFile: false
    };
  },
  created() {
    //
  },
  methods: {

    resetAdd() {
      this.dialogAddFile = false;
    },
    submitForm() {
      this.dialogAddFile = true;
    },
    getFile(event) {
      const file = event.target.files;
      for (let i = 0; i < file.length; i++) {
        //    上传类型判断
        const imgName = file[i].name;
        const idx = imgName.lastIndexOf('.');
        if (idx !== -1) {
          let ext = imgName.substr(idx + 1).toUpperCase();
          ext = ext.toLowerCase();
          if (ext !== 'pdf' && ext !== 'doc' && ext !== 'docx') {
            this.$message.success('类型不对');
          } else {
            this.addArr.push(file[i]);
          }
        } else {
          this.$message.success('其他');
        }
      }
    },
    submitAddFile() {
      if (this.addArr.length === 0) {
        this.$message({
          type: 'info',
          message: '请选择要上传的文件'
        });
        return;
      }

      const formData = new FormData();
      formData.append('num', this.addType);
      formData.append('linkId', this.addId);
      formData.append('rfilename', this.addFileName);
      for (let i = 0; i < this.addArr.length; i++) {
        formData.append('fileUpload', this.addArr[i]);
      }
      // const config = {
      //   headers: {
      //     'Content-Type': 'multipart/form-data',
      //     Authorization: this.token
      //   }
      // };

      minioUpload(formData).then((res) => {
        if (res.code === 200) {
          this.$message.success('附件上传成功!');
        }
      }).finally(() => {
        this.$message.success('成功后操作');
      });
    },
    downloadWj() {
      minioDelete().then((res) => {
        if (res.code === 200) {
          this.$message.success('功后操作');
        }
      }).finally(() => {
        this.$message.success('下载成功后操作');
      });
    }
  }
};

</script>

<style lang="scss" scoped>
.lcgl-content {

  &-main {
    height: 100%;
    background-color: #ffffff;
    padding: $page-content-padding;
    display: flex;
    flex-direction: column;

    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .table {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
      }
    }

    .button-list {
      margin-bottom: $page-content-margin;
    }
  }
}
</style>
