<template>
  <div ref="myChart" style="width:100%; height:100%"></div>
</template>

<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入折线图组件
require('echarts/lib/chart/line');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  props: {
    list: {
      type: Object
    },
    msg: {
      type: String
    }
  },
  data() {
    return {
      myChart: null,
      myChart64: null
    };
  },
  watch: {
    list(val) {
      this.drawLine();
    }
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      const { list } = this;
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(this.$refs.myChart);
      const option = {
        animation: false,
        title: {
          top: '2%',
          text: list.title,
          textStyle: {
            fontWeight: 'bold',
            fontSize: 14,
            fontFamily: '仿宋_GB2312',
            color: '#333'
          },
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '15%',
          right: '5%',
          bottom: '10%'
        },
        xAxis: {
          data: list.name,
          show: true,
          axisLabel: {
            interval: 0, // 横轴信息全部显示
            // rotate:30,//30度角倾斜显示
            type: 'category',
            show: true,
            textStyle: {
              color: '#333',
              fontSize: 11
            }
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333',
              width: 0.5
              // color:'#3881bb'
            }
          }
        },
        yAxis: {
          splitLine: {
            show: true,
            lineStyle: {
              color: '#224d6f',
              width: 0.5
              // color: '#3881bb'
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333'
              // eslint-disable-next-line no-tabs
              //		            	color:'#3881bb'
            },
            textStyle: {
              color: '#333'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#333'
            }
          }
        },
        series: [{
          type: 'line',
          name: '传输次数',
          data: list.value,
          lineStyle: {
            normal: {
              width: 3
            }
          }
        }]
      };
      this.myChart.setOption(option);
      setTimeout(() => {
        this.$emit('sendMychart', this.msg);
        // alert(1)
      }, 3000);
    },
    // 生成64位图片
    get64Bata() {
      if (typeof (this.myChart) === 'undefined') {
        this.myChart64 = '';
      } else {
        this.myChart64 = this.myChart.getDataURL({ type: 'png' }).split(',')[1];
      }
      // console.log(this.myChart64)
      return this.myChart64;
    }
  }
};
</script>

<style>
</style>
