// 设置token
export const setToken = (token, exdays) => {
  const exdate = new Date();// 获取时间
  exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays);// 保存的天数
  // 字符串拼接cookie
  window.document.cookie = `token=${ token };path=/;expires=${ exdate.toGMTString()}`;
};
// 读取token
export const getToken = () => {
  if (document.cookie.length > 0) {
    const arr = document.cookie.split('; ');
    for (let i = 0; i < arr.length; i++) {
      const arr2 = arr[i].split('=');// 再次切割
      // 判断查找相对应的值
      if (arr2[0] === 'token') {
        return arr2[1];// 保存到保存数据的地方
      }
    }
  }
  return '';
};
// 清除token
export const clearToken = () => {
  setToken('', -1);// 修改2值都为空，天数为负1天就好了
};
