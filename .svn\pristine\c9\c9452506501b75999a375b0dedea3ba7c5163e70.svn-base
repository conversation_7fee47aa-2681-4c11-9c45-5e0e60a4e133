<template>
  <div class="department-user" ref="element">
    <el-button
      v-if="isShowButton"
      type="primary"
      size="small"
      @click="buttonAction">
      {{ isEdit ? '编辑' : '保存' }}
    </el-button>
    <el-form
      ref="form"
      :rules="rules"
      :disabled="isEdit"
      label-width="150px"
      :model="formData"
      class="zhxy-form zhxy-form-search-part"
      :class="isEdit ? '' : 'form-status-edit'">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">上级菜单</span>
        <el-input
          disabled
          class="zhxy-form-inline"
          v-model="formData.fcdmc"
          placeholder="上级菜单"
          size="small"></el-input>
      </el-form-item>
      <el-form-item prop="cdlx">
        <span slot="label" class="zhxy-form-label">菜单类型</span>
        <el-select
          :disabled="disabled"
          class="zhxy-form-inline"
          v-model="formData.cdlx"
          placeholder="菜单类型"
          size="small">
          <el-option
            v-for="item in menuOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="!isInerRouter" prop="cdmc">
        <span class="zhxy-form-label" slot="label">名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.cdmc"
                  placeholder="名称"
                  size="small"></el-input>
      </el-form-item>
      <el-form-item prop="wlqqdz" v-if="!isFile && isOutRouter">
        <span class="zhxy-form-label" slot="label">外部链接地址</span>
        <el-input class="zhxy-form-inline" v-model="formData.wlqqdz" placeholder="外部连接地址"
                  size="small"></el-input>
      </el-form-item>

      <el-form-item prop="wlgnmc" v-if="!isFile && isInerRouter">
        <span class="zhxy-form-label" slot="label">内置功能资源</span>
        <div @click="!isEdit && openSourceDrawer(formData.nzgnzyid)">
          <el-input readonly class="zhxy-form-inline readonlyInput" v-model="formData.wlgnmc" placeholder="内置功能资源"
                    size="small"></el-input>
        </div>
      </el-form-item>
      <el-form-item v-if="!isFile && isInerRouter">
        <span class="zhxy-form-label" slot="label">打开方式</span>
        <el-select class="zhxy-form-inline" v-model="formData.nzgnzydkfs" placeholder="打开方式"
                   size="small">
          <el-option
            v-for="item in openOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">图标</span>
        <div @click="!isEdit && openIconDrawers(formData.tb)">
          <el-input readonly class="zhxy-form-inline readonlyInput" v-model="formData.tb" placeholder="图标"
                    size="small"></el-input>
        </div>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh" placeholder="排序号"
                  size="small"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash';

export default {
  props: {
    // 是否是显示状态
    showView: {
      type: Boolean,
      default: false
    },
    // 是否可编辑状态
    isEdit: {
      type: Boolean,
      default: true
    },
    // 是否展示编辑按钮
    isShowButton: {
      type: Boolean,
      default: true
    },
    // 菜单详情数据
    formData: {
      type: Object,
      default: () => ({
        cdmc: '',
        sjcd: '', // 上级菜单
        cdlx: '', // 菜单类型
        wlqqdz: '', // 外部连接地址
        wlgnmc: '', // 内置功能资源
        nzgnzydkfs: '', // 打开方式
        tb: '', // 图标
        pxh: '' // 排序号
      })
    }
  },
  data() {
    return {
      disabled: true,
      menuOptions: [
        {
          value: 1,
          label: '文件目录'
        },
        {
          value: 2,
          label: '内置资源'
        },
        {
          value: 3,
          label: '外部链接'
        }
      ],
      menuOption: [
        {
          value: 1,
          label: '文件目录'
        },
        {
          value: 2,
          label: '内置资源'
        },
        {
          value: 3,
          label: '外部链接'
        }
      ],
      // 菜单类型 options
      openOptions: [
        {
          value: 1,
          label: '内置打开'
        },
        {
          value: 2,
          label: '新标签页打开'
        }
      ],
      // form rules
      rules: {
        cdmc: [
          {
            required: true,
            message: '菜单名称不可为空',
            trigger: 'blur'
          }
        ],
        wlqqdz: [
          {
            required: true,
            message: '外部链接地址不可为空',
            trigger: 'blur'
          }
        ],
        cdlx: [
          {
            required: true,
            message: '菜单类型不能为空',
            trigger: ['change', 'blur']
          }
        ],
        wlgnmc: [
          {
            required: true,
            message: '内置功能资源不能为空',
            trigger: ['change', 'blur']
          }
        ]
      }
    };
  },
  computed: {
    // 文件类型:文件目录
    isFile() {
      return this.formData.cdlx === 1;
    },
    // 文件类型: 内置
    isInerRouter() {
      return this.formData.cdlx === 2;
    },
    // 文件类型: 外联
    isOutRouter() {
      return this.formData.cdlx === 3;
    }
  },
  watch: {
    showView: {
      handler(val) {
        if (val) {
          if (this.isShowButton) {
            this.disabled = true;
            this.menuOptions = cloneDeep(this.menuOption);
          } else {
            this.disabled = false;
            if (this.isFile) {
              this.menuOptions = this.menuOption.filter((x) => x.value === 1);
            } else {
              this.menuOptions = this.menuOption.filter((x) => x.value !== 1);
            }
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    buttonAction() {
      // 切换编辑状态
      if (!this.isEdit) {
        // 保存调用
        // eslint-disable-next-line consistent-return
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.$emit('menuUpdate');
          } else {
            return false;
          }
        });
      } else {
        this.$emit('update:isEdit', !this.isEdit);
      }
    },
    openSourceDrawer(val) {
      this.$emit('openSourceDrawer', val);
    },
    openIconDrawers(val) {
      this.$emit('openIconDrawers', val);
    }
  }
};
</script>
<style lang="scss">
.department-user {
  .el-input.is-disabled .el-input__inner {
    background-color: #FFFFFF;
    cursor: default;
  }

  .readonlyInput {
    .el-input__inner {
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss" scoped>
.department-user {
  .el-input.is-disabled .el-input__inner {
    background-color: #FFFFFF;
  }

  .zhxy-form-inline {
    width: 60%;
    min-width: 300px;
    margin-right: 0;
  }

  .zhxy-form.zhxy-form-search-part.form-status-edit {
    .el-form-item {
      margin-bottom: 20px !important;
    }
  }
}
</style>
