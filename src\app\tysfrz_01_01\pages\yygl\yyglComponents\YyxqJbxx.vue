<template>
  <div>
    <div class="dybzx-page-content">
      <div class="jksqgl-pageform-header">
        <span class="zhxy-form-label" slot="label">基本信息</span>
        <el-button type="primary" @click="edit()" size="small">编辑</el-button>
      </div>
    </div>
    <div style="display: flex;flex-wrap:wrap">
      <div v-for="(item,index) in editBaseData" :key="index" :style="`width:${item.isHalf ? '50%':'100%'}`">
        <div style="display: flex;flex-wrap:wrap">
          <span class="jksqgl-page-content-jbxx-left" :style="`width:${item.isHalf ? '30%':'15%'}`">{{
              item.label
            }}</span>
          <span :title="item.value" class="jksqgl-page-content-jbxx-right" :style="`width:${item.isHalf ? '70%':'35%'}`">{{
              item.value
            }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'YyxqJbxx',
  props: {
    editBaseData: {
      type: Array,
      default: () => ([])
    }
  },
  methods: {
    edit() {
      this.$emit('editForm');
    }
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    margin-top: 10px;
    padding: 0 10px;
    box-sizing: border-box;
  }
}

.jksqgl-page-content-jbxx-left {
  display: inline-block;
  height: 50px;
  text-align: center;
  border: 1px solid #ededed;
  background-color: #FFFFFF;
  line-height: 50px;
}

.jksqgl-page-content-jbxx-right {
  display: inline-block;
  height: 50px;
  padding-left: 10px;
  border: 1px solid #ededed;
  background-color: #FFFFFF;
  line-height: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
