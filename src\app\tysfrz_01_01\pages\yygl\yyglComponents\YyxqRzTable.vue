<template>
  <div>
    <div class="jksqgl-pageform-header">
      <span class="zhxy-form-label" slot="label">调用记录</span>
    </div>
    <div style="margin:20px;display: flex;align-items: center">
      <p style="padding-left: 20px" >用户ID/姓名</p>
      <el-input size="small"  v-model="tableDataName" placeholder="请输入姓名" style="width:240px;padding: 0px 20px"/>
      <el-button size="small" type="primary" @click="doFilter">查询</el-button>
    </div>
    <el-table :data="tableDataEnd" style="width:100%" border fit>
      <el-table-column label="序号" width="55">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号名称" width="120">
        <template slot-scope="scope">{{ scope.row.rzzh }}</template>
      </el-table-column>
      <el-table-column label="姓名" width="120">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column label="认证时间" width="240">
        <template slot-scope="scope">{{ scope.row.rzsj }}</template>
      </el-table-column>
      <el-table-column label="认证结果" width="100">
        <template slot-scope="scope">{{ scope.row.rzjg }}</template>
      </el-table-column>
      <el-table-column label="认证失败原因" width="240">
        <template slot-scope="scope">{{ scope.row.rzsbyy }}</template>
      </el-table-column>
      <el-table-column label="认证IP">
        <template slot-scope="scope">{{ scope.row.rzip }}</template>
      </el-table-column>
      <el-table-column label="认证名称">
        <template slot-scope="scope">{{ scope.row.rzmc }}</template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="currentPage" :page-sizes="[30, 50, 100, 500]" :page-size="pageSize"
                   :total="totalItems" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"/>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableDataBegin: [
        {
          id: '1',
          name: '李明明',
          rzzh: '20000001',
          rzsj: '2021-05-12 21:25:25',
          rzjg: '成功',
          rzsbyy: '此账号为恶意账号',
          rzip: '*************',
          rzmc: '校庆系统'
        },
        {
          id: '2',
          name: '张红',
          rzzh: '20000001',
          rzsj: '2021-05-12 21:25:25',
          rzjg: '成功',
          rzsbyy: '此账号为恶意账号',
          rzip: '*************',
          rzmc: '校庆系统'
        },
        {
          id: '3',
          name: '王小虎',
          rzzh: '20000001',
          rzsj: '2021-05-12 21:25:25',
          rzjg: '成功',
          rzsbyy: '此账号为恶意账号',
          rzip: '*************',
          rzmc: '校庆系统'
        },
        {
          id: '4',
          name: '王二虎',
          rzzh: '20000001',
          rzsj: '2021-05-12 21:25:25',
          rzjg: '成功',
          rzsbyy: '此账号为恶意账号',
          rzip: '*************',
          rzmc: '校庆系统'
        },
        {
          id: '5',
          name: '王大虎',
          rzzh: '20000001',
          rzsj: '2021-05-12 21:25:25',
          rzjg: '成功',
          rzsbyy: '此账号为恶意账号',
          rzip: '*************',
          rzmc: '校庆系统'
        }
      ],
      tableDataName: '',
      tableDataEnd: [],
      currentPage: 1,
      pageSize: 4,
      totalItems: 0,
      filterTableDataEnd: [],
      flag: false
    };
  },
  created() {
    this.totalItems = this.tableDataBegin.length;
    if (this.totalItems > this.pageSize) {
      for (let index = 0; index < this.pageSize; index++) {
        this.tableDataEnd.push(this.tableDataBegin[index]);
      }
    } else {
      this.tableDataEnd = this.tableDataBegin;
    }
  },
  methods: {
    // 前端搜索功能需要区分是否检索,因为对应的字段的索引不同
    // 用两个变量接收currentChangePage函数的参数
    doFilter() {
      if (this.tableDataName === '') {
        this.$message.warning('查询条件不能为空!');
        return;
      }
      this.tableDataEnd = [];
      // 每次手动将数据置空,因为会出现多次点击搜索情况
      this.filterTableDataEnd = [];
      this.tableDataBegin.forEach((value, index) => {
        if (value.name) {
          if (value.name.indexOf(this.tableDataName) >= 0) {
            this.filterTableDataEnd.push(value);
          }
        }
      });
      // 页面数据改变重新统计数据数量和当前页
      this.currentPage = 1;
      this.totalItems = this.filterTableDataEnd.length;
      // 渲染表格,根据值
      this.currentChangePage(this.filterTableDataEnd);
      // 页面初始化数据需要判断是否检索过
      this.flag = true;
    },
    // openData() {},
    handleSizeChange(val) {
      this.pageSize = val;
      /* this.handleCurrentChange(this.currentPage) */
      this.handleCurrentChange(1);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      // 需要判断是否检索
      if (!this.flag) {
        /* this.currentChangePage(this.tableDataEnd) */
        this.currentChangePage(this.tableDataBegin);
      } else {
        this.currentChangePage(this.filterTableDataEnd);
      }
    }, // 组件自带监控当前页码
    currentChangePage(list) {
      let from = (this.currentPage - 1) * this.pageSize;
      const to = this.currentPage * this.pageSize;
      this.tableDataEnd = [];
      for (; from < to; from++) {
        if (list[from]) {
          this.tableDataEnd.push(list[from]);
        }
      }
    }
  }
};
</script>

<style scoped>
.jksqgl-pageform-header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  background-color: #EAEAEA;
  border: 1px solid #ededed;
  width: 100%;
  height: 50px;
  padding: 0 10px;
  box-sizing: border-box;
}
</style>
