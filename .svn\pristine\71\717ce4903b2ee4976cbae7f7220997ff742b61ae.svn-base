<template>
  <div class="info-table">
    <el-table
      v-loading="loading"
      class="zhxy-table"
      :data="tableData"
      stripe
      border
      height="100%">
      <el-table-column prop="fwmc" label="服务名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="fwsm" label="服务说明" show-overflow-tooltip></el-table-column>
      <el-table-column prop="fwfl" label="服务分类" width="150">
        <template slot-scope="scope">
          <span>{{fwflFilter(scope.row.fwfl)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="pxh" align="center" label="排序号" width="100"></el-table-column>
      <el-table-column prop="fwzt" label="服务状态" width="100">
        <template slot-scope="scope">
          <span>{{ztFilter(scope.row.fwzt)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="cjr" label="创建人" width="130"></el-table-column>
      <el-table-column label="创建时间" width="130">
        <template slot-scope="scope">
          {{scope.row.cjsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column prop="bgr" label="变更人" width="130"></el-table-column>
      <el-table-column prop="bgsj" label="变更时间" width="130">
        <template slot-scope="scope">
          {{scope.row.bgsj | dateFilter}}
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="" label="操作" width="150">
        <template slot-scope="scope">
          <el-button size="small" @click="modify(scope.row)" type="text">编辑</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" @click="detail(scope.row)" type="text">流程配置</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" @click="deleteItem(scope.row)" type="text">删除</el-button>

        </template>
      </el-table-column>
    </el-table>
    <div class="pageIn">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="sizeList"
        layout="total, sizes, prev, pager, next, jumper"
        :total=total>
      </el-pagination>
    </div>
  </div>

</template>

<script>
import { dateFilter } from '@/utils/date-utils';

export default {
  filters: {
    dateFilter
  },
  props: {
    // table数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 总页数
    total: {
      type: Number,
      default: 0
    },
    // 状态list
    ztOptions: {
      type: Array,
      default: () => []
    },
    // 状态list
    fwflOptions: {
      type: Array,
      default: () => []
    },
    // 加载loading状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      sizeList: [30, 50, 100, 200, 500]
    };
  },
  computed: {
    // 状态数据解析
    ztFilter() {
      return (val) => {
        if (val && this.ztOptions) {
          for (let i = 0; i < this.ztOptions.length; i++) {
            const item = this.ztOptions[i];
            if (item.value === val) {
              return item.label;
            }
          }
          return '';
        }
        return '';
      };
    },
    // 服务分类数据解析
    fwflFilter() {
      return (val) => {
        if (val && this.fwflOptions) {
          for (let i = 0; i < this.fwflOptions.length; i++) {
            const item = this.fwflOptions[i];
            if (item.dmz === val) {
              return item.dmmc;
            }
          }
          return '';
        }
        return '';
      };
    }
  },
  methods: {
    /**
     * 流程配置button
     * @param val
     */
    detail(val) {
      this.$emit('detail', val);
    },
    /**
     * 编辑button
     * @param val
     */
    modify(val) {
      this.$emit('modify', val);
    },
    /**
     * 删除button
     * @param val
     */
    deleteItem(val) {
      this.$emit('deleteItem', val);
    },
    /**
     * pageSize button change event
     * @param val
     */
    handleSizeChange(val) {
      this.$emit('tableChangeSize', val);
    },
    /**
     * currentPage change event
     * @param val
     */
    handleCurrentChange(val) {
      this.$emit('tableChangePage', val);
    }
  }
};
</script>

<style lang="scss" scoped>
  .info-table {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto
  }
</style>
