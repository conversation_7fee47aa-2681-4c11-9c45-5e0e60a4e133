/*基本样式-定义常见元素边框及滚动条样式*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    box-sizing: border-box;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
    display: block;
}
ol, ul {
    list-style: none;
}
blockquote, q {
    quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}

/*::-webkit-scrollbar {*/
/*    width: 5px;*/
/*    height: 5px;*/
/*}*/

/*::-webkit-scrollbar-track-piece {*/
/*    background-color: rgba(0, 0, 0, 0.2);*/
/*    -webkit-border-radius: 6px;*/
/*}*/

/*::-webkit-scrollbar-thumb:vertical {*/
/*    height: 5px;*/
/*    background-color: rgba(125, 125, 125, 0.7);*/
/*    -webkit-border-radius: 6px;*/
/*}*/

/*::-webkit-scrollbar-thumb:horizontal {*/
/*    width: 5px;*/
/*    background-color: rgba(125, 125, 125, 0.7);*/
/*    -webkit-border-radius: 6px;*/
/*}*/

a{
    text-decoration:none;
}
a:link, a:visited, a:active {
    text-decoration: none;
}
:focus{
    outline: 0px;
}
