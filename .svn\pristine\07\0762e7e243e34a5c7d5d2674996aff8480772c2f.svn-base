<!--组织机构办理人组件-->
<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" @close="closeZzjg()" width="960px"
             lock-scroll
             close-on-press-escape>
    <!--    部门树-->
    <div class="el-transfer-panel" style="margin-right:20px">
      <p class="el-transfer-panel__header"
         style="background-color: rgb(238, 243, 253);color:#40537A;font-weight: 500">
        部门信息
      </p>
      <div class="el-transfer-panel__body" style="overflow: auto">
        <el-tree :data="bmtree" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
      </div>
    </div>
    <!--    待选人员-->
    <div class="el-transfer-panel">
      <el-table
        ref="dxryTable"
        :data="dxrytableData"
        :border="false"
        height="340px"
        :cell-style="nobottom"
        @selection-change="dxryCheck"
        style="width: 100%; overflow: auto; height: 340px;border:0px!important;">
        <el-table-column
          type="selection"
          width="45">
        </el-table-column>
        <el-table-column
          property="xm"
          :label="'待选人员（'+dxchecked.length+'/'+dxrytableData.length+'）'">
          <template slot-scope="scope">{{ scope.row.xm }}-{{ scope.row.yhid }}

          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--    按钮组-->
    <div class="el-transfer__buttons">
      <el-button icon="el-icon-arrow-left" size="mini" @click="moveLeft()"></el-button>
      <el-button icon="el-icon-arrow-right" size="mini" @click="moveRight()"></el-button>
    </div>
    <!--    已选人员-->
    <div class="el-transfer-panel" style="margin-right:20px">
      <el-table
        ref="yxryTable"
        :data="yxrytableData"
        :border="false"
        height="340px"
        :cell-style="nobottom"
        @selection-change="yxryCheck"
        style="width: 100%; overflow: auto; height: 340px;border:0px!important;">
        <el-table-column
          type="selection"
          width="45">
        </el-table-column>
        <el-table-column
          property="xm"
          :label="'已选人员（'+yxchecked.length+'/'+yxrytableData.length+'）'">
          <template slot-scope="scope">{{ scope.row.xm }}-{{ scope.row.yhid }}

          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitZzjg()">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'zzjg_dialog',
  data() {
    return {
      // 弹窗显示
      dialogVisible: false,
      dialogTitle: '组织机构',
      // 部门树
      bmtree: [],
      // 待选人员table
      dxrytableData: [],
      // 已选人员table
      yxrytableData: [],
      // 待选中选中人员
      dxchecked: [],
      // 已选中选中人员
      yxchecked: [],
      // 当前部门
      curentBm: '',
      // 部门树属性
      defaultProps: {
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      },
      // table动态样式
      nobottom: { 'border-bottom-width': '0px' }
    };
  },
  methods: {
    show() { // 显示方法
      this.dialogVisible = true;
    },
    hidden() { // 隐藏方法
      this.dialogVisible = false;
    },
    // 部门树节点点击事件
    handleNodeClick(data) {
      this.$emit('findYhListByBmm', data.bmm);
      this.curentBm = data.bmm;
    },

    // 生成待选人员
    geneDxry(data) {
      this.yxrytableData.forEach((item, ndex) => {
        data.splice(data.findIndex((v) => v.yhid === item.yhid), 1);
      });
      this.dxrytableData = data;
    },
    // 待选人员全选
    dxryCheck(val) {
      this.dxchecked = val;
    },
    // 已选人员全选
    yxryCheck(val) {
      this.yxchecked = val;
    },
    // 右侧移动
    moveRight() {
      this.dxchecked.forEach((item, index) => {
        if (this.yxrytableData.findIndex((v) => v.yhid === item.yhid) === -1) {
          this.yxrytableData.push(item);
          this.dxrytableData.splice(this.dxrytableData.findIndex((v) => v.yhid === item.yhid), 1);
        }
      });
      this.dxchecked = [];
    },
    // 左侧移动
    moveLeft() {
      this.yxchecked.forEach((item, index) => {
        this.yxrytableData.splice(this.yxrytableData.findIndex((v) => v.yhid === item.yhid), 1);
        if (this.dxrytableData.findIndex((v) => v.yhid === item.yhid) === -1 && item.bmm === this.curentBm) {
          this.dxrytableData.push(item);
        }
      });
      this.yxchecked = [];
    },
    // 提交
    submitZzjg() {
      this.$emit('submitZzjg', this.yxrytableData);
    },
    // 关闭
    closeZzjg() {
      this.dxrytableData = [];
      this.yxrytableData = [];
      this.dxchecked = [];
      this.yxchecked = [];
      this.bmtree = [];
      this.curentBm = '';
    }
  }
};
</script>

<style scoped>
  .el-transfer-panel {
    width: 240px;
  }

  .el-transfer-panel__body {
    height: 300px;
  }

</style>
