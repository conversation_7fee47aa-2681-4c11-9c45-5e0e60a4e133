<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">账号/姓名</span>
        <el-input
          class="zhxy-form-inline"
          v-model="listQuery.zhid"
          placeholder="账号/姓名"
          size="small">
        </el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">过期时间</span>
        <el-date-picker
          class="zhxy-form-inline"
          size="small"
          :style="`width: ${lableWidthSingle}`"
          v-model="listQuery.gqsj"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">密码强度</span>
        <el-select
          class="zhxy-form-inline"
          v-model="listQuery.mmqd"
          placeholder="密码强度"
          size="small">
          <el-option
            v-for="item in mmqdOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">数据来源</span>
        <el-select
          class="zhxy-form-inline"
          v-model="listQuery.zhly"
          placeholder="数据来源"
          size="small">
          <el-option
            v-for="item in zhlyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">活跃状态</span>
        <el-select
          class="zhxy-form-inline"
          v-model="listQuery.hyzt"
          placeholder="活跃状态"
          size="small">
          <el-option
            v-for="item in hyztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">状态</span>
        <el-select
          class="zhxy-form-inline"
          v-model="listQuery.zhzt"
          placeholder="状态"
          size="small">
          <el-option
            v-for="item in yhztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isShowLabel: false,
      // label 宽度
      lableWidth: '120px',
      // labelWidth
      lableWidthSingle: '450px',
      // search params
      listQuery: {
        zhid: '',
        gqsj: '',
        mmqd: '',
        zhly: '',
        hyzt: '',
        zhzt: ''
      },
      // 密码强度options
      mmqdOptions: [
        {
          label: '低',
          value: 1
        }, {
          label: '中',
          value: 2
        },
        {
          label: '高',
          value: 3
        },
        {
          label: '密码不符',
          value: 4
        }],
      // 用户状态 options
      yhztOptions: [
        {
          label: '停用',
          value: 2
        }, {
          label: '启用',
          value: 1
        }, {
          label: '锁定',
          value: 3
        }],
      // 账号来源 options
      zhlyOptions: [
        {
          label: '管理员创建',
          value: 1
        }, {
          label: '同步程序',
          value: 2
        }],
      // 活跃状态 options
      hyztOptions: [
        {
          label: '活跃',
          value: 1
        }, {
          label: '休眠',
          value: 2
        }]
    };
  },
  methods: {
    /**
     * 搜索查询
     */
    search() {
      this.$emit('search');
    },
    /**
     * 重置搜索条件
     */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });

      this.$emit('reset');
    }
  }
};
</script>

<style lang="scss" scoped>
.search-fold {
  color: $page-font-hover-color;
  display: inline-block;
  margin-left: 10px;
  margin-right: 5px;
  cursor: pointer
}
</style>
