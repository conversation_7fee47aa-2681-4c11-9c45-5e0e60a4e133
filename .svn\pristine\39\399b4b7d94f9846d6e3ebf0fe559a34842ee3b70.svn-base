/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp(`(\\s|^)${ cls }(\\s|$)`));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ` ${ cls}`;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp(`(\\s|^)${ cls }(\\s|$)`);
    ele.className = ele.className.replace(reg, ' ');
  }
}

/**
 * class切换
 * @param el
 * @param className
 * @returns {boolean}
 */
export const toggleClass = (el, className) => el.classList.toggle(className);
/**
 * 获取当前页面滚动位置
 * @param el
 * @returns {{x: (number), y: (number)}}
 */
export const getScrollPosition = (el = window) => ({
  x: el.pageXOffset !== undefined ? el.pageXOffset : el.scrollLeft,
  y: el.pageYOffset !== undefined ? el.pageYOffset : el.scrollTop
});

/**
 * 滚动到页面顶部
 */
export const scrollToTop = () => {
  const c = document.documentElement.scrollTop || document.body.scrollTop;
  if (c > 0) {
    window.requestAnimationFrame(scrollToTop);
    window.scrollTo(0, c - c / 8);
  }
};

/**
 * 指定元素是否在视口可见
 * @param el
 * @param partiallyVisible
 * @returns {*}
 */
export const elementIsVisibleInViewport = (el, partiallyVisible = false) => {
  const {
    top, left, bottom, right
  } = el.getBoundingClientRect();
  const { innerHeight, innerWidth } = window;
  return partiallyVisible ? ((top > 0 && top < innerHeight) || (bottom > 0 && bottom < innerHeight)) && ((left > 0 && left < innerWidth) || (right > 0 && right < innerWidth)) : top >= 0 && left >= 0 && bottom <= innerHeight && right <= innerWidth;
};

// Examples
// elementIsVisibleInViewport(el); // (不完全可见)
// elementIsVisibleInViewport(el, true); // (部分可见)
