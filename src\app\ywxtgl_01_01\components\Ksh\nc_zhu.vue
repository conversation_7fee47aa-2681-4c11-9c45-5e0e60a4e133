<template>
  <div ref="myChart" style="width: 100%;height: 100%;box-sizing: border-box;"></div>
</template>

<script>
// 引入基本模板
import { echarsMixins } from '@/app/ywxtgl_01_01/mixins/echars-resize';

const echarts = require('echarts/lib/echarts');
// 引入柱状图组件
require('echarts/lib/chart/bar');
// 引入提示框和title组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');

export default {
  mixins: [echarsMixins],
  props: {
    data: {
      type: Object
    },
    color: {
      type: String,
      default() {
        return '#3398DB';
      }
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    data(val) {
      this.drawLine();
    }

  },
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 实例存在则消除
      const chart = echarts.getInstanceByDom(this.$refs.myChart);
      if (chart) {
        chart.dispose();
      }

      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.myChart);
      this.echarsDom = myChart;

      const colors = ['#ff6666', '#f7bc16', '#00FFFF'];

      const option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '15%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis'
        },
        calculable: true,
        xAxis: [{
          type: 'category',
          axisLabel: {
            color: '#65aaf1',
            interval: 0,
            rotate: 0
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
              width: 1
            }
          },
          // data: ['***********', '***********', '***********', '***********', '***********', '***********', '***********',
          //   '***********', '***********', '************'
          // ]
          data: this.data.xData
        }],
        yAxis: [{

          nameTextStyle: {
            color: '#65aaf1',
            fontStyle: 'normal'
          },
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#65aaf1'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
              width: 1,
              type: 'solid'
            }
          },
          max: 100,
          name: '使用率%',
          type: 'value'
        }],
        series: [{
          name: '使用率',
          type: 'bar',
          barWidth: '50%',
          // data: [89, 75, 69, 65, 65, 59, 56, 52, 45, 30],
          data: this.data.yData,
          label: {
            show: true,
            position: 'top',
            formatter({ value }) {
              return `${value}%`;
            }
          },
          itemStyle: {
            normal: {
              color(params) {
                if (params.value >= 70) {
                  return '#ff6666';
                }
                if (params.value >= 60) {
                  return '#f7bc16';
                }

                return '#00FFFF';
              }
            }
          },
          markLine: {
            data: [{
              type: 'average',
              name: '平均值'
            }]
          }
        }]
      };

      myChart.setOption(option);
    }
  }
};
</script>

<style>
</style>
