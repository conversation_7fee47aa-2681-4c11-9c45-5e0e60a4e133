<template>
  <div class="jsgl-page-role-tree">
    <el-drawer
      :title="title"
      :visible.sync="visible"
      :direction="'rtl'"
      :before-close="handleClose">
      <div style="padding: 10px;height: 100%;">
        <el-select size="small" v-if="isPc" style="flex: 1;margin-right: 10px;margin-bottom: 20px;" v-model="sourceType" placeholder="请选择">
          <el-option
            v-for="item in sourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            @click.native="changeSzhjEvent(item.label)">
          </el-option>
        </el-select>
        <div style="padding-bottom: 20px" v-if="visiblezy">
          <el-button type="primary" size="small" @click="handledefine()">确定</el-button>
          <el-button type="" size="small" @click="hidetreeDialog()">取消</el-button>
        </div>
        <div style="padding-bottom: 20px" v-if="visiblebm">
          <el-button type="primary" size="small" @click="handledefinezy()">确定</el-button>
          <el-button type="" size="small" @click="hidetreeDialog()">取消</el-button>
        </div>
        <div style="height: calc(100% - 110px);overflow: auto">
          <div>
            <el-tree
              show-checkbox
              :default-checked-keys="xztreeZy"
              :data="treeZy"
              ref="wxtreeZy"
              node-key="gnzyid"
              :props="defaultZyProps"
              v-if="visiblezy">
            </el-tree>
          </div>
          <div>
            <el-tree
              show-checkbox
              :default-checked-keys="xztreeBm"
              :data="treeBm"
              ref="wxtreeBm"
              node-key="bmm"
              :props="defaultBmProps"
              v-if="visiblebm">
            </el-tree>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import {
  findCkeckedYyzy, findYyzylistAll, updateJsxzy, findBmlistAll, addYhjsmx
} from '../../../api/jsgl/jsgl';

export default {
  name: 'roleTreeDialog',
  props: {
    width: {
      type: String,
      default: '500px'
    },
    NodeClickJszy: {
      type: Function,
      default: null
    },
    title: {
      type: String,
      default: '选择用户111'
    },
    defaultZyProps: {
      type: Object,
      default: () => ({
        label: 'gnzymc',
        id: 'gnzyid',
        children: 'children'
      })
    },
    defaultBmProps: {
      type: Object,
      default: () => ({
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      })
    },
    nodeKey: {
      type: String,
      default: 'jsid'
    },
    treeRoleData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 资源tree结构
      treeZy: this.treeRoleData,
      xztreeZy: [],
      // 部门tree结构
      treeBm: [],
      xztreeBm: [],
      // 用户id
      xzyhid: '',
      // 角色id
      jsidZy: '',
      // 是否是PC端下拉框显示隐藏
      isPc: false,
      sourceType: '1',
      sourceOptions: [
        {
          value: '1',
          label: 'PC'
        },
        {
          value: '2',
          label: 'Mobile'
        }
      ],
      // dialog 显隐
      visible: false,
      // 资源树
      visiblezy: false,
      // 部门树
      visiblebm: false,
      // 部门用户
      departmentSearch: {
        userName: ''
      }
    };
  },
  methods: {
    // 新增角色资源
    handledefine() {
      const arr = [];
      const wxtreeZyarr = this.$refs.wxtreeZy.getCheckedNodes(true) || [];
      wxtreeZyarr.forEach((item) => {
        const itemparam = {
          jsid: this.jsidZy,
          gnzyid: item.gnzyid
        };
        arr.push(itemparam);
      });
      /**
       * 新增 角色资源 接口
       */
      const param = {
        gnzyarr: arr
      };
      updateJsxzy(param).then((res) => {
        this.$message.success('修改成功');
        const data = {
          jsid: this.jsidZy
        };
        // this.NodeClickJszy(data);
        this.$emit('NodeClickJszy', data);
        this.visible = false;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 新增 用户角色明细
    handledefinezy() {
      const arr = [];
      const wxtreeZyarr = this.$refs.wxtreeBm.getCheckedNodes(true) || [];
      wxtreeZyarr.forEach((item) => {
        const itemparam = {
          jsid: this.jsidZy,
          bmm: item.bmm,
          yhid: this.xzyhid
        };
        arr.push(itemparam);
      });
      /**
       * 新增 用户角色明细 接口
       */
      const param = {
        zymxarr: arr
      };
      addYhjsmx(param).then((res) => {
        this.$message.success('新增成功');
        this.visible = false;
        const paramdata = {
          jsid: this.jsidZy
        };
        this.$emit('NodeClickJsyh', paramdata);
      }).finally(() => {
        this.loading = false;
      });
    },
    // 角色资源显示
    showDialog() {
      this.visible = true;
      this.visiblezy = true;
      this.visiblebm = false;
      const param = {
        jsid: this.jsidZy,
        szhj: this.sourceType
      };
        /**
         * 获取 角色资源 接口
         */
      findYyzylistAll(param).then((res) => {
        this.treeZy = [];
        this.xztreeZy = [];
        this.treeZy = JSON.parse(res.data.content).gnzyList;
        const arr = JSON.parse(res.data.content).jszyList || [];
        arr.forEach((item) => {
          this.xztreeZy.push(item.gnzyid);
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    // 获取角色用户明细
    showBmDialog(val) {
      this.visible = true;
      this.visiblezy = false;
      this.visiblebm = true;
      this.xzyhid = val.yhid;
      const param = {
        jsid: this.jsidZy,
        yhid: val.yhid
      };
      /**
       * 获取 角色用户明细 接口
       */
      findBmlistAll(param).then((res) => {
        this.treeBm = [];
        this.xztreeBm = [];
        this.treeBm = JSON.parse(res.data.content).bmList;
        const arr = JSON.parse(res.data.content).jsxbmList || [];
        arr.forEach((item) => {
          this.xztreeBm.push(item.bmm);
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    changeSzhjEvent(szhjid) {
      this.showDialog();
    },
    hideDialog() {
      this.visible = false;
    },
    hidetreeDialog() {
      this.visible = false;
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done();
        })
        .catch((_) => {
        });
    }
  }
};
</script>
<style lang="scss">
  .el-drawer__header {
    margin-bottom: 0;
  }
  .jsgl-page-role-tree{
    .el-drawer__body{
      overflow: hidden;
    }
  }
</style>
