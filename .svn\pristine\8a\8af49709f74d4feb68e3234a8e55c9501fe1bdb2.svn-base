@media screen and (max-width: 1300px) {
  .zhxy-dialog-view {
    width: 500px;
    transition: all .5s;
  }
}

@media screen and (min-width: 1300px) {
  .zhxy-dialog-view {
    width: 800px;
    transition: all .5s;
  }
}
/*tree 结构 默认最小行高;auto,换行*/
.zhxy-tree-nomal{
  .el-tree-node{
    white-space: normal;
  }
  .el-tree-node__content{
    height: auto;
    min-height: 26px;
  }
}
/*页面title样式*/
.zhxy-page-title{
  position: relative;
  height: $title-height;
  line-height: $title-height;
  font-size: $title-font-size;
  font-weight: $title-font-weight;
  padding-left: $title-margin-left;

  &:before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    width: 4px;
    height: 20px;
    top: 50%;
    margin-top: -10px;
    background-color: $page-font-hover-color;
    /*border-radius: 2px;*/
  }
}
.zhxy-form{
    margin: $page-content-margin 0;
    &-label {
      color: #515a6e;
      font-weight: bold
    }

    &-inline {
      width: 150px;
      margin-right: 20px;
    }
}
.zhxy-form-search-part{
  .el-form-item{
    margin-bottom: 5px !important;
  }
  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
}
.zhxy-table.el-table thead{
    th,tr{
      background-color: $page-bg-color;
      font-weight: bold;
      color: $page-label-color;
    }
}

.el-table--enable-row-transition .el-table__body td{
  padding: $form-td-padding 0 !important;
}

.el-tabs__header{
  margin-bottom: 10px;
}
.el-dialog__header{
  border-bottom: 1px solid $page-bg-color;
}
.el-dialog__body{
  padding: $page-content-padding;
}
.el-dialog__footer{
  text-align: center;
  border-top: 1px solid $page-bg-color;
}

