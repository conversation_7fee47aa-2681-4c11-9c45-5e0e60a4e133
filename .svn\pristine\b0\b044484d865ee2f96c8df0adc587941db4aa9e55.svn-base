<template>
    <div class="dialog-content">
      <!-- 'formInline' 为此el-form 自定义的对象  -->
      <el-form :model="FormInline" :rules="rule" ref="dmxForm" label-width="140px" label-position="right">
        <el-form-item v-if="yjjsVisible" label="代码分类标识" prop="dmflbs">
          <el-input disabled :value="dmflbs" v-model="FormInline.dmflbs" placeholder="代码分类标识" size="small"/>
        </el-form-item>
        <el-form-item v-if="yjjsVisible" label="上级代码id" prop="sjdmid">
          <el-input disabled :value="sjdmid" v-model="FormInline.sjdmid" placeholder="代码分类标识" size="small"/>
        </el-form-item>
        <el-form-item label="代码名称" prop="dmmc" >
          <el-input v-model="FormInline.dmmc" placeholder="代码名称"></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="代码值" prop="dmz">
          <el-input v-model="FormInline.dmz" placeholder="代码值" ></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="排序号" prop="pxh">
          <el-input v-model="FormInline.pxh" placeholder="排序号"></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="扩展字段1" prop="kzzd1">
          <el-input v-model="FormInline.kzzd1" placeholder="扩展字段1"></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="扩展字段2" prop="kzzd2">
          <el-input v-model="FormInline.kzzd2" placeholder="扩展字段2"></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item label="扩展字段3" prop="kzzd3">
          <el-input v-model="FormInline.kzzd3" placeholder="扩展字段3"></el-input>   <!-- v-model 存放属性-->
        </el-form-item>
        <el-form-item prop="sfky" label="是否可用">
          <el-radio-group v-model="FormInline.sfky">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
</template>

<script>
import {
  adddmx
} from '../../../api/dmb/dmb';

export default {
  name: 'add_bm_dialog',
  props: {
    FormInline: {
      type: Object,
      default: () => ({
        dmflbs: '',
        dmid: '',
        dmmc: '',
        dmz: '',
        sjdmid: '',
        pxh: '',
        kzzd1: '',
        kzzd2: '',
        kzzd3: '',
        sfky: ''
      })
    }
  },
  data() {
    return {
      yjjsFjsid: '',
      yjjsVisible: false,
      dialogVisible: false,
      dmflbs: '',
      labelPosition: 'right',
      rule: {
        dmmc: [
          { required: true, message: '代码名称不可为空', trigger: 'blur' },
          {
            max: 100,
            message: '代码名称长度不能多于100位'
          }
        ],
        dmz: [
          { required: true, message: '代码值不可为空', trigger: 'blur' },
          {
            max: 50,
            message: '代码值长度不能多于50位'
          }
        ],
        kzzd1: [
          {
            max: 20,
            message: '扩展字段1长度不能多于20位'
          }
        ],
        kzzd2: [
          {
            max: 20,
            message: '扩展字段2长度不能多于20位'
          }
        ],
        kzzd3: [
          {
            max: 20,
            message: '扩展字段3长度不能多于20位'
          }
        ],
        pxh: [
          {
            max: 10,
            message: '排序号长度不能多于10位'
          }
        ]
      }
    };
  },
  methods: {
    show() { // 显示方法
      this.dialogVisible = true;
    },
    hidden() { // 隐藏方法
      this.dialogVisible = false;
    },
    hiddenInner(formName) {
      this.innerVisible = false;
      this.$refs[formName].clearValidate();
      this.$refs[formName].resetFields();
    }
  }
};
</script>

<style scoped>
/*@import "../../../../styles/app0301.scss";*/
.sbj {
  margin-top: 15px;
}
</style>
