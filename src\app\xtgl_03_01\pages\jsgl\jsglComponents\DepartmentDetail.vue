<template>
  <div class="department-user" ref="element">
    <el-button type="primary" size="small" @click="buttonAction"> {{isEdit ? '编辑' : '保存'}}</el-button>
    <el-form :disabled="isEdit" label-width="150px" :model="formData" :rules="rules" class="zhxy-form zhxy-form-search-part" :class="isEdit ? '' : 'form-status-edit'">
      <el-form-item>
        <span class="zhxy-form-label" slot="label">角色ID</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.jsid" placeholder="角色ID" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="jsmc">
        <span slot="label" class="zhxy-form-label">角色名称</span>
        <el-input class="zhxy-form-inline" v-model="formData.jsmc" placeholder="角色名称" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="jslx">
        <span class="zhxy-form-label" slot="label">角色类型</span>
        <el-radio-group v-model="formData.jslx">
          <el-radio :label="1">固化角色</el-radio>
          <el-radio :label="2">自建角色</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="pxh">
        <span class="zhxy-form-label" slot="label">排序号</span>
        <el-input class="zhxy-form-inline" v-model="formData.pxh" placeholder="排序号" size="small"></el-input>
      </el-form-item>
      <el-form-item prop="gzlgwtbfs">
        <span class="zhxy-form-label" slot="label">工作流岗位同步方式</span>
        <el-input class="zhxy-form-inline" v-model="formData.gzlgwtbfs" placeholder="工作流岗位同步方式" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjr" placeholder="创建人" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">创建时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.cjsj" placeholder="创建时间" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更人</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgr" placeholder="变更人" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <span class="zhxy-form-label" slot="label">变更时间</span>
        <el-input disabled class="zhxy-form-inline" v-model="formData.bgsj" placeholder="变更时间" size="small"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { findJsxyhlist, update } from '../../../api/jsgl/jsgl';

export default {
  name: 'DepartmentDetail',
  data() {
    return {
      // 是否可编辑
      isEdit: true,
      // 部门详情表单数据
      formData: {
        jsid: '',
        jsmc: '',
        jslx: '',
        fjsid: '',
        pxh: '',
        gzlgwtbfs: '',
        cjr: '',
        cjsj: '',
        bgr: '',
        bgsj: ''
      },
      rules: {
        jsmc: [{
          required: true,
          trigger: 'blur',
          message: '请输入角色名称'
        },
        {
          max: 32,
          message: '角色名称长度不能多于32位'
        }
        ],
        jslx: [{
          required: true,
          trigger: 'blur',
          message: '请选择角色类型'
        }
        ],
        pxh: [
          {
            max: 5,
            message: '排序号长度不能多于5位'
          }
        ],
        gzlgwtbfs: [
          {
            max: 20,
            message: '工作流岗位同步方式长度不能多于20位'
          }
        ]
      }
    };
  },
  methods: {
    // 删除角色用户
    deleteJsyh() {
      this.visible = true;
      // 调用
      this.NodeClickBm();
    },
    // 切换编辑状态
    buttonAction() {
      this.isEdit = !this.isEdit;
      if (this.isEdit === true) {
        const param = {
          gzlgwtbfs: this.formData.gzlgwtbfs,
          jsid: this.formData.jsid,
          jslx: this.formData.jslx,
          jsmc: this.formData.jsmc,
          pxh: this.formData.pxh
        };
        /**
         * 修改 角色 接口
         */
        update(param).then((res) => {
          this.$message.success('修改成功');
        }).finally(() => {
          this.loading = false;
        });
      }
    }
  }
};
</script>
<style lang="scss">
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
      cursor: default;
    }
  }
</style>
<style lang="scss" scoped>
  .department-user {
    .el-input.is-disabled .el-input__inner{
      background-color: #FFFFFF;
    }
    .zhxy-form-inline {
      width: 60%;
      min-width: 500px;
      margin-right: 0;
    }
    .zhxy-form.zhxy-form-search-part.form-status-edit {
      .el-form-item {
        margin-bottom: 20px !important;
      }
    }
  }
</style>
