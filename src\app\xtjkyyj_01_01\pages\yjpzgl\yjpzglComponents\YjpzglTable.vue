<template>
  <div>
    <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
      <el-table-column type=index label="序号" width="60" ></el-table-column>
      <el-table-column prop="yjlbmc" label="预警类别" width="180" ></el-table-column>
      <el-table-column prop="yjflmc" label="预警分类" width="180"></el-table-column>
      <el-table-column prop="yjbs" label="预警标识" width="180"></el-table-column>
      <el-table-column prop="yjmc" label="预警名称" width="180" ></el-table-column>
      <el-table-column prop="yjsm" label="预警说明" min-width="180" ></el-table-column>
      <el-table-column prop="yjjbm" label="预警级别码" width="180" v-if="false"></el-table-column>
      <el-table-column prop="yjjbmc" label="预警级别" width="180" ></el-table-column>
      <el-table-column prop="" label="预警开关" width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.yjkg === 1">开&nbsp;(<span style="color: #1313e8;"><a @click="changeKg(scope.row)">关闭</a></span>)</span>
          <span v-else-if="scope.row.yjkg === 0">关&nbsp;(<span style="color: #1313e8;"><a @click="changeKg(scope.row)">开启</a></span>)</span>
        </template>
      </el-table-column>
      <el-table-column prop="txrynum" label="提醒人员" width="180" >
        <template slot-scope="scope">
          <div>
            <el-button size="small" @click="showTxry(scope.row)" type="text">{{scope.row.txrynum}}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="yzkzzd01" label="阈值01" width="180" ></el-table-column>
      <el-table-column prop="yzkzzd02" label="阈值02" width="180" ></el-table-column>
      <el-table-column fixed="right" prop="" label="操作" width="140">
        <template slot-scope="scope">
          <el-button size="small" @click="modifyYjpz(scope.row)" type="text">修改</el-button>
          <i style="color: #e8eaec;"> | </i>
          <el-button size="small" type="text" @click="delYjpz(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 提醒人员信息 -->
    <el-dialog
      width="45%"
      ref="dialogEdit"
      customClass="zhxy-dialog-view"
      :visible.sync="txryVisible"
      title="提醒人员信息"
      :close-on-click-modal="false" @close="txryVisible=false;" append-to-body>
      <el-form :inline="true" class="demo-form-inline">
        <el-button type="primary" icon="el-icon-plus" size="small" style="margin:0 0 5px 5px;float: right;" @click="addTxry">添加人员</el-button>
      </el-form>
      <div style="height: 400px">
        <el-table class="zhxy-table" :data="txrylist" stripe border tooltip-effect="dark" style="height: 360px;">
          <el-table-column align="center" type="index" label="序号" width="80"></el-table-column>
          <el-table-column prop="sjjlid"  label="数据记录ID" width="200" v-if="false"></el-table-column>
          <el-table-column prop="yjbs"  label="预警标识" width="200" v-if="false"></el-table-column>
          <el-table-column prop="zgh" label="职工号" width="150"></el-table-column>
          <el-table-column prop="yhxm"  label="用户姓名" width="150" ></el-table-column>
          <el-table-column prop="bm"  label="部门" min-width="180" ></el-table-column>
          <el-table-column fixed="right" prop="" label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="delTxry(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="text-align: right;margin: 5px 0">
        <el-button type="" size="small" @click="txryVisible=false;">取消</el-button>
      </div>
    </el-dialog>

    <!-- 新增提醒人员 -->
    <el-dialog
      width="45%"
      ref="dialogAdd"
      customClass="zhxy-dialog-view"
      :visible.sync="addTxryVisble"
      title="新增提醒人员信息"
      :close-on-click-modal="false" @close="addTxryVisble=false;" append-to-body>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="职工号/姓名">
          <el-input placeholder="请输入职工号/姓名" v-model="zghxm"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDxtxryList">查询</el-button>
        </el-form-item>
      </el-form>
      <div style="height: 400px">
        <el-table class="zhxy-table" :data="dxtxryList" stripe border tooltip-effect="dark"  style="height: 360px;" >
          <el-table-column align="center" type="index" label="序号" width="80"></el-table-column>
          <el-table-column prop="zgh" label="职工号" width="150"></el-table-column>
          <el-table-column prop="yhxm"  label="用户姓名" width="150" ></el-table-column>
          <el-table-column prop="bm"  label="部门" width="180" ></el-table-column>
          <el-table-column fixed="right" prop="" label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="saveAddTxry(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
      <div style="text-align: right;margin: 5px 0">
        <el-button type="" size="small" @click="addTxryVisble=false;">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  findTxryList, findDxtxryList, addTxryxx, deleteTxryxx
} from '@/app/xtjkyyj_01_01/api/yjpzgl/yjpzgl.js';

export default {
  name: 'YjpzflTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      yjbs: '', // 预警标识
      txrylist: [], // 提醒人员的list
      txryVisible: false, // 提醒人员弹窗是否可见
      addTxryVisble: false, // 新增提醒人员弹窗是否可见
      dxtxryList: [], // 待选提醒人员的list
      zghxm: '', // 输入的职工号姓名
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      currentPage: 1 // 初始页
    };
  },
  methods: {
    modifyYjpz(val) {
      this.$emit('modifyYjpz', val);
    },
    delYjpz(val) {
      this.$emit('delYjpz', val);
    },
    showTxry(val) {
      this.yjbs = val.yjbs;
      this.getTxryyzxx();
      this.txryVisible = true;
    },
    getTxryyzxx() {
      const param = {
        yjbs: this.yjbs
      };
      findTxryList(param).then((res) => {
        if (res.code === 200) {
          this.txrylist = res.data.content;
        }
      });
    },
    addTxry() {
      this.getDxtxryList();
      this.addTxryVisble = true;
    },
    getDxtxryList(params) {
      const param = {
        pageSize: 30,
        page: 1,
        yjbs: this.yjbs,
        zghxm: this.zghxm
      };
      if (params) {
        Object.assign(param, params);
      }
      findDxtxryList(param).then((res) => {
        if (res.code === 200) {
          this.dxtxryList = res.data.content;
        }
      });
    },
    saveAddTxry(val) {
      const param = {
        yjbs: this.yjbs,
        zgh: val.zgh
      };
      addTxryxx(param).then((res) => {
        if (res.code === 200) {
          this.$parent.search();
          this.getTxryyzxx();
          this.$message.success('添加成功');
          this.addTxryVisble = false;
        }
      });
    },
    delTxry(val) {
      this.$confirm('确定执行删除操作 ？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            const param = {
              yjbs: val.yjbs,
              zgh: val.zgh
            };
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            deleteTxryxx(param).then((res) => {
              if (res.code === 200) {
                this.$message.success('删除成功');
                this.$parent.search();
                this.getTxryyzxx();
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      });
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: 30,
        page: 1,
        yjbs: this.yjbs,
        zghxm: this.zghxm
      };
      this.getDxtxryList(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: 30,
        page: 1,
        yjbs: this.yjbs,
        zghxm: this.zghxm
      };
      this.getDxtxryList(param);
    },
    changeKg(val) {
      this.$emit('changeKg', val);
    }
  }
};
</script>

<style scoped>

</style>
