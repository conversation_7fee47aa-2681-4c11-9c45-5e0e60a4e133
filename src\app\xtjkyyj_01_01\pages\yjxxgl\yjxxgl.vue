<template>
  <div class="yjxxgl-content">
    <div class="yjxxgl-content-main">
      <v-title name="预警信息管理"></v-title>
      <yjxxgl-search ref="searchElement" @search="search"></yjxxgl-search>
      <div class="table">
        <div class="table-content">
          <yjxxgl-table :scroller-height="scrollerHeight" :table-data="tableData" @modifySfhl="modifySfhl" @del="del"></yjxxgl-table>
        </div>
        <div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[30, 50, 100, 200]"
            :page-size="pageSize"
            layout="total,sizes,  prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import {
  findYjxxList, updateSfhl, del
} from '@/app/xtjkyyj_01_01/api/yjxxgl/yjxxgl.js';
import YjxxglSearch from './yjxxglComponents/YjxxglSearch';
import YjxxglTable from './yjxxglComponents/YjxxglTable';

const defaultYjxx = {
  yjbs: '',
  yjlb: '',
  yjfl: '',
  yjmc: '',
  yjms: '',
  yjjbm: '',
  yjjbmc: '',
  yjfs: '',
  scsj: '',
  gzfscjsj: ''
};

export default {
  name: 'yjxxgl',
  components: {
    VTitle,
    YjxxglSearch,
    YjxxglTable
  },
  data() {
    return {
      scrollerHeight: 0,
      currentPage: 1, // 初始页
      yjxx: { ...defaultYjxx },
      tableData: [
        {
          yjbs: '',
          yjlb: '',
          yjfl: '',
          yjmc: '',
          yjms: '',
          yjjbm: '',
          yjjbmc: '',
          yjfs: '',
          scsj: '',
          gzfscjsj: ''
        }
      ], // 列表数据集合
      pageTotal: 0,
      page: 1,
      pageSize: 30,
      options: {
        type: Array,
        default: () => []
      }
    };
  },
  mounted() {
    // table 尺寸 reize
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
  },
  // 页面销毁
  beforeDestroy() {
    // 移除 resize
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 监听页面尺寸改变table size
    handleResize() {
      const height = this.$refs.searchElement.$el.offsetHeight;
      this.scrollerHeight = window.innerHeight - height - 220;
    },
    search(params) {
      const param = {
        pageSize: 30,
        page: 1,
        yjlb: null,
        yjfl: null,
        yjjbm: null,
        sfhl: null,
        scsjq: null,
        scsjz: null,
        yjmc: null
      };
      if (params) {
        Object.assign(param, params);
      }
      findYjxxList(param).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.content;
          this.pageTotal = res.data.pageInfo.total;
        }
      });
    },
    reset() {
      this.listQuery.yjlb = null;
      this.listQuery.yjfl = null;
      this.listQuery.yjjbm = null;
      this.listQuery.sfhl = null;
      this.listQuery.scsjq = null;
      this.listQuery.scsjz = null;
      this.listQuery.yjmc = null;
      this.search();
    },
    modifySfhl(row) {
      const params = {
        sjjlid: row.sjjlid,
        yjbs: row.yjbs,
        sfhl: row.sfhl
      };
      this.$confirm('确认修改处理状态？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            updateSfhl(params).then((res) => {
              if (res.code === 200) {
                this.$message.success('修改成功');
                this.search();
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    del(row) {
      const sjjlid = row.sjjlid || ''; // sjjlid
      this.$confirm('确认删除？', {
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            del({ sjjlid }).then((res) => {
              if (res.code === 200) {
                const index = this.tableData.findIndex((item) => item.sjjlid === sjjlid);
                if (index > -1) {
                  this.tableData.splice(index, 1);
                }
                this.$message.success('删除成功');
                this.search();
              }
            }).finally(() => {
              instance.confirmButtonLoading = false;
              done();
            });
          } else {
            done();
          }
        }
      })
        .then(() => {
        })
        .catch(() => {
        });
    },
    /**
     * 每页显示条数改变事件
     * @param val
     */
    handleSizeChange(val) {
      this.pageSize = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        yjlb: this.$refs.searchElement.listQuery.yjlb,
        yjfl: this.$refs.searchElement.listQuery.yjfl,
        yjjbm: this.$refs.searchElement.listQuery.yjjbm,
        sfhl: this.$refs.searchElement.listQuery.sfhl,
        scsjq: this.$refs.searchElement.listQuery.scsjq,
        scsjz: this.$refs.searchElement.listQuery.scsjz,
        yjmc: this.$refs.searchElement.listQuery.yjmc
      };
      this.search(param);
    },
    /**
     * 当前页数改变事件
     * @param val
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      const param = {
        pageSize: this.pageSize, // 页码条数
        page: this.page, // 页码
        yjlb: this.$refs.searchElement.listQuery.yjlb,
        yjfl: this.$refs.searchElement.listQuery.yjfl,
        yjjbm: this.$refs.searchElement.listQuery.yjjbm,
        sfhl: this.$refs.searchElement.listQuery.sfhl,
        scsjq: this.$refs.searchElement.listQuery.scsjq,
        scsjz: this.$refs.searchElement.listQuery.scsjz,
        yjmc: this.$refs.searchElement.listQuery.yjmc
      };
      this.search(param);
    }
  }
};
</script>
<style lang="scss" scoped>
  .yjxxgl-content {
    &-main{
      background-color: #ffffff;
      padding: $page-content-padding;
    }
    .table {
      &-content {
        margin-top: $page-content-margin;
      }
    }
    .dialog-footer{
      text-align: center;
      display: flex;
      justify-content: flex-end;
    }
    .qran{
      margin-top: -3.1rem;
      margin-right: 1rem;
      float: right;
    }
  }
</style>
