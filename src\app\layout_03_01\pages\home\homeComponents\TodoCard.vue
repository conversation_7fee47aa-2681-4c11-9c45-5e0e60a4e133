<template>
  <div class="event-list">
    <v-title name="办理事项"></v-title>
    <el-tabs v-model="activeCurrent" @tab-click="handleClick">
      <el-tab-pane name="first">
        <span slot="label"> 待办事项(1)</span>
        <div class="tab-content">
          <div v-for="item in todoList" :key="item.id">
            <el-tooltip
              popper-class="todo-popper"
              :open-delay="20"
              placement="top"
              :content="item.content"
              :disabled="item.isShowTip"
            >
              <p @mouseenter="infoOver($event,item)" @mouseout="infoOut($event,item)" class="tab-content-info">{{item.content}}</p>
            </el-tooltip>

            <p class="tab-content-date">{{item.time}}</p>
          </div>

        </div>
      </el-tab-pane>
      <el-tab-pane name="second">
        <span slot="label"> 办理历史(12)</span>
        办理历史
      </el-tab-pane>
      <el-tab-pane name="third">
        <span slot="label"> 我发起的(2)</span>
        我发起的
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import VTitle from '@/components/title/VTitle';

export default {
  name: 'TodoCard',
  components: {
    VTitle
  },
  props: {
    activeName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 待办tab value
      activeCurrent: 'first',
      // 是否展示 超出提示 tip
      isShowTip: false,
      // 数据
      todoList: [
        {
          id: '1',
          content: '系统管理员在2020-11-07发起出差申请',
          time: '2021-01-27 08:47:26',
          isShowTip: false
        },
        {
          id: '2',
          content: '系统管理员在2020-11-07发起出差申请',
          time: '2021-01-27 08:47:26',
          isShowTip: false
        },
        {
          id: '3',
          content: '系统管理员在2020-11-07发起出差申请',
          time: '2021-01-27 08:47:26',
          isShowTip: false
        }
      ]
    };
  },
  /*  computed: {
    activeCurrent: {
      get() {
        return this.activeName;
      },
      set(val) {
        this.$emit('update:activeName', val);
      }
    }
  }, */
  methods: {
    /**
     * 切换tab click event
     * @param tab
     * @param event
     */
    handleClick(tab, event) {
      this.$emit('handleClick', tab);
    },
    /**
     * tooltip 显示隐藏判断
     * @param event
     */
    infoOver(event, item) {
      item.isShowTip = event.toElement.offsetWidth >= event.toElement.scrollWidth;
      // console.log(event.toElement.offsetWidth, event.toElement.scrollWidth, this.isShowTip);
    },
    /**
     * tooltip 显示移除效果设置
     * @param event
     */
    infoOut(event, item) {
      item.isShowTip = true;
    }
  }
};
</script>
<style>
  .todo-popper.el-tooltip__popper.is-dark{
    position: fixed;
  }
</style>
<style lang="scss" scoped>
.event-list{
  background-color: #FFFFFF;
  .tab-content {
    display: flex;
    flex-direction: column;
    &>div{
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #f2efed;
    }

    &-date {
      flex-shrink: 0;
      flex-wrap: nowrap;
      color: #9c9c9c;
      font-size: 14px;
    }

    &-info {
      flex: 1;
      flex-wrap: nowrap;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      color: #2d8cf0;
      cursor: pointer;
      position: relative;
      &:hover{
        color: #000000;
      }
    }
  }
}
</style>
