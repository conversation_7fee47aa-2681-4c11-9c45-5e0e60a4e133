<template>
  <div class="app-container">
    <el-container style="height: 100%;" direction="vertical">
      <el-header>
        <Navbar
          ref="navbar"
          @roleChange="roleChange"
          :collapse.sync="isCollpase"
          :active-name.sync="activeName"
          :navbar-list="navbarList"
          @changeTab="changeNavbar"/>
      </el-header>
      <el-container>

        <el-aside class="app-container-aside" :width="isCollpase?'64px':'220px'">
          <el-menu
            ref="menu"
            background-color="#191a23"
            text-color="#999"
            active-text-color="#FFFFFF"
            :default-active="menuActive"
            :collapse="isCollpase"
            :unique-opened="false"
            :collapse-transition="false"
            style="width: 100%;height: 100%;"
            :class="[isCollpase?'sliderTure':'']"
            mode="vertical"
          >
            <div>
              <sidebar-item
                @rollback="rollback"
                :collapse="isCollpase"
                v-for="route in secondArr"
                :key="route.id"
                :item="route" />
            </div>
          </el-menu>
        </el-aside>

        <el-main class="app-container-main" height="100%">
          <tag-view></tag-view>
          <transition name="fade-transform" mode="out-in">
            <div style="padding-top: 48px;height: 100%;overflow: auto">
              <keep-alive :include="getKeepList">
                <router-view v-if="isReloadComponent" style="height: 100%;min-height: 500px;min-width: 860px;overflow: auto"></router-view>
              </keep-alive>
            </div>
          </transition>
        </el-main>
      </el-container>
      <!--      <el-footer style="height: 30px" class="app-container-footer">Footer</el-footer>-->
    </el-container>
  </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex';
import { sessionData } from '@/utils/local-utils';
import { setKeepAliveMixins } from '@/app/layout_03_01/pages/mixin';
import Navbar from './IndexNavbar';
import SidebarItem from './SidebarItem';
import TagView from './TagView';
import { getMenuList } from '../../../api/user/user';

export default {
  name: 'Inreach',
  components: {
    Navbar,
    SidebarItem,
    TagView
  },
  mixins: [setKeepAliveMixins],
  data() {
    return {
      menuActive: this.$route.path,
      // 是否展开collpase slider
      isCollpase: false,
      // 一级菜单 选中
      activeName: '/',
      // 一级菜单list
      navbarList: [],
      // 数据菜单全部
      second: [],
      // 数据菜单 current
      secondArr: [],
      // menu all
      list: [],
      // 屏幕宽度
      windowWidth: document.documentElement.clientWidth // 实时屏幕宽度
    };
  },
  computed: {
    ...mapState('layout_03_01/user', ['menuList', 'userInfo', 'roleId'])
  },

  watch: {
    // 检测屏幕宽度 变更sliderBar收缩展开
    windowWidth(val) {
      this.isCollpase = val < 800;
    },
    // 检测router 变更 Menu菜单activeName
    $route: {
      handler(to) {
        if (to) {
          const active = to.meta.parentId;
          this.setSliderArr(active);
          this.activeName = active;
          this.menuActive = this.$route.fullPath;
          // console.log(this.activeName, this.navbarList);
        }
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  created() {
    // menu菜单初始化
    this.menuListInit();
  },
  mounted() {
    // 监听 屏幕宽度
    this.handleResize();
    window.addEventListener('resize', this.handleResize);
  },
  methods: {
    ...mapMutations('layout_03_01/user', ['setMenuList', 'setLoginRole', 'setResourceList', 'setHasLogin']), // vuex setMenuList(设置菜单) setLoginRole(设置角色)
    /**
     * 角色切换
     * @param val
     */
    roleChange(val) {
      // console.log(val);
      this.setHasLogin(true);
      // todo 资源 菜单切换
      this.setLoginRole(val);
      // 1. 菜单切换清空storage
      this.setMenuList([]);
      // 2.菜单重置
      this.menuListInit();
      // 3. 资源清空storage
      this.setResourceList([]);
      sessionData('clean', 'roleList');
      // 4. 刷新加载路由资源
      this.$router.go(0);
    },
    /**
       * 数据处理
       */
    menuListInit() {
      if (!this.menuList || this.menuList.length <= 0) {
        const params = {
          jsid: this.roleId,
          yhid: this.userInfo?.info?.userId || ''
        };
        getMenuList(params).then((response) => {
          this.setMenuList(response.data.content);
          this.handleMenuList(response.data.content);
        });
      } else {
        this.handleMenuList(this.menuList);
      }
    },
    /**
       * 处理菜单列表数据
       * @param response
       */
    handleMenuList(response) {
      const arr = [];
      const origin = response || [];
      this.second = [];
      this.list = origin;
      this.list.forEach((item) => {
        arr.push(item);
        if (item.children && item.children.length > 0) {
          this.second.push([...item.children]);
        }
      });
      this.navbarList = arr;
      this.$nextTick(() => {
        this.$refs.navbar.handleMounted();
        this.setDefaultPage();
      });
    },
    /**
       * 设置加载menu列表后默认进入的页面
       */
    setDefaultPage() {
      let currentActive = this.navbarList[0] ? this.navbarList[0].id : '';
      this.navbarList.forEach((item) => {
        if (this.$route.meta.parentId === item.id) {
          currentActive = item.id;
        }
      });
      setTimeout(() => {
        this.activeName = currentActive;
        this.setSliderArr(this.activeName);
      }, 20);
    },
    /**
       * 一级和二级联动
       * @param val
       */
    setSliderArr(val) {
      const index = this.list.findIndex((x) => x.id === val);
      if (index > -1) {
        this.secondArr = this.second[index];
      }
    },
    /**
       * 屏幕宽度检测
       */
    handleResize() {
      window.fullWidth = document.documentElement.clientWidth;
      this.windowWidth = window.fullWidth; // 宽
    },
    /**
       * menutab change
       * @param tab
       */
    changeNavbar(tab) {
      this.setSliderArr(tab.name);
    },
    rollback() {
      this.menuActive = '';
      setTimeout(() => {
        this.menuActive = this.$route.path;
        console.log('this.menuActive', this.menuActive);
      }, 20);
    }
  }
};
</script>

<style lang="scss" scoped="scoped">
  .app-container {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: #FFFFFF;

    .el-container {
      overflow: auto;
    }

    &-aside {
      transition: all .5s;
      background-color: #191a23;
      box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
      z-index: 2;
    }

    &-main {
      padding: 0 20px;
      position: relative;
      background-color: #f8f8f9;
      overflow: hidden;
    }

    .el-header {
      padding: 0;
      background-image: linear-gradient(90deg, rgb(29, 66, 171), rgb(33, 115, 220), rgb(30, 147, 255));
      color: #333;
      line-height: 60px;
    }

    &-footer {
      text-align: center;
      background-color: #b3b2b2;
    }
    /* fade-transform */
    .fade-transform-leave-active,
    .fade-transform-enter-active {
      transition: all .5s;
    }

    .fade-transform-enter {
      opacity: 0;
      transform: translateX(-30px);
    }

    .fade-transform-leave-to {
      opacity: 0;
      transform: translateX(30px);
    }
  }
</style>
<style lang="scss">
  .app-container {
    .sliderTure {
      .el-submenu__icon-arrow {
        display: none;
      }

      .el-submenu__title span {
        display: none;
      }
    }

    &-aside {
      .el-menu {
        overflow-x: hidden;
        border-right-color: transparent;
        overflow-y: auto;
        background: rgba(34, 116, 253, 0.5) !important;
        background-size: 100% auto;
        position: relative;
        &:after {
          content: '';
          display: block;
          position: absolute;
          width: 100%;
          height: 140px;
          background: url("../../../../layout_03_01/assets/slide-bg.jpg") no-repeat bottom right;
          background-size: cover;
          bottom: 0;
          left: 0;
          z-index: -1;
          opacity: 0.5;
          //box-shadow: inset 30px 0px 100px 50px #6495ed, inset -31px 0px 100px 51px #6495ed;  // 关键***
        }
      }

      .el-submenu__title:focus, .el-submenu__title {
        &:hover, &:focus {
          color: #FFFFFF !important;

          i {
            color: #FFFFFF;
          }
        }
      }

      .submenu-title-noDropdown {
        //background-color: #11131a !important;
        background-color: transparent !important;
      }

      .submenu-title-noDropdown.el-menu-item:focus, .submenu-title-noDropdown.el-menu-item:hover {
        color: #FFFFFF !important;

        i {
          color: #FFFFFF !important;
        }
      }

      .el-menu-item.is-active {
        color: #ffffff !important;
        background-color: #2d8cf0 !important;

        &:hover, &:focus {
          color: #ffffff !important;

          i {
            color: #ffffff !important;
          }
        }
      }
    }
  }

</style>
