<template>
  <div class="info-table">
    <el-table
      ref="multipleTable"
      class="zhxy-table"
      :data="tableData"
      stripe
      border
      height="100%"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" label="" width="50"></el-table-column>
      <el-table-column prop="jsmc" label="岗位名称"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    // table数据
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 选中selection list
      multipleSelection: [],
      list: {
        1: '起草者',
        2: '起草单位固定岗位类型',
        3: '起草单位任意岗位',
        4: '起草单位任意人员',
        5: '本单位固定岗位类型',
        6: '本单位任意岗位',
        7: '本单位任意人员',
        8: '任意单位固定岗位类型',
        9: '任意单位任意岗位',
        10: '任意单位任意人员',
        11: '',
        12: '指定单位固定岗位',
        13: '指定单位固定人员',
        14: '指定单位任意岗位',
        15: '指定单位任意人员'
      }
    };
  },
  methods: {
    /**
     * 默认勾选row设置
     * @param rows
     */
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    /**
     * selection change 事件
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    selectHandleType() {

    }
  }
};
</script>

<style lang="scss" scoped>
  .info-table {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    height: 400px;
    .cell-row{
      height: 42px;
      line-height: 42px;
    }
    .button-text{
      color: $page-font-hover-color;
      cursor: pointer;
    }
  }
</style>
