<template>
  <div class="http-error-page">
    <div class="http-error-page-container">
      <div class="http-error-page-container-image">
        <img class="pic-404-parent" src="@/assets/404_images/404.png" alt="404">
      </div>
      <div class="http-error-page-container-description">
        <div class="http-error-page-container-description-title">404</div>
        <div class="http-error-page-container-description-info">抱歉，您访问页面出错</div>
        <el-button @click="()=>{$router.push('/')}" type="primary">返回首页</el-button>
      </div>
    </div>
  </div>

</template>

<script>

export default {
  name: 'Page404',
  computed: {
    message() {
      return 'The webmaster said that you can not enter this page...';
    }
  }
};
</script>

<style lang="scss" scoped>
  .http-error-page{
    width: 100%;
    height: 100%;
    &-container {
      display: flex;
      height: 100%;
      justify-content: center;
      align-items: center;
      &>div{
        flex: 1;
      }

      &-image {
        img{
          width: 100%;
        }
      }

      &-description {
        &-title{
          margin-bottom: 24px;
          color: #515a6e;
          font-weight: 600;
          font-size: 72px;
          line-height: 72px;
          animation-name: slideUp;
          animation-duration: 0.5s;
          animation-fill-mode: forwards;
        }
        &-info{
          margin-bottom: 16px;
          color: #808695;
          font-size: 20px;
          line-height: 28px;
          animation-name: slideUp;
          animation-duration: 1s;
          animation-fill-mode: forwards;
        }
      }
    }
  }
  @keyframes slideUp {
    0% {
      transform: translateY(60px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
</style>
