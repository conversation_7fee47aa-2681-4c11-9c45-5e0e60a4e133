<template>
  <div class="dialog-content">
    <el-form ref="rwxxForm" :model="FormData" :rules="rules" label-width="130px" label-position="right">
      <el-form-item label="主机编号" prop="zjbh" style="display: none">
        <el-input :disabled="!zjbhVisible" v-model="FormData.zjbh" placeholder="必填" size="small"/>
      </el-form-item>

      <el-form-item prop="zjlx" label="主机类型" style="margin-bottom: 10px;">
        <el-radio-group v-model="FormData.zjlx">
          <el-radio label="1">管理服务器</el-radio>
          <el-radio label="2">文件服务器</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="主机IP" prop="zjip">
        <el-input v-model="FormData.zjip" placeholder="必填" maxlength="30" size="small"/>
      </el-form-item>
      <el-form-item label="主机用途：" prop="zjyt">
        <el-input v-model="FormData.zjyt" maxlength="200" size="small"/>
      </el-form-item>
      <el-form-item label="操作系统" prop="czxt">
        <el-input v-model="FormData.czxt" maxlength="100" size="small"/>
      </el-form-item>
      <el-form-item label="用户名" prop="yhm">
        <el-input v-model="FormData.yhm" maxlength="100" size="small"/>
      </el-form-item>
      <el-form-item label="密码" prop="mm">
        <el-input v-model="FormData.mm" maxlength="64" size="small"/>
      </el-form-item>

      <el-form-item prop="kyzt" label="可用状态" style="margin-bottom: 10px;">
        <el-radio-group v-model="FormData.kyzt">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="使用业务系统" prop="syywxt">
        <el-input v-model="FormData.syywxt" placeholder="必填"  maxlength="50" size="small"/>
      </el-form-item>
      <el-form-item label="负责人" prop="fzr">
        <el-input v-model="FormData.fzr" maxlength="50" size="small"/>
      </el-form-item>

    </el-form>

  </div>

</template>

<script>

export default {
  name: 'kettlerwglEdit',
  components: {
  },
  props: {
    FormData: {
      type: Object,
      default: () => ({
        zjbh: '',
        zjlx: '',
        zjip: '',
        zjyt: '',
        czxt: '',
        yhm: '',
        mm: '',
        kyzt: '',
        syywxt: '',
        fzr: ''
      })
    },
    zjbhVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        zjlx: [{
          required: true,
          trigger: 'blur',
          message: '请选择主机类型'
        }],
        zjip: [{
          required: true,
          trigger: 'blur',
          message: '请选择主机IP'
        }],
        kyzt: [{
          required: true,
          trigger: 'blur',
          message: '请选择可用状态'
        }],
        syywxt: [{
          required: true,
          trigger: 'blur',
          message: '请选择使用业务系统'
        }]
      }
    };
  },
  mounted() {
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped>
  .dialog-content {
    max-height: 500px;
    overflow: auto;
    padding: 0 20px;
  }
  .jbmc{
    width: 80%;
  }
  .zq{
    width: 150px;
  }
  .jbmcxz{
    width: 50px;
    display: inline-block;
    margin-left: 25px;
    color: #409EFF;
    cursor: pointer;
  }
  .jbmcqk{
    width: 50px;
    color: #409EFF;
    cursor: pointer;
  }
  .zhxy-form-inline{
    width: 100%;
  }
</style>
