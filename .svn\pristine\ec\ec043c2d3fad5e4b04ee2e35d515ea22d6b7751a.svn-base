import { createAPI } from '@/utils/request.js';

const BASE_URL = '';
// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/tysfrz_01_01/tysfrzQzxx`;
/**
 * 群组树列表 tree ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getGroupTree = (data) => createAPI(`${APP_PRE}/findList`, 'get', data);
/**
 * 群组树列表 新增 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const addGroupTree = (data) => createAPI(`${APP_PRE}/add`, 'post', data);
/**
 * 编辑群组数据 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const updateGroupBase = (data) => createAPI(`${APP_PRE}/update`, 'post', data);
/**
 * 群组查看详情 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getGroupBase = (data) => createAPI(`${APP_PRE}/findOne`, 'get', data);
/**
 * 群组删除接口 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const deleteGroupBase = (data) => createAPI(`${APP_PRE}/delete`, 'post', data);
/**
 * 获取群组下用户数据table ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getGroupUser = (data) => createAPI(`${APP_PRE}/findQzyhList`, 'get', data);

/**
 * 删除群组下用户数据 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const deleteGroupUser = (data) => createAPI(`${APP_PRE}/deleteZhqz`, 'post', data);
/**
 * 获取新增群组用户列表树 替换成 账号管理 的搜索 zhzt:1
 * @param data
 * @returns {AxiosPromise}
 */
// export const getGroupUserTree = (data) => createAPI(`${APP_PRE}/addGroupUser`, 'get', data);
/**
 * 保存 群组下用户 新增修改编辑数据 ok {zhid:[1,3,2],qzid:1}
 * @param data
 * @returns {AxiosPromise}
 */
export const updateGroupUserTree = (data) => createAPI(`${APP_PRE}/addZhqz`, 'post', data);
/**
 * 查询应用资源树 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getSourceTree = (data) => createAPI(`${APP_PRE}/findQzyylist`, 'get', data);
/**
 * 查询应用资源树 all ok
 * @param data
 * @returns {AxiosPromise}
 */
export const getSourceTreeAll = (data) => createAPI(`${APP_PRE}/findYylist`, 'get', data);
/**
 * 保存更新可授权的应用资源 ok
 * @param data
 * @returns {AxiosPromise}
 */
export const updateSourceTree = (data) => createAPI(`${APP_PRE}/updateYysq`, 'post', data);
