<template>
  <el-dialog
    :width="width"
    :title="title"
    :visible.sync="visible"
    custom-class="choose-user-dialog"
    append-to-body>
    <div class="origin-user-part">
      <div class="origin-user-part-left">
        <div class="content-top">
          <div class="zhxy-form zhxy-form-search-part" style="align-items: center;margin: 0">
            <el-form label-width="120" inline :model="departmentSearch" ref="searchForm">
              <el-form-item style="align-items: center">
                <el-input
                  class="zhxy-form-inline" v-model="departmentSearch.name"
                  style="width: 548px;margin: 0"
                  placeholder="请输入用户姓名或ID进行快速查找"
                  size="small">
                  <el-button slot="append">查询</el-button>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="content-main">
          <div class="content-main-left zhxy-tree-nomal">

            <el-tree
              :props="defaultProps"
              highlight-current
              :node-key="nodeKey"
              :data="treeData"
              ref="tree">
               <span slot-scope="{ node, data }">
                <i v-if="!node.isLeaf"
                   :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                {{data[defaultProps.label]}}
              </span>
            </el-tree>
          </div>
          <div class="content-main-right">
            <el-table
              :data="tableData"
              stripe
              border
              class="zhxy-table"
              height="calc(100% - 26px)"
              header-row-class-name="origin-table-header-middle"
              row-key="id"
              ref="treeTable"
              @selection-change="handleSelectionChange">
              <el-table-column
                reserve-selection
                align="center"
                type="selection"
                label=""
                width="52">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="xm"
                label="可选用户"
                width="100">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="zh"
                label="ID"
                width="120">
              </el-table-column>
            </el-table>
            <el-pagination
              style="text-align: center"
              small
              layout="prev, pager, next"
              @current-change="handleCurrentChange"
              :total="50">
            </el-pagination>
          </div>
        </div>

      </div>
      <div class="origin-usr-part-right">
        <el-table :data="multipleSelection" stripe border class="zhxy-table" height="calc(100% - 26px)"
                  header-row-class-name="origin-table-header">
          <el-table-column prop="xm" label="已选用户" width="260"></el-table-column>
          <el-table-column align="center" prop="" label="" width="60">
            <template slot-scope="scope">
              <el-button icon="el-icon-delete" circle size="small" type=""
                         @click="deletItem(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="text-align: center"
          small
          layout="prev, pager, next"
          :total="50"/>

      </div>
    </div>
    <div slot="footer">
      <el-button size="small" type="primary" @click="addBm()">确 定</el-button>
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ChooseUserDialog',
  props: {
    width: {
      type: String,
      default: '900px'
    },
    title: {
      type: String,
      default: '选择用户'
    },
    defaultProps: {
      type: Object,
      default: () => ({
        label: 'bmmc',
        id: 'bmm',
        children: 'children'
      })
    },
    nodeKey: {
      type: String,
      default: 'bmm'
    },
    treeData: {
      type: Array,
      default: () => []
    }

  },
  data() {
    return {
      // dialog 显隐
      visible: false,
      // 部门用户
      departmentSearch: {
        userName: ''
      },
      // 用户table
      tableData: [
        {
          id: '1',
          xm: '第三方师傅',
          zh: '12345678909'
        },
        {
          id: '2',
          xm: '第三方师傅',
          zh: '12345678909'
        },
        {
          id: '3',
          xm: '第三方师傅',
          zh: '12345678909'
        },
        {
          id: '4',
          xm: '第三方师傅',
          zh: '12345678909'
        }
      ],
      multipleSelection: []
    };
  },
  methods: {
    showDialog() {
      this.visible = true;
    },
    hideDialog() {
      this.visible = false;
    },
    handleSelectionChange(val) {
      console.log(val);
      this.multipleSelection = val;
    },
    deletItem(row) {
      const i = this.multipleSelection.findIndex((x) => x.id === row.id);
      // this.multipleSelection.splice(i, 1);
      this.$refs.treeTable.toggleRowSelection(row, false);
      console.log(this.multipleSelection);
    },
    handleCurrentChange(val) {
      console.log(val);
      console.log(this.multipleSelection);
      if (val === 2) {
        this.tableData = [
          {
            id: '21',
            xm: '第三方师傅',
            zh: '12345678909'
          },
          {
            id: '22',
            xm: '第三方师傅',
            zh: '12345678909'
          },
          {
            id: '23',
            xm: '第三方师傅',
            zh: '12345678909'
          }
        ];
      } else {
        this.tableData = [
          {
            id: '1',
            xm: '第三方师傅',
            zh: '12345678909'
          },
          {
            id: '2',
            xm: '第三方师傅',
            zh: '12345678909'
          },
          {
            id: '3',
            xm: '第三方师傅',
            zh: '12345678909'
          },
          {
            id: '4',
            xm: '第三方师傅',
            zh: '12345678909'
          }
        ];
      }
    }
  }
};
</script>

<style lang="scss">
  .choose-user-dialog {
    .el-dialog__body {
      padding: 10px 10px 0;
    }

    .el-dialog__footer {
      padding: 10px;
    }
    tr.origin-table-header-middle{
      th{
        background-color: #ffffff !important;
        padding: 5px;
      }
    }
    tr.origin-table-header{
      th{
        padding: 4px;
      }
    }
  }
</style>
<style lang="scss" scoped>
  .choose-user-dialog {
    .origin-user-part {
      height: 500px;
      display: flex;
      justify-content: space-between;
      overflow: auto;

      &-left {
        display: flex;
        flex-direction: column
      }
    }

    .content-main {
      display: flex;
      flex: 1;
      overflow: auto;

      &-left {
        margin-right: 10px;
        width: 265px;
        flex-shrink: 0;
        height: 100%;
        overflow: auto
      }
    }
  }
</style>
