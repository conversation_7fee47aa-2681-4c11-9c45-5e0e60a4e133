import { createAPI } from '@/utils/request.js';

const BASE_URL = '';
// 请求地址前缀拼接
const APP_PRE = `${BASE_URL}/tysfrz_01_01/tysfrzYysq`;
/**
 * 群组信息更改/保存
 * @param data
 * @returns {AxiosPromise}
 */
export const updateYyGoupTree = (data) => createAPI(`${APP_PRE}/add`, 'post', data);
/**
 * 获取用户信息新增数据的弹窗内容
 * @param data
 * @returns {AxiosPromise}
 */
export const getYyUserAddTable = (data) => createAPI(`${APP_PRE}/findPageList`, 'get', data);
/**
 * 用户新增保存
 * @param data
 * @returns {AxiosPromise}
 */
export const updateYyUserAddTable = (data) => createAPI(`${APP_PRE}/add`, 'post', data);

export const deleteYyUserTable = (data) => createAPI(`${APP_PRE}/delete`, 'post', data);
