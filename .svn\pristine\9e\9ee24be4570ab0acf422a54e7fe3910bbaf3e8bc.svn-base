<template>
  <div class="header-card">
    <div class="header-card-item" :style="`background-color:${data.color} `">
      <img width="40" :src="data.img" alt="">
      <div>
        <p class="header-card-item-num">
          <number-grow :value="data.num" :time="0.5"></number-grow>
        </p>
        <span class="header-card-item-des">{{data.des}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import NumberGrow from '../../components/NumberGrow';

export default {
  name: 'HomeCard',
  components: {
    NumberGrow
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  }
};
</script>

<style lang="scss" scoped>
  .header-card{
    width: 25%;
    padding: 5px 15px;
    &:nth-child(1){
      padding-left: 0;
    }
    &:nth-last-child(1){
      padding-right: 0;
    }
    &-item{
      display: flex;
      background-color: #4398f0;
      height: 80px;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      border-radius: 5px;
      cursor: pointer;
      img{
        margin-right: 10px;
      }
      &-num{
        font-size: 30px;
        font-weight: 500;
      }
      &-des{
        font-size: 14px;
      }
    }
  }
</style>
