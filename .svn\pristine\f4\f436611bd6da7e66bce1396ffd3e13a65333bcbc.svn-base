<template>
  <div class="dybzx-page-content">
    <div class="jksqgl-pageform-header">
      <span class="zhxy-form-label">统计信息</span>
    </div>
    <div class="yyxq-rzrz-tjxi">
      <div v-for="(item,id) in logInfo" :key="id" class="tjxx-content">
        <div class="tjxx-content-header">{{ item.label }}</div>
        <div class="tjxx-content-content">
          <div class="tjxx-content-content-box">
            <p class="tjxx-content-content-number font-color-blue">{{ item.sumcount }}</p>
            <p class="">总计</p>
          </div>
          <div class="tjxx-content-content-box">
            <p class="tjxx-content-content-number font-color-orange">{{ item.successcount }}</p>
            <p class="">成功</p>
          </div>
          <div class="tjxx-content-content-box">
            <p class="tjxx-content-content-number font-color-red">{{ item.failcount }}</p>
            <p class="">失败</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    logInfo: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {};
  }
};

</script>

<style lang="scss" scoped>
.dybzx-page-content {
  display: flex;
  flex-wrap: wrap;

  .jksqgl-pageform-header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    background-color: #EAEAEA;
    border: 1px solid #ededed;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    box-sizing: border-box;
  }

  .yyxq-rzrz-tjxi {
    width: 100%;
    padding: 2*$page-content-padding;
    display: flex;
    justify-content: space-between;

    .tjxx-content {
      width: 30%;
      background-color: #FFFFFF;
      border: #ededed 1px solid;
      .tjxx-content-header{
        background-color: #ededed;
        height: 50px;
        color: #3a8ee6;
        line-height: 50px;
        padding-left: 50px;
        font-size: 18px;
        font-weight: bold;
        box-sizing: border-box;
      }
      .tjxx-content-content{
        display: flex;
        justify-content: space-around;
        font-size: 18px;
        padding: 60px 0;
        .tjxx-content-content-box{
          text-align: center;
        }
        .tjxx-content-content-number{
          font-size: 32px;
        }
        .font-color-red{
          color: red;
        }
        .font-color-orange{
          color: darkorange;
        }
        .font-color-blue{
          color: #2d8cf0;
        }
      }

      .tjxx-page-content-first {
        width: 100%;
        height: 350px;

        .tjxx-page-content-first-title {
          background-color: #ededed;
          height: 50px;
          color: #3a8ee6;
          line-height: 50px;
          padding: 0px 50px;
          box-sizing: border-box;
        }

        .tjxx-page-content-first-mid {
          display: flex;
          justify-content: space-between;
          height: 200px;
          padding: 50px 50px 0px;
          text-align: center;
          box-sizing: border-box;

          .tjxx-page-content-first-mid-left {
            .tjxx-page-content-first-number {
              color: #2d8cf0;
            }

            .tjxx-page-content-first- written {
            }
          }

          .tjxx-page-content-first-mid-centre {
            .tjxx-page-content-first-number-centre {
              color: darkorange;
            }
          }

          .tjxx-page-content-first-mid-right {
            .tjxx-page-content-first-number-right {
              color: red;
            }
          }
        }

        .tjxx-page-content-first-last {
          background-color: #FFFFFF;
          width: 400px;
          height: 40px;
          display: flex;
          justify-content: center;
          line-height: 40px;
          border: #ededed 1px solid;
          border-top: none;
          box-sizing: border-box;

          .tjxx-page-content-first-last-title {
            color: #515a6e;
          }

          .tjxx-page-content-first-last-number {
            color: green;
          }
        }
      }
    }
  }
}

.jksqgl-pageform-header {
  height: 50px;
  padding: 0 20px;
}

.tjxx-page-content-tyjl {
  display: flex;
  margin-top: 20px;
  box-sizing: border-box;

  .demonstration {
    padding: 0px 20px;
  }
}

.tjxx-page-content-tyjl-anniu {
  padding-left: 400px;
}
</style>
