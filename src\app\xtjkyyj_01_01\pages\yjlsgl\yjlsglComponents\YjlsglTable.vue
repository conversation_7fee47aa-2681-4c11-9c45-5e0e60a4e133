<template>
  <div>
    <el-table class="zhxy-table" :data="tableData" stripe border :height="scrollerHeight">
      <el-table-column prop="yjbs" label="预警标识" width="180" v-if="false"></el-table-column>
      <el-table-column type=index label="序号" width="60" ></el-table-column>
      <el-table-column prop="yjlb" label="预警类别" width="180" ></el-table-column>
      <el-table-column prop="yjfl" label="预警分类" width="180"></el-table-column>
      <el-table-column prop="yjmc" label="预警名称" width="180" ></el-table-column>
      <el-table-column prop="yjms" label="预警描述" min-width="180" ></el-table-column>
      <el-table-column prop="yjjbm" label="预警级别码" width="180" v-if="false"></el-table-column>
      <el-table-column prop="yjjbmc" label="预警级别" width="180" ></el-table-column>
      <el-table-column prop="yjfs" label="预警方式" width="180" ></el-table-column>
      <el-table-column prop="scsj" label="生成时间" width="180">
        <template slot-scope="scope">
          <div v-if="scope.row.scsj != '' && scope.row.scsj != null">
            {{dayjs(scope.row.scsj).format('YYYY-MM-DD HH:mm:ss')}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="gzfscjsj" label="故障发生采集时间" width="160">
        <template slot-scope="scope">
          <div v-if="scope.row.gzfscjsj != '' && scope.row.gzfscjsj != null">
            {{dayjs(scope.row.gzfscjsj).format('YYYY-MM-DD HH:mm:ss')}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="gzhfcjsj" label="故障恢复采集时间" width="160">
        <template slot-scope="scope">
          <div v-if="scope.row.gzhfcjsj != '' && scope.row.gzhfcjsj != null">
            {{dayjs(scope.row.gzhfcjsj).format('YYYY-MM-DD HH:mm:ss')}}
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'YjlsglTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    scrollerHeight: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dayjs
    };
  },
  methods: {

  }
};
</script>

<style scoped>

</style>
