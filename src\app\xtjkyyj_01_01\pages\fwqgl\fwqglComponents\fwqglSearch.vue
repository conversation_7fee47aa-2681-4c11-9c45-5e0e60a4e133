<template>
  <div class="zhxy-form zhxy-form-search-part" ref="element">
    <el-form :label-width="lableWidth" inline :model="listQuery" ref="searchForm">

      <el-form-item>
        <span class="zhxy-form-label" slot="label">主机IP</span>
        <el-input class="zhxy-form-inline" v-model="listQuery.zjip" placeholder="主机IP" size="small"></el-input>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">主机类型</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.zjlx" placeholder="主机类型" size="small">
          <el-option
            v-for="item in zjlxOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <span class="zhxy-form-label" slot="label">可用状态</span>
        <el-select class="zhxy-form-inline" v-model="listQuery.kyzt" placeholder="可用状态" size="small">
          <el-option
            v-for="item in kyztOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search()" size="small">查询
        </el-button>
        <el-button type="" @click="reset()" size="small">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

</template>

<script>
export default {
  name: 'CjrwgzSearch',
  data() {
    return {
      // label 宽度
      lableWidth: '100px',
      // search params
      listQuery: {
        zjip: null,
        zjlx: null,
        kyzt: null
      },
      zjlxOptions: [{
        label: '全部',
        value: ''
      }, {
        label: '管理服务器',
        value: 1
      }, {
        label: '文件服务器',
        value: 2
      }],

      kyztOptions: [{
        label: '全部',
        value: ''
      }, {
        label: '正常',
        value: '1'
      }, {
        label: '异常',
        value: '0'
      }]

    };
  },
  mounted() {
    this.listQuery.zjlx = '';
    this.listQuery.kyzt = '';
  },
  methods: {
    /**
       * 搜索查询
       */
    search() {
      const param = {
        zjip: this.listQuery.zjip,
        zjlx: this.listQuery.zjlx,
        kyzt: this.listQuery.kyzt
      };
      this.$emit('search', param);
    },
    /**
       * 重置搜索条件
       */
    reset() {
      Object.keys(this.listQuery)
        .forEach((item) => {
          this.listQuery[item] = '';
        });
      this.search();
    }
  }
};
</script>

<style lang="scss" scoped>
  .title {
    display: flex;
    justify-content: space-between;
  }

  .search-fold {
    color: $page-font-hover-color;
    display: inline-block;
    margin-left: 10px;
    margin-right: 5px;
    cursor: pointer
  }
</style>
