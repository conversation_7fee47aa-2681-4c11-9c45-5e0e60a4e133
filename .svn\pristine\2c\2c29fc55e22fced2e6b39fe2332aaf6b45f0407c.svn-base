<template>
  <section>
    <v-title name="基础数据"></v-title>
    <div class="header">
      <template v-for="item in cardList">
        <home-card :data="item" :key="item.name"></home-card>
      </template>
    </div>
  </section>
</template>

<script>
import VTitle from '@/components/title/VTitle';
import HomeCard from './HomeCard';

export default {
  name: 'DataPart',
  components: {
    HomeCard,
    VTitle
  },
  data() {
    return {
      cardList: []
    };
  },
  created() {
    // 数据加载
    setTimeout(() => {
      this.cardList = [
        {
          num: 30,
          des: '数据总数',
          img: require('../../../assets/work-wait.png'),
          color: '#8fc99a'
        },
        {
          num: 78,
          des: '数据总数',
          img: require('../../../assets/work-wait.png'),
          color: ''
        },
        {
          num: 60,
          des: '数据总数',
          img: require('../../../assets/work-wait.png'),
          color: '#8870b4'
        },
        {
          num: 10,
          des: '数据总数',
          img: require('../../../assets/work-wait.png'),
          color: '#f4a074'
        }

      ];
    });
  }
};
</script>

<style scoped>
.header{
  display: flex;
}
</style>
